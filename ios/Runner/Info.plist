<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>${APP_DISPLAY_NAME}</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>vagustimpro</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.895301848976-hvnlegm5pao65c4i9nkmin2gncb66ivg</string>
				<string>com.googleusercontent.apps.895301848976-4hgmfrj5hfbacm7r3mkjevblakb6j93i</string>
				<string>com.googleusercontent.apps.895301848976-51a041c4bk0kk95e1706eojtoqkospn3</string>
				<string>com.googleusercontent.apps.895301848976-s1hg65lcvm0l5k36fmrpa0j5mapa5d2c</string>
				<string>com.googleusercontent.apps.895301848976-difmq7pusid7b1d4cfisgluni6tec7qr</string>
				<string>com.googleusercontent.apps.895301848976-eb25piq2dnfjfel5ktafh6gbk2b9lrfd</string>
				<string>com.googleusercontent.apps.895301848976-g3j2clvf4mu9089kou481aphd87nrk9m</string>
				<string>com.googleusercontent.apps.895301848976-0os1htclum0hfgi5vcr2u6souu2hegb8</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FirebaseDynamicLinksCustomDomains</key>
	<array>
		<string>https://vagustimpro.page.link</string>
	</array>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>googlegmail</string>
		<string>x-dispatch</string>
		<string>readdle-spark</string>
		<string>airmail</string>
		<string>ms-outlook</string>
		<string>ymail</string>
		<string>fastmail</string>
		<string>superhuman</string>
		<string>protonmail</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>The app uses bluetooth to find, connect and transfer data between Vagustim devices</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>The app uses bluetooth to find, connect and transfer data between Vagustim devices</string>
	<key>NSCameraUsageDescription</key>
	<string>To measure heart rate variability, this app uses the camera to detect blood flow from your fingertip.</string>
	<key>NSHealthShareUsageDescription</key>
	<string>We do not share your data. If you choose to enable it, we will read specific health metrics such as sleep and heart rate from Apple Health to enhance your experience with our app.</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>If you choose to grant permission, we will access your sleep and heart rate data from Apple Health to provide personalized insights and enhance your experience within the app.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need access to your location to provide location-based services in the app.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Location access is required to connect to devices while the app is in the background.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location to provide personalized experiences based on your current location.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>your usage description here</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>bluetooth-central</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>$(LAUNCH_SCREEN_STORYBOARD)</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
</dict>
</plist>
