# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Push a new beta build to TestFlight"
  lane :beta do
    #match(
    #  type: "appstore",
    #  git_basic_authorization: ENV["MATCH_GIT_BASIC_AUTHORIZATION"]
    #)
    #ipa_path = "../build/ios/iphoneos/Runner.ipa"  
    api_key = app_store_connect_api_key(
      key_id: ENV["AK_KEY_ID"],
      issuer_id: ENV["AK_ISSUER_ID"],
      key_filepath: "authkey.p8",
      in_house: false
    )

    upload_to_testflight(
      ipa: "/Users/<USER>/Development/actions-runner/_work_vagustim/vagustimpro/vagustimpro/build/ios/ipa/vagustimpro.ipa",
      api_key: api_key,
      app_identifier: ENV["APP_IDENTIFIER"],
      skip_waiting_for_build_processing: true
    )
  end
end
