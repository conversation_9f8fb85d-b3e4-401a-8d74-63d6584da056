name: vagustimpro
description: "A new Flutter project."

publish_to: "none"

version: 3.38.0+83

environment:
  sdk: ">=3.2.6 <4.0.0"

dependencies:
  #Core
  app_links: ^6.3.3
  app_settings: ^5.1.1
  flutter_dotenv: ^5.2.1
  flutter_native_splash: ^2.4.4
  # flutter_smartlook: ^4.1.25
  get_it: ^8.0.3
  intl: ^0.20.2
  equatable: ^2.0.7
  flutter_markdown: ^0.7.5
  upgrader: ^11.3.1
  circular_countdown_timer: ^0.2.4
  rflutter_alert: ^2.0.7
  #in_app_review: ^2.0.10
  battery_plus: ^6.2.1
  cached_network_image: ^3.4.1

  #UI
  country_picker: ^2.0.27
  cupertino_icons: ^1.0.8
  flutter_spinkit: ^5.2.1
  info_popup: ^4.3.2
  story: ^1.1.0
  fl_chart: ^0.69.0
  step_progress_indicator: ^1.0.2
  gauge_indicator: ^0.4.3
  pinput: ^5.0.1
  

  #Firebase
  firebase_analytics: ^11.4.0
  firebase_auth: ^5.5.1
  firebase_core: ^3.10.0
  firebase_crashlytics: ^4.3.0
  firebase_remote_config: ^5.4.2
  
  flutter:
    sdk: flutter

  #State Management
  flutter_bloc: ^9.0.0
  provider: ^6.1.2
  fpdart: ^1.1.1
  flutter_riverpod: ^2.6.1

  #Cache
  flutter_secure_storage: ^9.2.4
  hive_flutter: ^1.1.0
  shared_preferences: ^2.5.3

  #Auth
  google_sign_in: ^6.2.2

  #Network
  http: ^1.2.2
  network_info_plus: ^6.1.3
  url_launcher: ^6.3.1

  #Purchase
  in_app_purchase: ^3.2.0

  #Animation
  lottie: ^3.1.3

  #Notification
  onesignal_flutter: ^5.3.0

  #Permission
  permission_handler: ^11.3.1

  #Purchase
  purchases_flutter: ^8.6.0
  purchases_ui_flutter: ^8.8.1

  #Bluetooth
  flutter_reactive_ble: ^5.4.0

  #Report
  shake_flutter: ^17.1.0

  #Calendar
  table_calendar: ^3.1.3

  #Responsibility
  flutter_screenutil: ^5.9.3

  #Share
  share_plus: ^10.1.4

  #Analytics
  mixpanel_flutter: ^2.3.4

  #Image
  flutter_svg: ^2.0.17

  #Tutorial
  tutorial_coach_mark: ^1.2.12

  #Health
  health: ^13.0.1

  #Others
  time_chart:
    git:
      url: https://github.com/Vagustim-Bioelectronics/time_chart.git
      ref: fix-latest-3.32.0-version
      
  wakelock_plus: ^1.2.11
  
  #Lints
  dart_code_metrics_annotations: ^1.0.0
  camera: ^0.11.1
  image: ^4.5.3
  complex: ^0.8.0
  fftea: ^1.5.0+1
  vibration: ^3.1.3
  sentry_flutter: ^8.14.1
  rxdart: ^0.28.0

dev_dependencies:
  sentry_dart_plugin: ^2.4.1
  custom_lint: ^0.7.0
  dart_code_metrics_presets: ^2.20.0
  flutter_lints: ^5.0.0

  flutter_test:
    sdk: flutter

flutter:
  generate: true
  uses-material-design: true

  assets:
    - assets/
    - assets/images/
    - assets/images/story/
    - assets/images/svg/
    - assets/gifs/
    - assets/lottie/
    - shorebird.yaml
    - assets/remote_config_defaults.json
    - .env.professional
    - .env.vagustim
    - .env.hrv
    - .env.parkinson

  fonts:
    - family: SF-Pro-Display
      fonts:
        - asset: assets/fonts/SF-Pro-Display-Thin.otf
          weight: 100
        - asset: assets/fonts/SF-Pro-Display-Ultralight.otf
          weight: 200
        - asset: assets/fonts/SF-Pro-Display-Light.otf
          weight: 300
        - asset: assets/fonts/SF-Pro-Display-Regular.otf
          weight: 400
        - asset: assets/fonts/SF-Pro-Display-Medium.otf
          weight: 500
        - asset: assets/fonts/SF-Pro-Display-Semibold.otf
          weight: 600
        - asset: assets/fonts/SF-Pro-Display-Bold.otf
          weight: 700
        - asset: assets/fonts/SF-Pro-Display-Heavy.otf
          weight: 800
        - asset: assets/fonts/SF-Pro-Display-Black.otf
          weight: 900

flutter_gen:
  synthetic-package: false

sentry:
  upload_debug_symbols: true
  upload_source_maps: true
  project: flutter
  org: vagustim
