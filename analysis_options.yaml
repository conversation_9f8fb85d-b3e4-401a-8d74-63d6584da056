# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# Include recommended lints for Flutter apps, packages, and plugins
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "example/**"
    - "build/**"
    - "**/*.g.dart"
    - "**/*.freezed.dart"
dart_code_metrics:
  extends:
    - package:dart_code_metrics_presets/all.yaml
  assists:
    wrap-with:
      - WidgetName
      - MyOtherWidget
  formatter:
    indent: 0
    line-length: 100
    cascading-widget-extensions: true
  rules:
    - avoid-inferrable-type-arguments: false
    - no-empty-string: false
    - prefer-define-hero-tag: false
    - arguments-ordering:
        alphabetize: true
        last:
          - child
          - children
    - no-equal-arguments:
        ignored-parameters:
          - height
          - width
          - enableScaleWH
          - bottomLeft
          - bottomRight
        ignore-inline-functions: false
