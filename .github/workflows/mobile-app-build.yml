name: mobile-app-build

on:
  workflow_dispatch:
  push:
    branches:
      - live

concurrency:
  group: mobile-app-build-${{ github.ref }}
  cancel-in-progress: true

jobs:
  mobile-app-build:
    runs-on: [self-hosted, vagustim-mac]
    env:
      MACOS_KEYCHAIN_PASSWORD: ${{ secrets.MACOS_KEYCHAIN_PASSWORD }} # only needed if you unlock keychain
      AK_ISSUER_ID: ${{ secrets.AK_ISSUER_ID }}
      AK_KEY_ID: ${{ secrets.AK_KEY_ID }}
      APPLE_ID: ${{ secrets.APPLE_ID }}
      ITC_TEAM_ID: ${{ secrets.ITC_TEAM_ID }}
      TEAM_ID: ${{ secrets.TEAM_ID }}
      
    permissions:
      contents: write

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Copy local build requirements to workspace
        run: |
          cp /Users/<USER>/Development/pipeline-files/.env.* .
          cp /Users/<USER>/Development/pipeline-files/key.properties android/
          cp /Users/<USER>/Development/pipeline-files/vagustimproprod/google-services.json android/app/src/vagustimproprod
          mkdir -p  android/app/src/vagustimprodev
          cp /Users/<USER>/Development/pipeline-files/vagustimproprod/google-services.json android/app/src/vagustimprodev
          cp /Users/<USER>/Development/pipeline-files/professionalprod/google-services.json android/app/src/professionalprod
          cp /Users/<USER>/Development/pipeline-files/professionaldev/google-services.json android/app/src/professionaldev


      - name: Decode App Store Connect API Key
        run: echo "${{ secrets.APP_STORE_API_KEY_BASE64 }}" | base64 -d > ios/authkey.p8

      - name: Generate Google Play Store json key
        run: |
          echo "${{ secrets.SUPPLY_JSON_KEY }}" | base64 -d > android/jsonkey.json

      - name: Get app version 
        run: |
          current_version=$(grep "^version:" pubspec.yaml | awk '{print $2}')
          echo "VERSION=$current_version" >> $GITHUB_ENV
          echo "v$current_version"
          
      - name: Setup rbenv and Ruby
        run: |
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          eval "$(rbenv init -)"
          export LANG=en_US.UTF-8  # Fix CocoaPods warning about encoding
          ruby -v
          pod --version

      - name: Install Ruby gems via Bundler
        run: |
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          eval "$(rbenv init -)"
          bundle install
          
      - name: 📦 Install Dart & Flutter dependencies
        run: |
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          
          eval "$(rbenv init -)"
          export LANG=en_US.UTF-8  # Fix CocoaPods warning about encoding 
          flutter clean
          flutter pub get
          cd ios
          pod install --repo-update

      - name: Build IOS IPA for "vagustimproprod" and push to appstore
        run: |
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          eval "$(rbenv init -)"
          export LANG=en_US.UTF-8  # Fix CocoaPods warning about encoding
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim IOS App* build `v${{ env.VERSION }}` started! :hammer_and_wrench: "}' *******************************************************************************
          /Users/<USER>/Development/pipeline-files/xcode_build.sh vagustimproprod /Users/<USER>/Development/actions-runner/_work_vagustim/vagustimpro/vagustimpro/lib/app/vagustimpro/main_vagustimpro_prod.dart 3.32.0
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim IOS App* build `v${{ env.VERSION }}` completed! :package: "}' *******************************************************************************
          cd ios
          APP_IDENTIFIER="com.vagustim.vagustimpro" bundle exec fastlane ios beta
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim IOS App* `v${{ env.VERSION }}` sent to testflight! :raised_hands: "}' *******************************************************************************

      - name: 🚀 Upload Android build for "vagustimproprod" to Play Store
        run:  | 
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          export PATH="$HOME/.shorebird/bin:$PATH"
          eval "$(rbenv init -)"
          export LANG=en_US.UTF-8  # Fix CocoaPods warning about encoding
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Android App* build `v${{ env.VERSION }}` started! :hammer_and_wrench: "}' *******************************************************************************
          yes | shorebird release android --flavor vagustimproprod --target lib/app/vagustimpro/main_vagustimpro_prod.dart --flutter-version=3.32.0
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Android App* build `v${{ env.VERSION }}` completed! :package: "}' *******************************************************************************
          cd android
          PACKAGE_NAME="com.vagustim.vagustimpro" bundle exec fastlane android vagustimproprod
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Android App* `v${{ env.VERSION }}` sent to Google Play Store for review! :raised_hands: "}' *******************************************************************************
      
      - name: 🚀 Upload Android build for "professionalprod" to Play Store
        run:  | 
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          export PATH="$HOME/.shorebird/bin:$PATH"
          eval "$(rbenv init -)"
          export LANG=en_US.UTF-8  # Fix CocoaPods warning about encoding
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Plus Android App* build `v${{ env.VERSION }}` started! :hammer_and_wrench: "}' *******************************************************************************
          yes | shorebird release android --flavor professionalprod --target lib/app/professional/main_professional_prod.dart --flutter-version=3.32.0
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Plus Android App* build `v${{ env.VERSION }}` completed! :package: "}' *******************************************************************************
          cd android
          PACKAGE_NAME="com.vagustim.professional" bundle exec fastlane android professionalprod
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Plus Android App* `v${{ env.VERSION }}` sent to Google Play Store for review! :raised_hands: "}' *******************************************************************************



      - name: Build IOS IPA for "vagustimprodev" and push to appstore
        run: |
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          eval "$(rbenv init -)"
          export LANG=en_US.UTF-8  # Fix CocoaPods warning about encoding
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim(DEV) IOS App* build `v${{ env.VERSION }}` started! :hammer_and_wrench: "}' *******************************************************************************
          /Users/<USER>/Development/pipeline-files/xcode_build.sh vagustimprodev /Users/<USER>/Development/actions-runner/_work_vagustim/vagustimpro/vagustimpro/lib/app/vagustimpro/main_vagustimpro_dev.dart 3.32.0
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim(DEV) IOS App* build `v${{ env.VERSION }}` completed! :package: "}' *******************************************************************************          
          cd ios
          APP_IDENTIFIER="com.vagustim.vagustimpro.dev" bundle exec fastlane ios beta
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim(DEV) IOS App* `v${{ env.VERSION }}` sent to testflight! :raised_hands: "}' *******************************************************************************

      - name: Build IOS IPA for "professionalprod" and push to appstore
        run: |
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          eval "$(rbenv init -)"
          export LANG=en_US.UTF-8  # Fix CocoaPods warning about encoding
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Plus IOS App* build `v${{ env.VERSION }}` started! :hammer_and_wrench: "}' *******************************************************************************
          /Users/<USER>/Development/pipeline-files/xcode_build.sh professionalprod /Users/<USER>/Development/actions-runner/_work_vagustim/vagustimpro/vagustimpro/lib/app/professional/main_professional_prod.dart 3.32.0
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Plus IOS App* build `v${{ env.VERSION }}` completed! :package: "}' *******************************************************************************                    
          cd ios
          APP_IDENTIFIER="com.vagustim.professional" bundle exec fastlane ios beta
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Plus IOS App* `v${{ env.VERSION }}` sent to testflight! :raised_hands: "}' *******************************************************************************
          
      - name: Build IOS IPA for "professionaldev" and push to appstore
        run: |
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          eval "$(rbenv init -)"
          export LANG=en_US.UTF-8  # Fix CocoaPods warning about encoding
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Plus(DEV) IOS App* build `v${{ env.VERSION }}` started! :hammer_and_wrench: "}' *******************************************************************************
          /Users/<USER>/Development/pipeline-files/xcode_build.sh professionaldev /Users/<USER>/Development/actions-runner/_work_vagustim/vagustimpro/vagustimpro/lib/app/professional/main_professional_dev.dart 3.32.0
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Plus(DEV) IOS App* build `v${{ env.VERSION }}` completed! :package: "}' *******************************************************************************                              
          cd ios
          APP_IDENTIFIER="com.vagustim.professional.dev" bundle exec fastlane ios beta
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Vagustim Plus(DEV) IOS App* `v${{ env.VERSION }}` sent to testflight! :raised_hands: "}' *******************************************************************************

      - name: Build IOS IPA for "hrvprod" and push to appstore
        run: |
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          eval "$(rbenv init -)"
          export LANG=en_US.UTF-8  # Fix CocoaPods warning about encoding
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Heartium IOS App* build `v${{ env.VERSION }}` started! :hammer_and_wrench: "}' *******************************************************************************
          /Users/<USER>/Development/pipeline-files/xcode_build.sh hrvprod /Users/<USER>/Development/actions-runner/_work_vagustim/vagustimpro/vagustimpro/lib/app/hrv/main_hrv_prod.dart 3.32.0
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Heartium IOS App* build `v${{ env.VERSION }}` completed! :package: "}' *******************************************************************************                              
          cd ios
          APP_IDENTIFIER="com.vagustim.hrv" bundle exec fastlane ios beta
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Heartium IOS App* `v${{ env.VERSION }}` sent to testflight! :raised_hands: "}' *******************************************************************************

      - name: Build IOS IPA for "hrvdev" and push to appstore
        run: |
          export PATH="$HOME/.rbenv/shims:$PATH"
          export PATH="/usr/local/bin:$PATH"
          eval "$(rbenv init -)"
          export LANG=en_US.UTF-8  # Fix CocoaPods warning about encoding
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Heartium(DEV) IOS App* build `v${{ env.VERSION }}` started! :hammer_and_wrench: "}' *******************************************************************************          
          /Users/<USER>/Development/pipeline-files/xcode_build.sh hrvdev /Users/<USER>/Development/actions-runner/_work_vagustim/vagustimpro/vagustimpro/lib/app/hrv/main_hrv_dev.dart 3.32.0
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Heartium(DEV) IOS App* build `v${{ env.VERSION }}` completed! :package: "}' *******************************************************************************                                        
          cd ios
          APP_IDENTIFIER="com.vagustim.hrv.dev" bundle exec fastlane ios beta
          curl -X POST -H 'Content-type: application/json' --data '{"text": "*Heartium(DEV) IOS App* `v${{ env.VERSION }}` sent to testflight! :raised_hands: "}' *******************************************************************************

      - name: Notify Slack if any step fails
        if: failure()  # Only runs if any previous step failed
        run: |
          curl -X POST -H 'Content-type: application/json' --data '{"text": ":rotating_light: *Mobile App Build Pipeline Failed* :rotating_light: Something went wrong during the `v${{ env.VERSION }}` build. :x: Please check commit `${{ github.sha }}`! :warning: "}' *******************************************************************************
