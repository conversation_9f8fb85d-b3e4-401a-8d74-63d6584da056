plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def debugKeystorePropertiesFile = rootProject.file("debug-key.properties")
def debugKeystoreProperties = new Properties()
if (debugKeystorePropertiesFile.exists()) {
    debugKeystoreProperties.load(new FileInputStream(debugKeystorePropertiesFile))
}

def keystoreProperties = new Properties()
def keystoreFile = rootProject.file('key.properties')
if (keystoreFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystoreFile))
}

android {
    namespace "com.vagustim.vagustimpro"
    compileSdkVersion 35
    ndkVersion "28.1.13356709"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.vagustim.vagustimpro"
        minSdkVersion 26
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

     signingConfigs {
        debug {
            keyAlias debugKeystoreProperties['keyAlias']
            keyPassword debugKeystoreProperties['keyPassword']
            storeFile file(debugKeystoreProperties['storeFile'])
            storePassword debugKeystoreProperties['storePassword']
        }
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.release
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            signingConfig signingConfigs.release 
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    flavorDimensions "app"
    productFlavors {
        parkinsonprod {
            dimension "app"
            applicationId "com.vagustim.parkinson" 
            resValue "string", "app_name", "Parkinson"
        }

        parkinsondev {
            dimension "app"
            applicationId "com.vagustim.parkinson.dev" 
            resValue "string", "app_name", "Parkinson (Dev)"
        }

        vagustimproprod {
            dimension "app"
            applicationId "com.vagustim.vagustimpro" 
            resValue "string", "app_name", "Vagustim"
        }

        vagustimprodev {
            dimension "app"
            applicationId "com.vagustim.vagustimpro.dev" 
            resValue "string", "app_name", "Vagustim (Dev)"
        }

        professionalprod {
            dimension "app"
            applicationId "com.vagustim.professional" 
            resValue "string", "app_name", "Vagustim Plus"
            
        }

        professionaldev {
            dimension "app"
            applicationId "com.vagustim.professional.dev" 
            resValue "string", "app_name", "Vagustim Plus (Dev)"
        }

    }
}

flutter {
    source '../..'
}

dependencies {
    implementation platform("com.google.firebase:firebase-bom:33.14.0")
    implementation "com.google.firebase:firebase-crashlytics"
    implementation "com.google.firebase:firebase-analytics"
    implementation "androidx.multidex:multidex:2.0.1"
    implementation "androidx.core:core-ktx:1.15.0"
    implementation "androidx.appcompat:appcompat:1.7.0"
    implementation "com.polidea.rxandroidble2:rxandroidble:1.11.1"  

}