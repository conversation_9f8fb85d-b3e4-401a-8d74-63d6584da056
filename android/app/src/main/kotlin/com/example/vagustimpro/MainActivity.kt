package com.vagustim.vagustimpro

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothProfile
import android.bluetooth.BluetoothDevice
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.util.Log
import com.polidea.rxandroidble2.exceptions.BleException  
import io.reactivex.exceptions.UndeliverableException
import io.reactivex.plugins.RxJavaPlugins

class MainActivity : FlutterFragmentActivity() {
    private val CHANNEL_PERMISSIONS = "com.vagustim.vagustimpro/permissions"
    private val CHANNEL_BLUETOOTH = "com.vagustim.vagustimpro/bluetooth"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // RxJava global error handler
        RxJavaPlugins.setErrorHandler { throwable ->
            if (throwable is UndeliverableException && throwable.cause is BleException) {
                // Ignore BLE exceptions when there's no subscriber
                return@setErrorHandler
            } else {
                // Propagate other errors
                throw throwable
            }
        }

        // Bluetooth permissions MethodChannel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_PERMISSIONS).setMethodCallHandler { call, result ->
            if (call.method == "requestBluetoothPermissions") {
                requestBluetoothPermissions()
                result.success(null)
            } else {
                result.notImplemented()
            }
        }

        // Bluetooth MethodChannel for connected and bonded devices
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_BLUETOOTH).setMethodCallHandler { call, result ->
            when (call.method) {
                "getBondedDevices" -> result.success(getBondedDevices())
                "getConnectedDevices" -> result.success(getConnectedDevices())
                else -> result.notImplemented()
            }
        }

        // Global uncaught exception handler
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            Log.e("UncaughtException", "Uncaught exception in thread ${thread.name}", exception)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Handle dynamic link
        intent?.data?.let { handleDynamicLink(it) }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        intent.data?.let { handleDynamicLink(it) }
    }

    private fun handleDynamicLink(data: Uri) {
        Log.d("DynamicLink", "Received dynamic link: $data")
    }

    private fun getBondedDevices(): List<Map<String, String>> {
        val bluetoothManager = getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        val bluetoothAdapter = bluetoothManager.adapter
        val bondedDevices = bluetoothAdapter?.bondedDevices

        return bondedDevices?.map { device ->
            mapOf(
                "name" to (device.name ?: "Unknown"),
                "address" to device.address
            )
        } ?: emptyList()
    }

    private fun getConnectedDevices(): List<Map<String, String>> {
        val bluetoothManager = getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        val bluetoothAdapter = bluetoothManager.adapter

        // Check GATT-connected devices
        val connectedDevices = bluetoothManager.getConnectedDevices(BluetoothProfile.GATT)
        if (connectedDevices.isNotEmpty()) {
            val device = connectedDevices[0]
            return listOf(
                mapOf(
                    "name" to (device.name ?: "Unknown"),
                    "address" to device.address
                )
            )
        }

        // Fall back to BLE bonded devices
        bluetoothAdapter?.bondedDevices?.forEach { device ->
            if (device.type == BluetoothDevice.DEVICE_TYPE_LE) {
                return listOf(
                    mapOf(
                        "name" to (device.name ?: "Unknown"),
                        "address" to device.address
                    )
                )
            }
        }

        return emptyList()
    }

    private fun requestBluetoothPermissions() {
        val permissions = arrayOf(
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.NEARBY_WIFI_DEVICES,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )

        val missingPermissions = permissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }

        if (missingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(this, missingPermissions.toTypedArray(), 0)
        }
    }
}
