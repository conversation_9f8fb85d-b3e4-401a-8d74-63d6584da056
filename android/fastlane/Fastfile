default_platform(:android)

platform :android do
  desc "Upload APK  to Google Play"
  lane :vagustimproprod do
    # Upload APK
    supply(
      aab: "/Users/<USER>/Development/actions-runner/_work_vagustim/vagustimpro/vagustimpro/build/app/outputs/bundle/vagustimproprodRelease/app-vagustimproprod-release.aab",
      json_key: "jsonkey.json", # or hardcode path like "fastlane/google-play-key.json"
      package_name: ENV["PACKAGE_NAME"], # change this to your actual package
      track: "production"
    )
  end
  lane :professionalprod do
    # Upload APK
    supply(
      aab: "/Users/<USER>/Development/actions-runner/_work_vagustim/vagustimpro/vagustimpro/build/app/outputs/bundle/professionalprodRelease/app-professionalprod-release.aab",
      json_key: "jsonkey.json", # or hardcode path like "fastlane/google-play-key.json"
      package_name: ENV["PACKAGE_NAME"], # change this to your actual package
      track: "production"
    )
  end
end

