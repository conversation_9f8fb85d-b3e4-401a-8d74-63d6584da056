import 'package:flutter/material.dart';

import '../../core/app_config/app_colors.dart';
import '../../core/app_config/app_text_styles.dart';

class FlavorBottomBarConfig {
  List<Widget> pages;
  List<BottomNavigationBarItem> items;
  TextStyle? selectedLabelStyle;
  Color? selectedItemColor;
  TextStyle? unselectedLabelStyle;
  Color? backgroundColor;
  FlavorBottomBarConfig({
    this.backgroundColor,
    required this.items,
    required this.pages,
    this.selectedItemColor,
    this.selectedLabelStyle,
    this.unselectedLabelStyle,
  });

  void init() {
    final backgroundOpacity = 0.1;

    selectedLabelStyle ??= AppTextStyles.bottomBarSelectedTextStyle;
    selectedItemColor ??= AppColors.bottomBarSelectedItemColor;
    unselectedLabelStyle ??= AppTextStyles.bottomBarUnSelectedTextStyle;
    backgroundColor ??= AppColors.bottomBarBackgroundColor.withValues(alpha: backgroundOpacity);
  }
}
