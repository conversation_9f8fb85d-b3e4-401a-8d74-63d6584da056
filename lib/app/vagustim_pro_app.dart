import 'dart:async';
import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// Import 'package:flutter_smartlook/flutter_smartlook.dart';.
import 'package:get_it/get_it.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:provider/provider.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shake_flutter/shake_flutter.dart';
import 'package:upgrader/upgrader.dart';
import 'package:vagustimpro/app/flavor/flavor_config.dart';
import 'package:vagustimpro/core/app_config/app_strings.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/custom_widgets/custom_intro_screen.dart';

import 'package:vagustimpro/core/navigator/app_navigator.dart';
import 'package:vagustimpro/core/app_config/app_theme.dart';
import 'package:vagustimpro/core/custom_widgets/loader.dart';
import 'package:vagustimpro/core/paywall/app_data.dart';
import 'package:vagustimpro/core/paywall/constants.dart';
import 'package:vagustimpro/features/app_permission/presentation/bloc/app_permission_bloc.dart';

import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/pages/login/hrv_auth_widget.dart';
import 'package:vagustimpro/features/auth/presentation/pages/login/login_page.dart';
import '../core/manager/bluetooth_service.dart';
import '../features/landing/presentation/pages/landing_page.dart';

class VagustimProApp extends StatefulWidget {
  const VagustimProApp({
    required this.appTitle,
    this.initScreen,
    this.isDebugShowCheckedModeBanner = false,
    super.key,
    required this.theme,
  });

  final int? initScreen;
  final ThemeData? theme;
  final String appTitle;
  final bool isDebugShowCheckedModeBanner;

  @override
  State<VagustimProApp> createState() => _VagustimProAppState();
}

class _VagustimProAppState extends State<VagustimProApp> {
  static bool _isEventsDispatched = false;

  static Widget _buildAuthenticatedScreen(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    final bundleId =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

    if (user == null) {
      return const Loader();
    }

    // Only dispatch these events once.
    if (!_isEventsDispatched) {
      unawaited(Shake.registerUser(user.uid));
      unawaited(Purchases.logIn(user.uid));
      unawaited(OneSignal.login(user.uid));

      context.read<AuthBloc>().add(GetUserEvent());
      context.read<AppPermissionBloc>().add(GetAppPermissionsEvent());

      _isEventsDispatched = true;
    }

    return FlavorConstants.isHrv(bundleId) ? const HrvAuthWidget() : LandingPage();
  }

  @override
  void initState() {
    super.initState();
    unawaited(_initPlatformState());
    /*final smartlookTool = Smartlook.instance;
    unawaited(smartlookTool.preferences.setProjectKey(dotenv.env['SMARTLOOK_API_KEY']!));
    if (!kDebugMode) unawaited(smartlookTool.start());*/
    final bundleId =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

    if (Platform.isAndroid && !FlavorConstants.isHrv(bundleId)) {
      BluetoothService.getBondedDevices();
    }
  }

  Future<void> _initPlatformState() async {
    AppData.instance.appUserID = await Purchases.appUserID;

    Purchases.addCustomerInfoUpdateListener(
      (customerInfo) => _handleCustomerInfoUpdate(customerInfo),
    );
  }

  void _handleCustomerInfoUpdate(CustomerInfo customerInfo) {
    _updateEntitlements(customerInfo);
  }

  void _updateEntitlements(CustomerInfo customerInfo) {
    final entitlements = customerInfo.entitlements.all;

    final individulaEntitlement = entitlements[individualEntitlementID];
    AppData.instance.isIndividualEntitlementActive = individulaEntitlement?.isActive ?? false;

    final healthProfessionalEntitlement = entitlements[healthProfessionalEntitlementID];
    AppData.instance.isHealthProfessionalEntitlementActive =
        healthProfessionalEntitlement?.isActive ?? false;

    setState(() {});
  }

  Widget _buildHome() {
    //Burası ValideOTP view'ında 3 kere tetikleniyor. Widget tekrar çalıştığı için duplicate hatası veriyor idi.
    //isRedirectLandingPage ile 1 kere tetiklenmesi yapıldı.
    bool isRedirectLandingPage = (context.read<AuthBloc>().isRedirectLandingPage ?? false);
    final bundleId =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

    return StreamBuilder<User?>(
      builder: (context, snapshot) {
        if (widget.initScreen == null || widget.initScreen == 0) {
          FlutterNativeSplash.remove();

          return const CustomIntroScreen();
        }
        FlutterNativeSplash.remove();

        switch (snapshot.connectionState) {
          case ConnectionState.active:
            return snapshot.hasData && !(isRedirectLandingPage)
                ? _buildAuthenticatedScreen(context)
                : FlavorConstants.isHrv(bundleId)
                    ? const HrvAuthWidget()
                    : LoginPage();

          case ConnectionState.waiting:
            return const Scaffold(body: SafeArea(child: Loader()));

          default:
            return FlavorConstants.isHrv(bundleId) ? const HrvAuthWidget() : LoginPage();
        }
      },
      stream: FirebaseAuth.instance.idTokenChanges(),
    );
  }

  @override
  Widget build(BuildContext context) {
    // IPhone 12, iPhone 12 Pro, iPhone 13, iPhone 13 Pro.
    const size = Size(390, 844);

    return ScreenUtilInit(
      designSize: size,
      enableScaleText: () => true,
      enableScaleWH: () => true,
      ensureScreenSize: true,
      minTextAdapt: true,
      splitScreenMode: true,
      child: Consumer<BleStatus?>(
        builder: (_, status, __) {
          // Return SmartlookRecordingWidget(.
          return MaterialApp(
            builder: (ctx, child) {
              return child == null
                  ? const Loader()
                  : MediaQuery(
                      data: MediaQuery.of(ctx).copyWith(textScaler: const TextScaler.linear(1)),
                      child: child,
                    );
            },
            debugShowCheckedModeBanner: widget.isDebugShowCheckedModeBanner,
            home: UpgradeAlert(
              dialogStyle: UpgradeDialogStyle.cupertino,
              child: _buildHome(),
            ),
            navigatorKey: AppNavigator.navigatorKey,
            routes: AppNavigator.routes,
            theme: AppTheme.customThemeMode,
            title: widget.appTitle,
          );
        },
      ),
    );
  }
}
