import 'package:vagustimpro/app/flavor/flavor_config.dart';
import 'package:vagustimpro/app/vagustimpro/vagustim_pro_flavor_init.dart';
import 'package:vagustimpro/core/firebase_options/vagustimpro/firebase_options_vagustimpro_dev.dart';
import 'package:vagustimpro/main_common.dart';

import '../../core/constants/flavor_constants.dart';

void main() async {
  await mainCommon(
    FlavorConfig()
      ..firebaseOptions = DefaultFirebaseOptionsVagustimproDev.currentPlatform
      ..appTitle = "Vagustim (Dev)"
      ..baseUrl = "https://neo-dev.vagustim.io"
      ..imageLocation = "assets/images/app_icon.png"
      ..isDebugShowCheckedModeBanner = false
      ..flavorBottomBarConfig = VagustimProFlavorInit.flavorBottomBarConfig
      ..bundleId = FlavorConstants.vagustimProDevBundleId
      ..appName = "vagustim"
      ..envFileName = ".env.vagustim",
  );
}
