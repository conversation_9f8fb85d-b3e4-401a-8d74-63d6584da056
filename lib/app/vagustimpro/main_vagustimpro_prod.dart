import 'package:vagustimpro/app/flavor/flavor_config.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/firebase_options/vagustimpro/firebase_options_vagustimpro_prod.dart';
import 'package:vagustimpro/main_common.dart';

import 'vagustim_pro_flavor_init.dart';

void main() async {
  await mainCommon(
    FlavorConfig()
      ..firebaseOptions = DefaultFirebaseOptionsVagustimProProd.currentPlatform
      ..baseUrl = "https://weekly.vagustim.io"
      ..appTitle = "Vagustim"
      ..imageLocation = "assets/images/app_icon.png"
      ..isDebugShowCheckedModeBanner = false
      ..flavorBottomBarConfig = VagustimProFlavorInit.flavorBottomBarConfig
      ..bundleId = FlavorConstants.vagustimProProdBundleId
      ..appName = "vagustim"
      ..envFileName = ".env.vagustim",
  );
}
