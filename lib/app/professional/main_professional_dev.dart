import 'package:vagustimpro/app/flavor/flavor_config.dart';
import 'package:vagustimpro/app/professional/professional_flavor_init.dart';
import 'package:vagustimpro/core/firebase_options/professional/firebase_options_professional_dev.dart';
import 'package:vagustimpro/main_common.dart';

import '../../core/constants/flavor_constants.dart';

void main() async{
  await mainCommon(
    FlavorConfig()
      ..firebaseOptions = DefaultFirebaseOptionsProfessionalDev.currentPlatform
      ..appTitle = "Vagustim Plus (Dev)"
      ..baseUrl = "https://neo-dev.vagustim.io"
      ..imageLocation = "assets/images/app_icon.png"
      ..isDebugShowCheckedModeBanner = true
      ..flavorBottomBarConfig = ProfessionalFlavorInit.flavorBottomBarConfig
      ..bundleId = FlavorConstants.vagustimProfessionalDevBundleId
      ..appName = "professional"
      ..envFileName = ".env.professional",
  );
}
