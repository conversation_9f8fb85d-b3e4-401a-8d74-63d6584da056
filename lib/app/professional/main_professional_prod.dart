import 'package:vagustimpro/app/flavor/flavor_config.dart';
import 'package:vagustimpro/app/professional/professional_flavor_init.dart';
import 'package:vagustimpro/core/firebase_options/professional/firebase_options_professional_prod.dart';
import 'package:vagustimpro/main_common.dart';

import '../../core/constants/flavor_constants.dart';

void main() {
  mainCommon(
    FlavorConfig()
      ..firebaseOptions = DefaultFirebaseOptionsProfessionalProd.currentPlatform
      ..appTitle = "Vagustim Plus"
      ..baseUrl = "https://weekly.vagustim.io"
      ..imageLocation = "assets/images/app_icon.png"
      ..isDebugShowCheckedModeBanner = false
      ..flavorBottomBarConfig = ProfessionalFlavorInit.flavorBottomBarConfig
      ..bundleId = FlavorConstants.vagustimProfessionalProdBundleId
      ..appName = "professional"
      ..envFileName = ".env.professional",
  );
}
