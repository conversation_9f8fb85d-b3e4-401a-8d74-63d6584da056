import 'package:vagustimpro/app/flavor/flavor_config.dart';
import 'package:vagustimpro/app/hrv/hrv_flavor_init.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/firebase_options/hrv/firebase_options_hrv_dev.dart';
import 'package:vagustimpro/main_common.dart';

void main()async {
 await  mainCommon(
    FlavorConfig()
      ..firebaseOptions = DefaultFirebaseOptionsHrvDev.currentPlatform
      ..appTitle = "Heartium (Dev)"
      ..baseUrl = "https://dev.vagustim.io"
      ..imageLocation = "assets/images/app_icon.png"
      ..isDebugShowCheckedModeBanner = true
      ..flavorBottomBarConfig = HrvFlavorInit.flavorBottomBarConfig
      ..bundleId = FlavorConstants.hrvDevBundleId
      ..appName = "heartium"
      ..envFileName = ".env.hrv",
  );
}
