import 'package:vagustimpro/features/home/<USER>/entities/activity_entity.dart';

class ActivityModel extends ActivityEntity {
  const ActivityModel({
    required super.day,
    required super.isUsed,
    super.weekStartDate,
  });

  factory ActivityModel.fromJson(Map<String, dynamic> json) {
    return ActivityModel(
      day: json['day'],
      isUsed: json['is_used'],
      weekStartDate: json['week_start_date'],
    );
  }
}
