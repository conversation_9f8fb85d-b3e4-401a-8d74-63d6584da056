import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vagustimpro/core/paywall/paywall_helper.dart';
import 'package:vagustimpro/features/home/<USER>/usecases/get_activity_usecase.dart';
import 'package:vagustimpro/features/home/<USER>/bloc/home_status.dart';

import '../../domain/entities/activity_entity.dart';
import '../../domain/params/activity_params.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final GetActivityUsecase getActivityUsecase;

  HomeBloc({required this.getActivityUsecase}) : super(const HomeState()) {
    on<GetActivityEvent>(_getActivityEvent);
    on<GetUserPremiumEvent>(_getUserPremium);
  }

  Future<void> _getActivityEvent(
    GetActivityEvent event,
    Emitter<HomeState> emit,
  ) async {
    emit(state.copyWith(status: HomeStatus.loading));

    final res = await getActivityUsecase.calculate(event.params);

    res.fold(
      (fail) => emit(
        state.copyWith(failureMessage: fail.message, status: HomeStatus.failure),
      ),
      (activities) => emit(state.copyWith(entity: activities, status: HomeStatus.success)),
    );
  }

  Future<void> _getUserPremium(
    GetUserPremiumEvent event,
    Emitter<HomeState> emit,
  ) async {
    emit(state.copyWith(status: HomeStatus.loading));

    final isUserPremium = await PaywallHelper.isUserPremium(event.context);

    emit(state.copyWith(isUserPremium: isUserPremium));
  }
}
