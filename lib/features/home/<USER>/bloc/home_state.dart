part of 'home_bloc.dart';

class HomeState extends Equatable {
  final HomeStatus status;
  final String? failureMessage;
  final List<ActivityEntity>? entity;
  final bool? isUserPremium;

  @override
  List<Object?> get props => [status, failureMessage, entity, isUserPremium];
  const HomeState({
    this.entity,
    this.failureMessage,
    this.isUserPremium,
    this.status = HomeStatus.initial,
  });

  HomeState copyWith({
    List<ActivityEntity>? entity,
    String? failureMessage,
    bool? isUserPremium,
    HomeStatus? status,
  }) {
    return HomeState(
      entity: entity ?? this.entity,
      failureMessage: failureMessage ?? this.failureMessage,
      isUserPremium: isUserPremium ?? this.isUserPremium,
      status: status ?? this.status,
    );
  }
}
