import 'package:vagustimpro/features/home/<USER>/models/activity_model.dart';
import 'package:vagustimpro/features/home/<USER>/entities/activity_entity.dart';
import 'package:vagustimpro/features/home/<USER>/params/activity_params.dart';

import '../../../../core/app_config/network_constants.dart';
import '../../../../core/exception/api_exception.dart';
import '../../../../core/network/network_endpoints_enum.dart';
import '../../../../core/utils/api_response.dart';
import 'package:http/http.dart' as http;

import 'home_data_source.dart';

class HomeDataSourceImpl extends HomeDataSource {
  @override
  Future<List<ActivityEntity>> getActivity({required ActivityParams params}) async {
    const statusCode = 500;
    try {
      final response = await http.get(
        Uri.parse(
          '${Endpoints.weeklyActivity.path}/${params.subuserUid ?? ""}?week_start=${params.getWeekStartDate ?? ""}',
        ),
        headers: await NetworkConstants.requestHeaders(),
      );

      final apiResponse = parseResponse(response);
      final ApiResponse(:data, :errorMessage, :isSuccess) = apiResponse;

      if (isSuccess) {
        if (data["activities"] is List) {
          final activities = data["activities"];

          return activities.map<ActivityEntity>((daily) => ActivityModel.fromJson(daily)).toList();
        }
        Error.throwWithStackTrace(
          ApiException(message: errorMessage ?? "", statusCode: statusCode),
          StackTrace.current,
        );
      }
      Error.throwWithStackTrace(
        ApiException(message: errorMessage ?? "", statusCode: statusCode),
        StackTrace.current,
      );
    } catch (error, stack) {
      Error.throwWithStackTrace(
        ApiException(message: error.toString(), statusCode: statusCode),
        stack,
      );
    }
  }
}
