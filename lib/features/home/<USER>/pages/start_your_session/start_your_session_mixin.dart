part of 'start_your_session.dart';

mixin StartYourSessionMixin on State<StartYourSession> {
  final _homePageConfig = GetIt.instance<RemoteConfigService>().homePageConfig;

  // Constants for dialog messages.
  static const _kStopSessionDialogDescription =
      "Please stop the current session before starting a new one.";

  static const _kControlButtonText = "Control Vagustim";

  void _handleAIDrivenCustomizedProtocol(
    BleScanner bleScanner,
    BleStatus? bleStatus,
    ConnectionStateUpdate connectionStateUpdate,
  ) {
    if (bleStatus == BleStatus.poweredOff) {
      _handleBleStatusPoweredOff();

      return;
    }

    if (bleStatus != BleStatus.ready) {
      _handleBleReady();

      return;
    }
    final deviceStatus = context.read<StimulationBloc>().deviceStatus;

    if (_isDeviceActive(deviceStatus) &&
        connectionStateUpdate.connectionState == DeviceConnectionState.connected) {
      _showStopSessionDialog();
    } else {
      unawaited(bleScanner.startScan());

      unawaited(AppRoute.pushNewScreen(context, SelectSessionTypeAiDrivenView()));
    }
  }

  void _handleAdjustYourOwnParameter(
    BleScanner bleScanner,
    BleStatus? bleStatus,
    ConnectionStateUpdate connectionStateUpdate,
  ) {
    if (bleStatus == BleStatus.poweredOff) {
      _handleBleStatusPoweredOff();

      return;
    }

    if (bleStatus != BleStatus.ready) {
      _handleBleReady();

      return;
    }
    final deviceStatus = context.read<StimulationBloc>().deviceStatus;

    if (_isDeviceActive(deviceStatus) &&
        connectionStateUpdate.connectionState == DeviceConnectionState.connected) {
      _showStopSessionDialog();
    } else {
      unawaited(bleScanner.startScan());

      AppRoute.pushNamed(
        context,
        RouteConstants.adjustParametersPage,
        arguments: widget.subuser,
      );
    }
  }

  void _handleOnTap(BleScanner bleScanner, BleStatus? bleStatus, SessionType sessionType) {
    if (bleStatus == BleStatus.poweredOff) {
      _handleBleStatusPoweredOff();

      return;
    }

    if (bleStatus != BleStatus.ready) {
      _handleBleReady();

      return;
    }

    if (sessionType == SessionType.preSetPrograms) {
      unawaited(bleScanner.startScan());

      unawaited(AppRoute.pushNewScreen(context, PresetProgramView()));
    }
  }

  void _handleBleReady() async {
    PermissionStatus status = await Permission.bluetoothConnect.status;
    if (!status.isGranted) {
      final dialogService = GetIt.instance.get<DialogService>();

      final goSettings = _homePageConfig.goToSettings;
      final settingsMessage = _homePageConfig.goToSettingsMessage;
      final settingsTitle = _homePageConfig.blePermission;

      await dialogService.showDialog(
        confirmButtonText: goSettings,
        message: settingsMessage,
        navigatorKey: AppNavigator.navigatorKey,
        onConfirm: () async => await openAppSettings(),
        title: settingsTitle,
      );
    }
  }

  void _handleBleStatusPoweredOff() async {
    final dialogService = GetIt.instance.get<DialogService>();
    final okText = _homePageConfig.okText;
    final title = _homePageConfig.bleOffMessage;

    await dialogService.showDialog(
      confirmButtonText: okText,
      message: _homePageConfig.bleOffMessage,
      navigatorKey: AppNavigator.navigatorKey,
      title: title,
    );
  }

  static bool _isDeviceActive(String? deviceStatus) {
    final start = "start";
    final pause = "pause";
    final zeroValue = "0";

    return deviceStatus == start ||
        deviceStatus == pause ||
        (int.tryParse(deviceStatus ?? zeroValue) ?? 0) > 0;
  }

  void _showStopSessionDialog() {
    unawaited(AppDialog(
      context: context,
      description: _kStopSessionDialogDescription,
      okButtonOnTap: () => _handleNavigateToBleSearchPage(true),
      okButtonText: _kControlButtonText,
    ).show);
  }

  void _handleNavigateToBleSearchPage(bool isContinuousStimulation) {

    context.read<LandingBloc>().add(ChangeBottomBarIndexEvent(currentIndex: 2));

    /*  unawaited(AppRoute.pushNewScreen(
      context,
      BleSearchPage(
        isContinuousStimulation: isContinuousStimulation,
        presetProgram: stimBloc.presetProgramType,
        sessionType: stimBloc.currentSessionType ?? SessionType.quickStart,
        subuser: context.read<AuthBloc>().currentUser?.defaultSubuser,
      ),
    ));*/
  }
}
