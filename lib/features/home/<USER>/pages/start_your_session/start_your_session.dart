// ignore_for_file: avoid-long-functions

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:get_it/get_it.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/remote_config/select_your_session_type_page_config.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/home_card.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/home_card_line_widget.dart';
import '../../../../../core/app_config/app_colors.dart';
import '../../../../../core/ble/ble_scanner.dart';
import '../../../../../core/enum/assets_enums.dart';
import '../../../../../core/navigator/app_navigator.dart';
import '../../../../../core/navigator/routes/app_route.dart';
import '../../../../../core/navigator/routes/constants/route_constants.dart';
import '../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../core/services/dialog_service.dart';
import '../../../../landing/presentation/bloc/landing_bloc.dart';
import '../../../../parameter/domain/params/session_type.dart';
import '../../../../parameter/presentation/pages/widgets/preset_programs/preset_program_view.dart';
import '../../../../parameter/presentation/pages/widgets/select_session_type/ai_driven/select_session_type_ai_driven_view.dart';
import '../../../../parameter/presentation/widgets/session_option_minimal_widget.dart';
import '../../../../stimulation/presentation/bloc/stimulation_bloc.dart';
import '../../../../time_of_usage/presentation/widgets/app_dialog.dart';

part 'start_your_session_mixin.dart';

class StartYourSession extends StatefulWidget {
  const StartYourSession({super.key, required this.subuser});

  final SubuserEntity? subuser;

  @override
  State<StartYourSession> createState() => _StartYourSessionState();
}

class _StartYourSessionState extends State<StartYourSession>
    with StartYourSessionMixin, TickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    final selectYourSessionTypePageConfig =
        GetIt.instance.get<RemoteConfigService>().selectYourSessionTypePageConfig;

    final SelectYourSessionTypePageConfig(
      :adjustYourOwnParametersDesc,
      :aiDriven,
      :aiDrivenDesc,
    ) = selectYourSessionTypePageConfig;

    return Padding(
      padding: context.paddingMediumHorizontal,
      child: Consumer3<BleStatus?, BleScanner, ConnectionStateUpdate>(
        builder: (ctx, bleStatus, bleScanner, connectionStateUpdate, child) {
          return HomeCard(
            child: Padding(
              padding: ctx.paddingDisplay,
              child: Column(
                spacing: 0,
                children: [
                  HomeCardLineWidget(title: "Start Your Session"),
                  AppGaps.instance.gapVS8,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    spacing: 0,
                    children: [
                      Expanded(
                        child: SessionOptionMinimalWidget(
                          backgroundColor: AppColors.aiDrivenBgColor,
                          description: aiDrivenDesc,
                          iconBackgroundColor: AppColors.aiDrivenIconBgColor,
                          imagePath: AssetsEnums.icAiDriven.assetSvg,
                          isComingSoon: true,
                          onTap: () => _handleAIDrivenCustomizedProtocol(
                            bleScanner,
                            bleStatus,
                            connectionStateUpdate,
                          ),
                          title: aiDriven,
                        ),
                      ),
                      AppGaps.gapW4,
                      Expanded(
                        child: SessionOptionMinimalWidget(
                          backgroundColor: AppColors.presetProgramsBgColor,
                          description: "Programs",
                          iconBackgroundColor: AppColors.presetProgramsIconBgColor,
                          imagePath: AssetsEnums.icPresetPrograms.assetSvg,
                          onTap: () =>
                              _handleOnTap(bleScanner, bleStatus, SessionType.preSetPrograms),
                          title: "Programs\n",
                        ),
                      ),
                      AppGaps.gapW4,
                      Expanded(
                        child: SessionOptionMinimalWidget(
                          backgroundColor: AppColors.adjustParametersBgColor,
                          description: adjustYourOwnParametersDesc,
                          iconBackgroundColor: AppColors.adjustParametersIconBgColor,
                          imagePath: AssetsEnums.icAdjustYourParameters.assetSvg,
                          isRequiredPremium: true,
                          onTap: () => _handleAdjustYourOwnParameter(
                            bleScanner,
                            bleStatus,
                            connectionStateUpdate,
                          ),
                          title: "Adjust Your\nOwn Parameters",
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
