part of "individual_home_view.dart";

mixin IndividualHomeViewMixin on State<IndividualHomeView> {
  final _isShowDailyReminder = false;

  @override
  void initState() {
    super.initState();
    Future.microtask(() => handleFutureMicroTask());
    getSubuserStimulationEvent;

    setAnalytics();
  }

  void setAnalytics() {
    if (widget.subuser == null) return;
    AnalyticHelper.instance.track(AppStrings.subuserDetailView);

    AnalyticHelper.instance.setUserAnalyticWithSubuser(widget.subuser!, widget.userType);
  }

  Future<void> get _requestPermissions async {
    final isGranted = await _isCheckPermission;

    if (!isGranted) {
      if (Platform.isAndroid) {
        await PermissionService.requestBluetoothPermissions();
      } else {
        // ignore: avoid-ignoring-return-values*
        await Permission.bluetooth.request();
      }
    }
  }

  Future<bool> get _isCheckPermission async {
    final isBleGranted = await Permission.bluetooth.isGranted;

    return Platform.isAndroid ? (await Permission.location.isGranted & isBleGranted) : isBleGranted;
  }

  void get getSubuserStimulationEvent {
    final uid = widget.subuser?.uid ?? "";
    if ((uid).isNotEmpty && mounted) {
      context.read<StimulationBloc>().add(StimulationGetSubuserStimulationsEvent(uid));
    }
  }

  void handleFutureMicroTask() {
    _requestPermissions;

    InitOnesignal.initializeOneSignal();
  }
}
