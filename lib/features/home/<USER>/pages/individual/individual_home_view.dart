import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/auth/domain/entities/user_entity.dart';
import 'package:vagustimpro/features/home/<USER>/pages/start_your_session/start_your_session.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/story/individual_home_story.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/premium_button_widget_for_individual.dart';
import '../../../../../app/flavor/flavor_config.dart';
import '../../../../../core/app_config/app_gaps.dart';
import '../../../../../core/app_config/app_strings.dart';
import '../../../../../core/ble/permission_service.dart';
import '../../../../../core/helpers/analytic_helper.dart';
import '../../../../stimulation/presentation/bloc/stimulation_bloc.dart';
import '../../../../subuser/domain/entities/subuser_entity.dart';
import '../../bloc/home_bloc.dart';
import '../activity/individual_activity_card.dart';
import '../../../../subuser/presentation/widgets/individual/individual_home_header.dart';
import '../../../../../core/init/init_onesignal.dart';
part 'individual_home_view_mixin.dart';

class IndividualHomeView extends StatefulWidget {
  const IndividualHomeView({
    required this.isHistory,
    super.key,
    this.subuser,
    this.user,
    required this.userType,
  });

  final SubuserEntity? subuser;
  final UserEntity? user;

  final String userType;
  final bool isHistory;

  @override
  State<IndividualHomeView> createState() => _IndividualHomeViewState();
}

class _IndividualHomeViewState extends State<IndividualHomeView> with IndividualHomeViewMixin {
  @override
  Widget build(BuildContext context) {
    final gapVS8 = AppGaps.instance.gapVS16;
    final bundleId =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

    final isIndividual = FlavorConstants.isIndividual(bundleId);

    return BlocProvider<HomeBloc>.value(
      value: GetIt.instance.get(),
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              IndividualHomeHeader(
                isShowDailyReminder: _isShowDailyReminder,
                subuser: widget.subuser,
                user: widget.user,
                userType: widget.userType,
              ),
              const PremiumButtonWidgetForIndividual(),
              if (isIndividual) StartYourSession(subuser: widget.subuser),
              if (isIndividual) gapVS8,
              if (isIndividual) IndividualActivityCard(),
              gapVS8,
              Padding(
                padding: EdgeInsets.only(bottom: context.paddingMedium.bottom),
                child: const IndividualHomeStory(),
              ),
              AppGaps.gapBottom(context),
            ],
          ),
        ),
      ),
    );
  }
}
