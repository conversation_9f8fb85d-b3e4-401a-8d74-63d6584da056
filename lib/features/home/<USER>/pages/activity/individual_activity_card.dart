import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vagustimpro/features/home/<USER>/pages/activity/individual_activity_succes_card.dart';

import '../../../../../core/custom_widgets/loader.dart';
import '../../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../domain/params/activity_params.dart';
import '../../bloc/home_bloc.dart';
import '../../bloc/home_status.dart';
import 'individual_activity_failure_card.dart';

class IndividualActivityCard extends StatefulWidget {
  const IndividualActivityCard({super.key});

  @override
  State<IndividualActivityCard> createState() => _IndividualActivityCardState();
}

class _IndividualActivityCardState extends State<IndividualActivityCard> {
  @override
  void initState() {
    super.initState();

    Future.microtask(() => context.read<HomeBloc>().add(GetActivityEvent(
          params: ActivityParams(
            subuserUid: context.read<AuthBloc>().currentUser?.defaultSubuser?.uid ?? "",
          ),
        )));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (ctx, state) {
        switch (state.status) {
          case HomeStatus.initial:
          case HomeStatus.loading:
            return const Loader();

          case HomeStatus.failure:
            return IndividualActivityFailureCard();

          case HomeStatus.success:
            return IndividualActivitySuccesCard(entity: state.entity);
        }
      },
    );
  }
}
