import 'package:flutter/cupertino.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../core/app_config/app_border_radius.dart';
import '../../../../../core/app_config/app_gaps.dart';
import '../../../../subuser/presentation/widgets/individual/home_card.dart';
import '../../../../subuser/presentation/widgets/individual/home_card_line_widget.dart';
import 'individual_activity_item.dart';
import 'individual_activity_show_more_button.dart';

class IndividualActivityFailureCard extends StatelessWidget {
  const IndividualActivityFailureCard({super.key});

  static int get getTodayIndex {
    int todayWeekday = DateTime.now().weekday;

    return todayWeekday - 1;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: context.paddingMediumHorizontal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 0,
        children: [
          HomeCardLineWidget(title: "Weekly Activity"),
          AppGaps.instance.gapVS8,
          HomeCard(
            child: Padding(
              padding: context.paddingDisplay,
              child: ClipRRect(
                borderRadius: AppBorderRadius.circularSize16Radius(),
                child: Column(
                  spacing: 0,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: List.generate(7, (index) {
                        final isDone = [false, false, false, false, false, false, false]
                            .elementAtOrNull(index);
                        final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
                        final isToday = index == getTodayIndex;

                        return IndividualActivityItem(
                          day: days[index],
                          isDone: isDone,
                          isToday: isToday,
                        );
                      }),
                    ),
                    AppGaps.instance.gapVS8,
                    IndividualActivityShowMoreButton(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
