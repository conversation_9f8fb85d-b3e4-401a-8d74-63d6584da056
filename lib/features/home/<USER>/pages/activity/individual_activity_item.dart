// ignore_for_file: no-equal-arguments

import 'package:flutter/material.dart';

import '../../../../../core/app_config/app_colors.dart';
import '../../../../../core/app_config/app_gaps.dart';
import '../../../../../core/app_config/app_text_styles.dart';
import '../../../../../core/enum/assets_enums.dart';

class IndividualActivityItem extends StatelessWidget {
  const IndividualActivityItem({this.day, this.isDone, this.isToday, super.key});
  // Constants for magic numbers.
  static const _itemSize = 36.0;
  static const _iconSize = 24.0;

  static const _dotIndicatorSize = 8.0;

  final bool? isDone;
  final String? day;
  final bool? isToday;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: 0,
      children: [
        Text(
          day ?? "",
          style: AppTextStyles.activityItemTitle.copyWith(
            color: ((isDone ?? false) || (isToday ?? false))
                ? AppColors.neoBlue
                : AppColors.activityItemBackground,
          ),
        ),
        AppGaps.instance.gapVS8,
        Container(
          decoration: BoxDecoration(
            border: (isToday ?? false) ? Border.all(color: AppColors.neoBlue, width: 2) : null,
            gradient: (isDone ?? false)
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    colors: AppColors.activityBorderLinear.colors,
                    end: Alignment.bottomRight,
                  )
                : LinearGradient(
                    begin: Alignment.topLeft,
                    colors: [Colors.grey.shade200, Colors.grey.shade300],
                    end: Alignment.bottomRight,
                  ),
            shape: BoxShape.circle,
          ),
          height: _itemSize,
          width: _itemSize,
          child: Center(
            child: (isDone ?? false)
                ? AssetsEnums.icSLogo.toSvg(height: _iconSize, width: _iconSize)
                : Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade400,
                      shape: BoxShape.circle,
                    ),
                    height: _dotIndicatorSize,
                    width: _dotIndicatorSize,
                  ),
          ),
        ),
      ],
    );
  }
}
