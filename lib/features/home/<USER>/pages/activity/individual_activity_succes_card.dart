// ignore_for_file: prefer-for-loop-in-children, prefer-extracting-callbacks

import 'package:flutter/cupertino.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../core/app_config/app_border_radius.dart';
import '../../../../../core/app_config/app_gaps.dart';
import '../../../../subuser/presentation/widgets/individual/home_card.dart';
import '../../../../subuser/presentation/widgets/individual/home_card_line_widget.dart';
import '../../../domain/entities/activity_entity.dart';
import 'individual_activity_item.dart';
import 'individual_activity_show_more_button.dart';

class IndividualActivitySuccesCard extends StatelessWidget {
  const IndividualActivitySuccesCard({this.entity, super.key});
  final List<ActivityEntity>? entity;

  static int get getTodayIndex {
    int todayWeekday = DateTime.now().weekday;

    return todayWeekday - 1;
  }

  @override
  Widget build(BuildContext context) {
    final substringLength = 3;

    final todayIndex = getTodayIndex;

    return Padding(
      padding: context.paddingMediumHorizontal,
      child: HomeCard(
        child: Padding(
          padding: context.paddingDisplay,
          child: Column(
            children: [
              HomeCardLineWidget(title: "Weekly Activity"),
              AppGaps.instance.gapVS8,
              ClipRRect(
                borderRadius: AppBorderRadius.circularSize16Radius(),
                child: Column(
                  spacing: 0,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: List.generate(entity?.length ?? 0, (index) {
                        final activityEntity = entity?[index];
                        final isToday = index == todayIndex;

                        return IndividualActivityItem(
                          day: activityEntity?.day?.characters
                              .getRange(0, substringLength)
                              .toString(),
                          isDone: activityEntity?.isUsed,
                          isToday: isToday,
                        );
                      }),
                    ),
                    AppGaps.instance.gapVS8,
                    IndividualActivityShowMoreButton(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
