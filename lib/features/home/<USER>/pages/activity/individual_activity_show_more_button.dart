import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../../../../../core/app_config/app_strings.dart';
import '../../../../../core/app_config/app_text_styles.dart';
import '../../../../../core/navigator/routes/app_route.dart';
import '../../../../../core/remote_config/hrv_page_config.dart';
import '../../../../../core/remote_config/remote_config_service.dart';
import '../../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../../subuser/presentation/pages/subuser_detail_view.dart';

class IndividualActivityShowMoreButton extends StatefulWidget {
  const IndividualActivityShowMoreButton({super.key});

  @override
  State<IndividualActivityShowMoreButton> createState() => _IndividualActivityShowMoreButtonState();
}

class _IndividualActivityShowMoreButtonState extends State<IndividualActivityShowMoreButton> {
  static void _handleOnTap(BuildContext context) {
    final currentUser = context.read<AuthBloc>().currentUser;

    final subuser = currentUser?.defaultSubuser;
    final isIndividual = currentUser?.userType == AppStrings.individualType;
    if (!isIndividual) return;
    if (subuser != null) {
      const individuals = 'individuals';

      unawaited(AppRoute.pushNewScreen(
        context,
        SubuserDetailView(
          isHistory: true,
          subuser: subuser,
          userType: individuals,
        ),
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    final HrvPageConfig(:hrMeasurement, :lastMeasurement, :showMore) =
        GetIt.instance.get<RemoteConfigService>().hrvPageConfig;

    return Column(
      children: [
        Divider(),
        Center(
          child: InkWell(
            onTap: () => _handleOnTap(context),
            child: Text(
              showMore,
              style: AppTextStyles.timePickerSaveButton.copyWith(fontWeight: FontWeight.normal),
            ),
          ),
        ),
      ],
    );
  }
}
