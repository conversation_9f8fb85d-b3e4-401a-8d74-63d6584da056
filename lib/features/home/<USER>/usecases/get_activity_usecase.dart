import 'package:fpdart/fpdart.dart';
import 'package:vagustimpro/core/exception/failure.dart';
import 'package:vagustimpro/core/usecase/usecase.dart';
import 'package:vagustimpro/features/home/<USER>/entities/activity_entity.dart';
import 'package:vagustimpro/features/home/<USER>/params/activity_params.dart';
import 'package:vagustimpro/features/home/<USER>/repositories/activity_repositories.dart';

class GetActivityUsecase implements UseCase<List<ActivityEntity>, ActivityParams> {
  final ActivityRepositories activityRepositories;

  const GetActivityUsecase(this.activityRepositories);

  @override
  Future<Either<Failure, List<ActivityEntity>>> calculate(ActivityParams params) {
    return activityRepositories.getActivity(params: params);
  }
}
