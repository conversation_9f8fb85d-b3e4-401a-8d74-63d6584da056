import 'package:intl/intl.dart';
import 'package:vagustimpro/features/home/<USER>/entities/activity_entity.dart';

class ActivityParams extends ActivityEntity {
  // Get the start date of the current week.
  String get getWeekStartDate {
    final format = "yyyy-MM-dd";

    DateTime now = DateTime.now();

    // Calculate the number of days from Monday.
    int daysFromMonday = now.weekday - 1;

    // Calculate the start date of the current week.
    DateTime monday = now.subtract(Duration(days: daysFromMonday));

    return DateFormat(format).format(monday);
  }

  const ActivityParams({super.subuserUid, super.weekStartDate});


}
