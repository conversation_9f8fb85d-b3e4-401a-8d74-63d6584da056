import 'package:fpdart/fpdart.dart';
import 'package:vagustimpro/features/home/<USER>/entities/activity_entity.dart';
import 'package:vagustimpro/features/home/<USER>/params/activity_params.dart';
import 'package:vagustimpro/features/home/<USER>/repositories/activity_repositories.dart';

import '../../../../core/exception/failure.dart';
import '../datasource/home_data_source.dart';

class ActivityRepositoryImpl extends ActivityRepositories {
  final HomeDataSource homeDataSource;

  ActivityRepositoryImpl(this.homeDataSource);

  @override
  Future<Either<Failure, List<ActivityEntity>>> getActivity({
    required ActivityParams params,
  }) async {
    try {
      final result = await homeDataSource.getActivity(params: params);

      return Right(result);
    } catch (error) {
      return Left(Failure(error.toString()));
    }
  }
}
