// ignore_for_file: avoid-non-null-assertion, no-equal-arguments

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/parameter/domain/params/session_type.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';

import '../../../../../../core/navigator/routes/app_route.dart';
import '../../../../../../core/navigator/routes/constants/route_constants.dart';
import '../../../../../app/flavor/flavor_config.dart';
import '../../../../../core/app_config/app_dialog_close_button.dart';
import '../../../../../core/app_config/app_strings.dart';
import '../../../../../core/app_config/app_text_styles.dart';
import '../../../../parameter/presentation/pages/new_adjust_parameters_page.dart';
import '../../../../parameter/presentation/pages/widgets/preset_programs/preset_program_view.dart';
import '../../../../parameter/presentation/pages/widgets/select_session_type/session_option_data.dart';
import '../../../../parameter/presentation/widgets/modern_session_options.dart';
import '../../../../stimulation/presentation/bloc/stimulation_bloc.dart';
import '../../../../time_of_usage/presentation/widgets/app_dialog.dart';
import '../../../domain/entities/multiple_device_entity.dart';

class MultipleDeviceSelectSessionTypeView extends StatefulWidget {
  const MultipleDeviceSelectSessionTypeView({
    required this.device,
    this.isContinuousStimulation,
    super.key,
    required this.subuser,
  });

  final SubuserEntity? subuser;
  final bool? isContinuousStimulation;
  final MultipleDeviceEntity device;

  @override
  State<MultipleDeviceSelectSessionTypeView> createState() =>
      _MultipleDeviceSelectSessionTypeViewState();
}

class _MultipleDeviceSelectSessionTypeViewState extends State<MultipleDeviceSelectSessionTypeView>
    with SingleTickerProviderStateMixin {
  // Constants.
  static const _kAnimationDuration = 1000;

  static const _kAnimationIntervalStart = 0.15;
  static const _kAnimationIntervalEnd = 0.5;
  static const _kSlideOffset = 0.2;
  static const _kAppDialogHeight = 0.8;

  static const _kAiDrivenIndex = 0;
  static const _kPresetProgramsIndex = 1;
  static const _kAdjustParametersIndex = 2; // String constants.
  static const _kAiDrivenTitle = 'AI-Driven Protocol';
  static const _kAiDrivenDescription = 'Personalized sessions powered by artificial intelligence';
  static const _kPresetProgramsTitle = 'Programs';
  static const _kPresetProgramsDescription = 'Choose from our expert-designed programs';

  static const _kAdjustParametersTitle = 'Adjust Your Own Parameters';
  static const _kAdjustParametersDescription =
      'Fine-tune every aspect of your session'; // Color constants.

  static void _handleAiDrivenTap() {
    debugPrint('AI-Driven Protocol tapped');
  }

  final _bundleId =
      GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

  AnimationController? _animationController;
  List<Animation<double>>? _fadeAnimations;

  List<Animation<Offset>>? _slideAnimations;

  List<SessionOptionData> _sessionOptions = <SessionOptionData>[];

  @override
  void initState() {
    super.initState();
    _initializeSessionOptions();
    _initializeAnimations();
  }

  void _initializeSessionOptions() {
    // Create a new list instead of mutating the existing one.
    final sessionOptions = [
      SessionOptionData(
        backgroundColor: AppColors.aiDrivenBgColor,
        description: _kAiDrivenDescription,
        iconBackgroundColor: AppColors.aiDrivenIconBgColor,
        iconColor: AppColors.aiDrivenIconBgColor,
        imagePath: AssetsEnums.icAiDriven.assetSvg,
        title: _kAiDrivenTitle,
      ),
      SessionOptionData(
        backgroundColor: AppColors.presetProgramsBgColor,
        description: _kPresetProgramsDescription,
        iconBackgroundColor: AppColors.presetProgramsIconBgColor,
        iconColor: AppColors.presetProgramsIconBgColor,
        imagePath: AssetsEnums.icPresetPrograms.assetSvg,
        title: _kPresetProgramsTitle,
      ),
      SessionOptionData(
        backgroundColor: AppColors.adjustParametersBgColor,
        description: _kAdjustParametersDescription,
        iconBackgroundColor: AppColors.adjustParametersIconBgColor,
        iconColor: AppColors.adjustParametersIconBgColor,
        imagePath: AssetsEnums.icAdjustYourParameters.assetSvg,
        title: _kAdjustParametersTitle,
      ),
    ];

    _sessionOptions = [];
    _sessionOptions.addAll(sessionOptions);
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: Duration(milliseconds: _kAnimationDuration.toInt()),
      vsync: this,
    );

    _createFadeAnimations();
    _createSlideAnimations();

    unawaited(_animationController?.forward());
  }

  void _createFadeAnimations() {
    _fadeAnimations = List.generate(
      _sessionOptions.length,
      (index) => Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
        curve: Interval(
          index * _kAnimationIntervalStart,
          _kAnimationIntervalEnd + index * _kAnimationIntervalStart,
          curve: Curves.easeOut,
        ),
        parent: _animationController!,
      )),
    );
  }

  void _createSlideAnimations() {
    _slideAnimations = List.generate(
      _sessionOptions.length,
      (index) => Tween<Offset>(
        begin: const Offset(0, _kSlideOffset),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        curve: Interval(
          index * _kAnimationIntervalStart,
          _kAnimationIntervalEnd + index * _kAnimationIntervalStart,
          curve: Curves.easeOutCubic,
        ),
        parent: _animationController!,
      )),
    );
  }

  void _navigateToPresetProgramView() async {
    if (FlavorConstants.isHealthPro(_bundleId)) {
      final response = await AppDialog(
        context: context,
        insetPadding: context.paddingMediumHorizontal,
        child: SizedBox(
          height: context.height * _kAppDialogHeight,
          width: context.width,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              AppDialogCloseButton(),
              Center(
                child: Text(_kPresetProgramsTitle, style: AppTextStyles.multipleDeviceDetailTitle),
              ),
              Expanded(
                child: PresetProgramView(
                  isContinuousStimulation: context.read<StimulationBloc>().currentSessionType ==
                          SessionType.preSetPrograms
                      ? widget.isContinuousStimulation
                      : false,
                ),
              ),
            ],
          ),
        ),
      ).show;

      Navigator.of(context).pop(response);
    } else {
      unawaited(AppRoute.pushNewScreen(
        context,
        PresetProgramView(
          isContinuousStimulation:
              context.read<StimulationBloc>().currentSessionType == SessionType.preSetPrograms
                  ? widget.isContinuousStimulation
                  : false,
        ),
      ));
    }
  }

  void _navigateToAdjustParametersPage() async {
    if (FlavorConstants.isHealthPro(_bundleId)) {
      if (widget.subuser == null) return;

      final response = await AppDialog(
        contentPadding: EdgeInsets.zero,
        context: context,
        insetPadding: EdgeInsets.zero,
        child: SizedBox(
          height: context.height,
          width: context.width,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Padding(
                padding: context.paddingMedium,
                child: AppDialogCloseButton(),
              ),
              Center(
                child:
                    Text(_kAdjustParametersTitle, style: AppTextStyles.multipleDeviceDetailTitle),
              ),
              Expanded(child: NewAdjustParametersPage(subuser: widget.subuser!)),
            ],
          ),
        ),
      ).show;

      Navigator.of(context).pop(response);
    } else {
      AppRoute.pushNamed(
        context,
        RouteConstants.adjustParametersPage,
        arguments: widget.subuser,
      );
    }
  }

  void _handleOptionTap(int index) {
    switch (index) {
      case _kAiDrivenIndex:
        _handleAiDrivenTap();
        break;

      case _kPresetProgramsIndex:
        _navigateToPresetProgramView();
        break;

      case _kAdjustParametersIndex:
        _navigateToAdjustParametersPage();
        break;
    }
  }

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: context.height * _kAppDialogHeight,
      width: context.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          AppDialogCloseButton(),
          Center(
            child: Text("Select Session Type", style: AppTextStyles.multipleDeviceDetailTitle),
          ),
          AppGaps.instance.gapVS16,
          Expanded(
            child: ListView.separated(
              itemBuilder: (ctx, index) {
                return FadeTransition(
                  opacity:
                      _fadeAnimations?.elementAtOrNull(index) ?? const AlwaysStoppedAnimation(1.0),
                  child: SlideTransition(
                    position: _slideAnimations?.elementAtOrNull(index) ??
                        const AlwaysStoppedAnimation(Offset.zero),
                    child: ModernSessionOptions(
                      backgroundColor: _sessionOptions[index].backgroundColor,
                      description: _sessionOptions[index].description,
                      iconBackgroundColor: _sessionOptions[index].iconBackgroundColor,
                      iconColor: _sessionOptions[index].iconColor,
                      imagePath: _sessionOptions[index].imagePath,
                      isComingSoon:
                          _sessionOptions[index].imagePath == AssetsEnums.icAiDriven.assetSvg,
                      isRequiredPremium: _sessionOptions[index].isRequiredPremium,
                      onTap: () => _handleOptionTap(index),
                      title: _sessionOptions[index].title,
                    ),
                  ),
                );
              },
              itemCount: _sessionOptions.length,
              separatorBuilder: (ctx, index) => AppGaps.gapH12,
            ),
          ),
        ],
      ),
    );
  }
}
