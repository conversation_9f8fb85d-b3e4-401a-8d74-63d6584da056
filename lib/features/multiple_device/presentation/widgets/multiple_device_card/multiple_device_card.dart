// ignore_for_file: avoid-long-functions, avoid-long-parameter-list

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:get_it/get_it.dart';
import 'package:provider/provider.dart';
import 'package:vagustimpro/core/ble/ble_methods.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/extension/iterable_extension.dart';
import 'package:vagustimpro/features/multiple_device/domain/entities/device_status_enum.dart';
import 'package:vagustimpro/features/multiple_device/presentation/bloc/multiple_device_bloc.dart';
import 'package:vagustimpro/features/multiple_device/presentation/widgets/ble_connect_button/multiple_device_connect_button.dart';
import 'package:vagustimpro/features/multiple_device/presentation/widgets/multiple_device_detail/multiple_device_detail_session_type_field.dart';
import 'package:vagustimpro/features/multiple_device/presentation/widgets/multiple_device_item/multiple_device_item_forward_icon.dart';
import 'package:vagustimpro/features/multiple_device/presentation/widgets/multiple_device_select_session_type/multiple_device_select_session_type_view.dart';
import '../../../../../app/flavor/flavor_config.dart';
import '../../../../../core/app_config/app_colors.dart';
import '../../../../../core/app_config/app_gaps.dart';
import '../../../../../core/app_config/app_strings.dart';
import '../../../../../core/ble/ble_device_connector.dart';
import '../../../../../core/ble/ble_scanner.dart';
import '../../../../../core/ble/models/ble_scanner_state.dart';
import '../../../../../core/custom_widgets/common_button.dart';
import '../../../../../core/navigator/app_navigator.dart';
import '../../../../../core/navigator/routes/app_route.dart';
import '../../../../../core/navigator/routes/constants/route_constants.dart';
import '../../../../../core/navigator/stimulation_control_page_args.dart';
import '../../../../parameter/domain/params/session_type.dart';
import '../../../../parameter/domain/params/session_type_params.dart';
import '../../../../parameter/presentation/pages/widgets/preset_programs/preset_program.dart';
import '../../../../stimulation/presentation/widgets/utils/device_streams.dart';
import '../../../../subuser/domain/entities/subuser_entity.dart';
import '../../../../subuser/presentation/bloc/subuser_bloc.dart';
import '../../../../time_of_usage/presentation/widgets/app_dialog.dart';
import '../../../domain/entities/multiple_device_entity.dart';
import '../../bloc/multiple_device_event.dart';
import '../../bloc/multiple_device_state.dart';
import '../multiple_device_detail/multiple_device_detail_view.dart';
import '../multiple_device_item/multiple_device_item_device_name_text.dart';
import '../multiple_device_item/multiple_device_item_person_name_text.dart';
import '../select_users/multiple_device_select_users_view.dart';
part '../ble_connect_button/mixin/multiple_device_connect_button_mixin.dart';
part '../ble_connect_button/mixin/stimulation_control_mixin.dart';
part '../ble_connect_button/mixin/device_connection_mixin.dart';
part '../ble_connect_button/mixin/device_scanner_mixin.dart';
part '../ble_connect_button/mixin/device_button_properties_mixin.dart';
part '../ble_connect_button/multiple_device_control_button.dart';

class MultipleDeviceCard extends StatefulWidget {
  const MultipleDeviceCard({required this.device, super.key});
  final MultipleDeviceEntity device;

  @override
  State<MultipleDeviceCard> createState() => _MultipleDeviceCardState();
}

class _MultipleDeviceCardState extends State<MultipleDeviceCard>
    with
        MultipleDeviceConnectButtonMixin,
        DeviceScannerMixin,
        DeviceConnectionMixin,
        StimulationControlMixin,
        DeviceButtonPropertiesMixin {
  void _handleConnectButtonOnTap(
    BleDeviceConnector bleDeviceConnector,
    BleScanner bleScanner,
    BleStatus? bleStatus,
    ConnectionStateUpdate connectionStateUpdate,
    MultipleDeviceEntity? currentDevice,
    MultipleDeviceState state,
  ) {
    if (bleStatus == BleStatus.poweredOff) {
      BleMethods.handleBleStatusPoweredOff();

      return;
    }
    if (bleStatus != BleStatus.ready) {
      BleMethods.handleBleReady();

      return;
    }
    if (isControlLimitConnected(state)) return;
    if (isDeviceConnected(currentDevice)) return;

    final deviceId = widget.device.deviceId;
    if ((deviceId ?? "").isEmpty) return;

    final isAvailableConnect =
        _isAvailableConnect(connectionStateUpdate, context, widget.device, state);
    if (!isAvailableConnect) return;

    startScan(bleDeviceConnector, bleScanner: bleScanner, state: state);
  }

  @override
  Widget build(BuildContext context) {
    final stateMultiDeviceBloc = context.watch<MultipleDeviceBloc>().state;

    return Consumer4<BleDeviceConnector, ConnectionStateUpdate, BleScanner, BleStatus?>(
      builder: (
        ctx,
        bleDeviceConnector,
        connectionStateUpdate,
        bleScanner,
        bleStatus,
        child,
      ) {
        debugPrint(
          "Multiple Product Card Connection State: ${connectionStateUpdate.connectionState} ${connectionStateUpdate.deviceId}",
        );
        handleDeviceConnected(connectionStateUpdate, context: ctx, state: stateMultiDeviceBloc);
        handleDeviceDisconnected(
          connectionStateUpdate,
          context: ctx,
          device: widget.device,
          state: stateMultiDeviceBloc,
        );

        return BlocBuilder<MultipleDeviceBloc, MultipleDeviceState>(
          buildWhen: (previous, current) => !listEquals(previous.devices, current.devices),
          builder: (ctxBloc, state) {
            final currentDevice = state.devices
                ?.firstWhereOrNull((element) => element.deviceId == widget.device.deviceId);

            return InkWell(
              onTap: () => _handleItemOnTap(ctxBloc, currentDevice ?? widget.device),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 0,
                    children: [
                      MultipleDeviceItemDeviceNameText(deviceName: currentDevice?.deviceName ?? ""),
                      gapVS8,
                      MultipleDeviceItemPersonNameText(
                        selectedSubuser: currentDevice?.selectedSubuser,
                      ),
                      gapVS8,
                      MultipleDeviceConnectButton(
                        buttonProps: getButtonProperties(device: widget.device, state: state),
                        device: currentDevice ?? widget.device,
                        isDisabled: isDisabled(bleStatus, currentEntity: currentDevice),
                        isDisabledWithOnTap: isDisabledWithOnTap(bleStatus),
                        onPress: () => _handleConnectButtonOnTap(
                          bleDeviceConnector,
                          bleScanner,
                          bleStatus,
                          connectionStateUpdate,
                          currentDevice,
                          state,
                        ),
                      ),
                    ],
                  ),
                  if (isDeviceConnected(currentDevice) && currentDevice != null)
                    MultipleDeviceItemForwardIcon(device: currentDevice),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
