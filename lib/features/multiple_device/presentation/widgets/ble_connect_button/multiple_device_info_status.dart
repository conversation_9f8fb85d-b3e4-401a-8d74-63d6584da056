// ignore_for_file: avoid-non-null-assertion

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/extension/iterable_extension.dart';
import 'package:vagustimpro/features/multiple_device/domain/entities/multiple_device_entity.dart';
import 'package:vagustimpro/features/multiple_device/presentation/bloc/multiple_device_bloc.dart';
import 'package:vagustimpro/features/multiple_device/presentation/bloc/multiple_device_event.dart';
import 'package:vagustimpro/features/stimulation/presentation/bloc/stimulation_bloc.dart';

import '../../../../stimulation/presentation/widgets/utils/device_streams.dart';
import '../../../domain/entities/device_status_enum.dart';
import '../../bloc/multiple_device_state.dart';
import '../../../../../core/custom_widgets/simple_percentage_indicator.dart';

class MultipleDeviceInfoStatus extends StatefulWidget {
  const MultipleDeviceInfoStatus({required this.buttonProps, required this.device, super.key});
  final MultipleDeviceEntity? device;
  final Map<String, dynamic> buttonProps;

  @override
  State<MultipleDeviceInfoStatus> createState() => _MultipleDeviceInfoStatusState();
}

class _MultipleDeviceInfoStatusState extends State<MultipleDeviceInfoStatus> {
  final _percentage = 100.0;
  final _size = 32;
  final _paddingValue = 4.0;

  final maxValue = 100.0;

  void _checkCompletion(double clampedPercentage, BuildContext ctx) {
    if (clampedPercentage < _percentage) return;

    final bloc = ctx.read<MultipleDeviceBloc>();
    final currentEntity =
        bloc.state.devices?.firstWhereOrNull((element) => element.bleId == widget.device?.bleId);

    if (currentEntity != null && currentEntity.deviceStatus != DeviceStatusEnum.completed) {
      bloc.add(ChangeConnectedDeviceStatus(
        device: currentEntity,
        status: DeviceStatusEnum.completed,
      ));
    }
  }

  double _processProgressData(BuildContext ctx, String resultData) {
    final totalTime = (widget.device?.totalDuration ?? 0);
    final splitList = resultData.split('_').toList();

    final timeLeftString = splitList.elementAtOrNull(4)?.trim();
    final timeLeft = int.tryParse(timeLeftString ?? "") ?? 0;

    final percentageCompleted = _percentage - ((timeLeft / (totalTime).toInt()) * _percentage);
    final clampedPercentage = percentageCompleted.clamp(0.0, _percentage);

    debugPrint('clampedPercentage: $clampedPercentage');
    _checkCompletion(clampedPercentage, ctx);

    return widget.device?.deviceStatus == DeviceStatusEnum.completed ? maxValue : clampedPercentage;
  }

  @override
  Widget build(BuildContext context) {
    final bleId = widget.device?.bleId;

    if ((bleId ?? "").isEmpty) return const SizedBox.shrink();

    return BlocBuilder<MultipleDeviceBloc, MultipleDeviceState>(
      builder: (_, __) {
        return BlocBuilder<StimulationBloc, StimulationState>(
          buildWhen: (previous, current) => current is StimulationTimeAndIntensityReadSuccess,
          builder: (_, __) {
            return Padding(
              padding: EdgeInsets.only(left: _paddingValue),
              child: StreamBuilder<List<int>>(
                builder: (ctx, snapshot) {
                  final AsyncSnapshot<List<int>>(:connectionState, :data, :hasData) = snapshot;

                  if (hasData && connectionState == ConnectionState.active && data != null) {
                    final resultData = String.fromCharCodes(data);
                    final currentPercentage = _processProgressData(ctx, resultData);

                    return SimplePercentageIndicator(
                      percentage: currentPercentage,
                      progressColor: widget.buttonProps['textColor'],
                      size: _size.sp,
                    );
                  }

                  return const SizedBox.shrink();
                },
                stream: DeviceStreams(bleId!).stimulationTimeAndIntensity,
              ),
            );
          },
        );
      },
    );
  }
}
