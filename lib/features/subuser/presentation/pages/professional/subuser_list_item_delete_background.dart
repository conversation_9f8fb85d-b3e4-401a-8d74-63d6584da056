import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../core/app_config/app_colors.dart';
import '../../../../../core/enum/assets_enums.dart';

class SubuserListItemDeleteBackground extends StatelessWidget {
  const SubuserListItemDeleteBackground({super.key});

  @override
  Widget build(BuildContext context) {
    final radius = 28;
    final size = 24;
    final opacity = 0.3;

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Padding(
          padding: context.paddingMediumHorizontal,
          child: CircleAvatar(
            backgroundColor:
                AppColors.multipleDevicesStatusInfoBackground.withValues(alpha: opacity),
            radius: radius.sp,
            child: AssetsEnums.icDelete.toSvg(height: size.sp, width: size.sp),
          ),
        ),
      ],
    );
  }
}
