// ignore_for_file: avoid-similar-names

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';

class HomeCard extends StatelessWidget {
  const HomeCard({this.child, this.height, super.key});
  final Widget? child;
  final double? height;

  @override
  Widget build(BuildContext context) {
    final blurRadius = 20.0;
    final offset = const Offset(0, 8);
    final shadowOpacity = 0.05;
    final gradientOpacity = 0.9;
    final gradientOpacity2 = 0.7;
    final blurRadius2 = 40.0;
    final offset2 = const Offset(0, 16);
    final shadowOpacity2 = 0.1;

    return Container(
      decoration: BoxDecoration(
        borderRadius: AppBorderRadius.circularSize12Radius(),
        boxShadow: [
          BoxShadow(
            blurRadius: blurRadius,
            color: AppColors.individualHomeStoryBackgroundColor.withValues(alpha: shadowOpacity),
            offset: offset,
          ),
          BoxShadow(
            blurRadius: blurRadius2,
            color: AppColors.neoBlue.withValues(alpha: shadowOpacity2),
            offset: offset2,
          ),
        ],
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          colors: [
            AppColors.pureWhite.withValues(alpha: gradientOpacity),
            AppColors.pureWhite.withValues(alpha: gradientOpacity2),
          ],
          end: Alignment.bottomRight,
        ),
      ),
      height: height,
      child: child,
    );
  }
}
