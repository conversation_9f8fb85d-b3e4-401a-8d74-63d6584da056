import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import '../../../../../core/app_config/app_colors.dart';
import '../../../../../core/app_config/app_text_styles.dart';

class HomeCardLineWidget extends StatelessWidget {
  const HomeCardLineWidget({super.key, this.title});
  final String? title;

  @override
  Widget build(BuildContext context) {
    /* const height = 24.0;
    const width = 4.0;*/
    const letterSpacing = -0.5;

    return Row(
      spacing: 0,
      children: [
        /* Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(2)),
            gradient: AppColors.activityBorderLinear,
          ),
          height: height,
          width: width,
        ),
        const SizedBox(width: 12),*/
        Text(
          title ?? "",
          style: AppTextStyles.individualHomeStoryTitle.copyWith(
            letterSpacing: letterSpacing,
          ),
        ),
      ],
    );
  }
}

class ModernSectionDivider extends StatelessWidget {
  const ModernSectionDivider({
    this.color,
    this.gradient,
    this.horizontalPadding = 16.0,
    super.key,
    this.textStyle,
    this.thickness = 1.0,
    required this.title,
  });

  final String title;
  final TextStyle? textStyle;
  final double? thickness;
  final Color? color;
  final Gradient? gradient;

  final double horizontalPadding;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: gradient == null ? (color ?? Theme.of(context).dividerColor) : null,
              gradient: gradient ?? AppColors.activityBorderLinear,
            ),
            height: thickness,
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Text(
            title,
            style: textStyle ?? Theme.of(context).textTheme.titleSmall,
          ),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: gradient == null ? (color ?? Theme.of(context).dividerColor) : null,
              gradient: gradient ?? AppColors.activityBorderLinear,
            ),
            height: thickness,
          ),
        ),
      ],
    );
  }
}

// Option 1: Icon + Divider + Text + Divider + Icon.
class PremiumSectionDivider extends StatelessWidget {
  const PremiumSectionDivider({
    this.dividerColor,
    super.key,
    this.leftIcon,
    this.rightIcon,
    this.textStyle,
    this.thickness = 1.2,
    required this.title,
  });

  final String title;
  final TextStyle? textStyle;
  final IconData? leftIcon;
  final IconData? rightIcon;
  final Color? dividerColor;

  final double thickness;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (leftIcon != null)
          Icon(
            leftIcon,
            color: dividerColor ?? AppColors.darkTextColor.withOpacity(0.6),
            size: 16,
          ),
        if (leftIcon != null) const SizedBox(width: 8),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(thickness / 2),
              color: dividerColor ?? AppColors.darkTextColor.withOpacity(0.8),
            ),
            height: thickness,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(title, style: textStyle),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(thickness / 2),
              color: dividerColor ?? AppColors.darkTextColor.withOpacity(0.8),
            ),
            height: thickness,
          ),
        ),
        if (rightIcon != null) const SizedBox(width: 8),
        if (rightIcon != null)
          Icon(
            rightIcon,
            color: dividerColor ?? AppColors.darkTextColor.withOpacity(0.6),
            size: 16,
          ),
      ],
    );
  }
}

// Option 2: Box container ile modern.
class BoxedSectionTitle extends StatelessWidget {
  const BoxedSectionTitle({
    this.backgroundColor,
    this.borderColor,
    super.key,
    this.textStyle,
    required this.title,
  });

  final String title;
  final TextStyle? textStyle;
  final Color? backgroundColor;

  final Color? borderColor;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            color: borderColor ?? AppColors.darkTextColor.withOpacity(0.7),
            height: 1.5,
          ),
        ),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: borderColor ?? AppColors.darkTextColor.withOpacity(0.7),
              width: 1.2,
            ),
            borderRadius: const BorderRadius.all(Radius.circular(20)),
            color: backgroundColor ?? AppColors.extraLightGray,
          ),
          margin: const EdgeInsets.symmetric(horizontal: 12),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(title, style: textStyle),
        ),
        Expanded(
          child: Container(
            color: borderColor ?? AppColors.darkTextColor.withOpacity(0.7),
            height: 1.5,
          ),
        ),
      ],
    );
  }
}

// Option 3: Sharp geometric divider.
class SharpSectionDivider extends StatelessWidget {
  const SharpSectionDivider({
    this.accentColor,
    super.key,
    this.textStyle,
    required this.title,
  });

  final String title;
  final TextStyle? textStyle;

  final Color? accentColor;

  @override
  Widget build(BuildContext context) {
    final color = accentColor ?? AppColors.neoBlue;

    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color, color.withOpacity(0.1)],
              ),
            ),
            height: 2,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(title, style: textStyle),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [color.withOpacity(0.1), color],
              ),
            ),
            height: 2,
          ),
        ),
      ],
    );
  }
}

// Option 4: Gradient border ile ultra modern.
class GradientBorderTitle extends StatelessWidget {
  const GradientBorderTitle({
    super.key,
    this.textStyle,
    required this.title,
  });

  final String title;

  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.activityBorderLinear,
            ),
            height: 2,
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(16)),
            gradient: AppColors.activityBorderLinear,
          ),
          margin: const EdgeInsets.symmetric(horizontal: 8),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          child: Text(
            title,
            style: textStyle?.copyWith(color: Colors.white) ??
                TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.w600),
          ),
        ),
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.activityBorderLinear,
            ),
            height: 2,
          ),
        ),
      ],
    );
  }
}
