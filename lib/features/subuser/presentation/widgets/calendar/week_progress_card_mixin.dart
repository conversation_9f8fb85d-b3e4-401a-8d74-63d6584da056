// ignore_for_file: avoid-non-null-assertion, avoid-long-functions, avoid-similar-names, avoid-unrelated-type-casts, avoid-type-casts

part of 'week_progress_card.dart';

// Constants.
abstract final class _WeekProgressConstants {
  static const detailsTitle = 'Details';
  static const defaultRangeDays = 7;
  static const hourInDay = 12;
  static const animationDuration = Duration(milliseconds: 300);
  static const animationCurve = Curves.easeIn;
}

mixin WeekProgressCardMixin on State<WeekProgressCard> {
  // State variables.
  final _selectedEvents = ValueNotifier<List<CalendarEvent>>([]);

  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now().toLocal();
  DateTime? _rangeStart;
  DateTime? _rangeEnd;

  LinkedHashMap<DateTime, List<CalendarEvent>> _kEvents =
      LinkedHashMap<DateTime, List<CalendarEvent>>();

  // Animation variables - No late!
  AnimationController? _animationController;
  Animation<double>? _fadeAnimation;

  // Getters for safe access.
  AnimationController? get animationController => _animationController;
  Animation<double>? get fadeAnimation => _fadeAnimation;

  // Calendar Helper Access.
  CalendarHelper get _calendarHelper => (this as CalendarHelper);

  // Initialization methods.
  void initializeAnimations() {
    _animationController = AnimationController(
      duration: _WeekProgressConstants.animationDuration,
      vsync: (this as TickerProviderStateMixin),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      curve: _WeekProgressConstants.animationCurve,
      parent: animationController!,
    ));
  }

  void initializeCalendar() {
    setupEvents();
    initializeTimeOfUsage();
    initializeSurveyIfNeeded();
    setDefaultDateRange();
  }

  void setupEvents() {
    _kEvents = LinkedHashMap<DateTime, List<CalendarEvent>>(
      equals: isSameDay,
      hashCode: _calendarHelper.handleGetHashCode,
    )..addAll(_calendarHelper.generateEvents(context));
  }

  void initializeTimeOfUsage() {
    context.read<TimeOfUsageBloc>().add(SetTimeOfUsageEvent(_kEvents));
  }

  void initializeSurveyIfNeeded() {
    if (shouldInitializeSurvey) {
      context.read<SurveyBloc>().add(
            GetSubuserScoresEvent(
              histories: _kEvents,
              subuserId: widget.subuser!.uid!,
            ),
          );
    }
  }

  bool get shouldInitializeSurvey => !widget.isHistory && (widget.subuser?.uid ?? "").isNotEmpty;

  void setDefaultDateRange() {
    final now = DateTime.now();
    final startDateTime =
        now.subtract(const Duration(days: _WeekProgressConstants.defaultRangeDays));
    final initialStartDateTime =
        widget.firstDay.isBefore(startDateTime) ? startDateTime : widget.firstDay;

    final focusedDay = now;
    handleRangeSelected(end: now, focusedDay: focusedDay, start: initialStartDateTime);
  }

  // Event handlers.
  void onStimulationListener() {
    initializeCalendar();
  }

  void handleRangeSelected({
    required DateTime? end,
    required DateTime? focusedDay,
    required DateTime? start,
  }) {
    setState(() {
      _rangeStart = start;
      _rangeEnd = end;
      _focusedDay = focusedDay ?? _focusedDay;
      updateSelectedEvents(end: end, start: start);
    });

    unawaited(animationController?.forward(from: 0.0));
  }

  void updateSelectedEvents({DateTime? end, DateTime? start}) {
    if (start != null && end != null) {
      updateSelectedEventsForRange(end: end, start: start);
    } else if (start != null) {
      updateSelectedEventsForSingleDay(start);
    } else {
      _selectedEvents.value = [];
    }
  }

  void updateSelectedEventsForRange({required DateTime end, required DateTime start}) {
    final selectedRangeEvents = <CalendarEvent>[];
    for (DateTime day = start; _isDayInRange(day, end); day = day.add(const Duration(days: 1))) {
      selectedRangeEvents.addAll(_calendarHelper.handleGetEventsForDay(day: day, events: _kEvents));
    }
    selectedRangeEvents.sort((item, item2) => item2.createdAt.compareTo(item.createdAt));
    _selectedEvents.value = selectedRangeEvents;
  }

  /// Checks if the given day is within the range (inclusive of end date).
  bool _isDayInRange(DateTime day, DateTime end) {
    return day.isBefore(end) || isSameDay(day, end);
  }

  void updateSelectedEventsForSingleDay(DateTime start) {
    final startEvents = _calendarHelper.handleGetEventsForDay(day: start, events: _kEvents);
    startEvents.sort((item1, item2) => item2.createdAt.compareTo(item1.createdAt));
    _selectedEvents.value = startEvents;
  }

  void handleOnFormatChanged(CalendarFormat format) {
    if (_calendarFormat != format) {
      setState(() => _calendarFormat = format);
    }
  }

  void handleOnPageChanged(DateTime focusedDay) {
    _focusedDay = focusedDay;
  }

  void handleBlocListener() {
    onStimulationListener();
  }

  // Builder methods.
  Widget get handleBuildContent {
    return SingleChildScrollView(
      child: Padding(
        padding: context.paddingMedium.copyWith(bottom: 0, top: 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 0,
          children: [
            buildCalendar,
            AppGaps.instance.gapVS12,
            buildDetailsSection,
          ],
        ),
      ),
    );
  }

  Widget get buildCalendar {
    final kToday = DateTime.now().toLocal();

    final DateTime(:day, :month, :year) = kToday;

    final kLastDay = DateTime(year, month, day, _WeekProgressConstants.hourInDay).toUtc();

    return HistoryCard(
      child: TableCalendar<CalendarEvent>(
        availableGestures: AvailableGestures.horizontalSwipe,
        calendarBuilders: buildCalendarBuilders,
        calendarFormat: _calendarFormat,
        calendarStyle: buildCalendarStyle,
        currentDay: DateTime.now(),
        daysOfWeekStyle: buildDaysOfWeekStyle,
        enabledDayPredicate: (dayPredicate) => dayPredicate.isBefore(kLastDay),
        eventLoader: (dayEvent) =>
            _calendarHelper.handleGetEventsForDay(day: dayEvent, events: _kEvents),
        firstDay: widget.firstDay,
        focusedDay: _focusedDay,
        headerStyle: buildHeaderStyle,
        lastDay: kLastDay,
        onPageChanged: handleOnPageChanged,
        onRangeSelected: (start, end, focusedDay) => handleRangeSelected(
          end: end,
          focusedDay: focusedDay,
          start: start,
        ),
        rangeEndDay: _rangeEnd,
        rangeSelectionMode: RangeSelectionMode.toggledOn,
        rangeStartDay: _rangeStart,
        startingDayOfWeek: StartingDayOfWeek.monday,
      ),
    );
  }

  CalendarBuilders<CalendarEvent> get buildCalendarBuilders {
    return CalendarBuilders(
      markerBuilder: (context, day, events) => handleBuildMarker(
        day: day,
        events: events,
      ),
      outsideBuilder: (context, day, focusedDay) => const SizedBox.shrink(),
      rangeEndBuilder: (context, day, focusedDay) {
        final events = _calendarHelper.handleGetEventsForDay(day: day, events: _kEvents);
        final hasEvents = events.isNotEmpty;

        return HistoryRangeEnd(day: day.day.toString(), hasEvents: hasEvents);
      },
      rangeHighlightBuilder: (context, day, isWithinRange) {
        final events = _calendarHelper.handleGetEventsForDay(day: day, events: _kEvents);
        final hasEvents = events.isNotEmpty;

        return handleBuildRangeHighlight(
          day: day,
          hasEvents: hasEvents,
          isWithinRange: isWithinRange,
        );
      },
      rangeStartBuilder: (context, day, focusedDay) {
        final events = _calendarHelper.handleGetEventsForDay(day: day, events: _kEvents);
        final hasEvents = events.isNotEmpty;

        return HistoryRangeStart(day: day.day.toString(), hasEvents: hasEvents);
      },
    );
  }

  Widget handleBuildMarker({
    required DateTime day,
    required List<CalendarEvent> events,
  }) {
    final isSelected = isSameDay(day, _focusedDay);
    final isDateInRange = _isDateInRange(day);

    return events.isNotEmpty && !isSelected && !isDateInRange
        ? HistoryMarker(day: day.day.toString())
        : const SizedBox.shrink();
  }

  Widget handleBuildRangeHighlight({
    required DateTime day,
    required bool hasEvents,
    required bool isWithinRange,
  }) {
    final isRangeStart = isSameDay(day, _rangeStart);
    final isRangeEnd = isSameDay(day, _rangeEnd);

    return isWithinRange
        ? HistoryRangeHighlight(
            day: day.day.toString(),
            hasEvents: hasEvents,
            isRangeEnd: isRangeEnd,
            isRangeStart: isRangeStart,
          )
        : const SizedBox.shrink();
  }

  CalendarStyle get buildCalendarStyle {
    final weekendTextStyle = AppTextStyles.historyCalendarValue;
    final weekNumberTextStyle = weekendTextStyle;
    final defaultTextStyle = weekNumberTextStyle;
    final disabledTextStyle = defaultTextStyle;

    final rangeStartTextStyle = AppTextStyles.historyRange;
    final rangeEndTextStyle = rangeStartTextStyle;
    final withinRangeTextStyle = rangeEndTextStyle;

    final disableOpacity = 0.5;

    return CalendarStyle(
      defaultTextStyle: defaultTextStyle,
      disabledTextStyle: disabledTextStyle.copyWith(
        color: AppColors.historyCalendarValue.withValues(alpha: disableOpacity),
      ),
      rangeEndTextStyle: rangeEndTextStyle,
      rangeStartTextStyle: rangeStartTextStyle,
      todayDecoration: const BoxDecoration(color: Colors.transparent),
      todayTextStyle: AppTextStyles.historyValue.copyWith(fontWeight: FontWeight.bold),
      weekNumberTextStyle: weekNumberTextStyle,
      weekendTextStyle: weekendTextStyle,
      withinRangeTextStyle: withinRangeTextStyle,
    );
  }

  DaysOfWeekStyle get buildDaysOfWeekStyle {
    final weekdayStyle = AppTextStyles.historyCalendarWeek;
    final weekendStyle = weekdayStyle;

    return DaysOfWeekStyle(
      weekdayStyle: weekdayStyle,
      weekendStyle: weekendStyle,
    );
  }

  HeaderStyle get buildHeaderStyle {
    final headerMargin = EdgeInsets.zero;
    final headerPadding = headerMargin;

    return HeaderStyle(
      formatButtonShowsNext: false,
      formatButtonVisible: false,
      headerMargin: headerMargin,
      headerPadding: headerPadding,
      titleCentered: true,
      titleTextStyle: AppTextStyles.historyCalendarMonth,
    );
  }

  Widget get buildDetailsSection {
    return Padding(
      padding: context.paddingLowHorizontal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 0,
        children: [
          Text(
            _WeekProgressConstants.detailsTitle,
            style: AppTextStyles.individualHomeStoryTitle,
          ),
          AppGaps.instance.gapVS12,
          buildEventsList,
        ],
      ),
    );
  }

  Widget get buildEventsList {
    return HistoryEventList(
      animationController: animationController,
      fadeAnimation: _fadeAnimation,
      selectedEvents: _selectedEvents,
    );
  }

  // Utility methods.
  bool _isDateInRange(DateTime day) {
    if (_rangeStart == null || _rangeEnd == null) return false;

    return day.isAfter(_rangeStart!.subtract(const Duration(days: 1))) &&
        day.isBefore(_rangeEnd!.add(const Duration(days: 1)));
  }

  @override
  void dispose() {
    disposeResources();
    super.dispose();
  }

  void disposeResources() {
    _selectedEvents.dispose();
    _animationController?.stop();
    _animationController?.dispose();
    _animationController = null;
  }
}
