import 'dart:async';
import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/calendar/calendar_event.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/calendar/calendar_helper.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/calendar/widgets/history_range_end.dart';
import 'package:vagustimpro/features/survey/presentation/bloc/survey_bloc.dart';
import 'package:vagustimpro/features/time_of_usage/presentation/bloc/time_of_usage_bloc.dart';

import '../../../../../core/app_config/app_colors.dart';
import '../../../../survey/presentation/bloc/survey_event.dart';
import 'widgets/history_card.dart';
import 'widgets/history_event_list.dart';
import 'widgets/history_marker.dart';
import 'widgets/history_range_highlight.dart';
import 'widgets/history_range_start.dart';
import 'widgets/week_progress_card_inherited_widget.dart';

part 'week_progress_card_mixin.dart';

class WeekProgressCard extends StatefulWidget {
  const WeekProgressCard({
    required this.firstDay,
    this.isHistory = false,
    super.key,
    this.padding,
    this.subuser,
  });

  final DateTime firstDay;
  final SubuserEntity? subuser;
  final bool isHistory;
  final EdgeInsets? padding;

  @override
  State<WeekProgressCard> createState() => _WeekProgressCardState();
}

class _WeekProgressCardState extends State<WeekProgressCard>
    with CalendarHelper, WeekProgressCardMixin, TickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    handleBlocListener();
    initializeAnimations();
    initializeCalendar();
  }

  @override
  Widget build(BuildContext context) {
    return WeekProgressCardInheritedWidget(
      mixin: this,
      child: handleBuildContent,
    );
  }
}
