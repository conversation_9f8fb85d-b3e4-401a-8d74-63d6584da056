import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import '../../../../../core/app_config/app_strings.dart';
import '../../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../../stimulation/presentation/bloc/stimulation_bloc.dart';
import '../premium_button_widget.dart';
import '../subuser_week_progress_card.dart';

class WeekProgressCardWrapper extends StatefulWidget {
  const WeekProgressCardWrapper({super.key});

  @override
  State<WeekProgressCardWrapper> createState() => _WeekProgressCardWrapperState();
}

class _WeekProgressCardWrapperState extends State<WeekProgressCardWrapper> {
  @override
  void initState() {
    super.initState();

    Future.microtask(() {
      final subuserUid = context.read<AuthBloc>().currentUser?.defaultSubuser?.uid;

      if ((subuserUid ?? "").isNotEmpty) {
        context.read<StimulationBloc>().add(StimulationGetSubuserStimulationsEvent(subuserUid!));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final user = context.read<AuthBloc>().currentUser;

    final isIndividual = user?.userType == AppStrings.individualType;
    if (!isIndividual) return SizedBox();

    return (user?.defaultSubuser == null)
        ? SizedBox()
        : Scaffold(
            body: SafeArea(
              bottom: false,
              child: SingleChildScrollView(
                child: Column(
                  spacing: 0,
                  children: [
                    PremiumButtonWidget(
                      padding: context.paddingMedium.copyWith(bottom: 0, left: 0, right: 0),
                    ),
                    SubuserWeekProgressCard(isHistory: true, subuser: user?.defaultSubuser),
                  ],
                ),
              ),
            ),
          );
  }
}
