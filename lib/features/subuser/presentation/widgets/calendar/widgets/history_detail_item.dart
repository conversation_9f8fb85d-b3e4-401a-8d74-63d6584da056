import 'package:flutter/widgets.dart';

import '../../../../../../core/app_config/app_gaps.dart';
import '../../../../../../core/app_config/app_text_styles.dart';

class HistoryDetailItem extends StatelessWidget {
  const HistoryDetailItem(
      {this.fontSize, this.icon, super.key, this.subtitle, this.title, this.titleStyle});
  final String? title;
  final String? subtitle;
  final Widget? icon;
  final double? fontSize;
  final TextStyle? titleStyle;

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 0,
      children: [
        icon ?? const SizedBox(),
        AppGaps.instance.gapHS8,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if ((subtitle ?? "").isNotEmpty)
              Text(subtitle ?? "", style: AppTextStyles.historyDetailSubtitle),
            Text(
              title ?? "",
              overflow: TextOverflow.ellipsis,
              style: titleStyle ?? AppTextStyles.historyDetailTitle.copyWith(fontSize: fontSize),
            ),
          ],
        ),
      ],
    );
  }
}
