import 'package:flutter/material.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../../../core/app_config/app_border_radius.dart';
import '../../../../../../../core/app_config/app_colors.dart';
import '../../../../../../../core/app_config/app_text_styles.dart';

class HistoryMarker extends StatelessWidget {
  const HistoryMarker({this.day, super.key});

  final String? day;

  @override
  Widget build(BuildContext context) {
    final marginValue = 2.0;
    final margin = context.paddingLow / marginValue;
    final radius = 4.0;

    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: AppBorderRadius.circularSize8Radius(),
            color: AppColors.historyFaded,
          ),
          margin: margin,
        ),
        Text((day ?? ""), style: AppTextStyles.historyValue),
        Positioned(
          right: 0,
          top: 0,
          child: Padding(
            padding: context.paddingLow,
            child: CircleAvatar(
              backgroundColor: AppColors.historyCircleBackground,
              radius: radius,
            ),
          ),
        ),
      ],
    );
  }
}
