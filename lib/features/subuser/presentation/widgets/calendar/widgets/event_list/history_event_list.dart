import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/home_card.dart';

import '../../../../../../../core/app_config/app_gaps.dart';
import '../../../../../../../core/app_config/app_text_styles.dart';
import '../../../../../../../core/app_config/app_strings.dart';
import '../../calendar_event.dart';
import '../card/history_card.dart';
import '../detail/history_detail_item_default_type.dart';
import '../detail/history_detail_with_preset_item.dart';

part 'staggered_animation_item.dart';
part 'history_event_default_details.dart';
part 'history_event_list_constants.dart';

class HistoryEventList extends StatefulWidget {
  const HistoryEventList({super.key, required this.selectedEvents});

  final ValueNotifier<List<CalendarEvent>> selectedEvents;

  @override
  State<HistoryEventList> createState() => _HistoryEventListState();
}

class _HistoryEventListState extends State<HistoryEventList> {
  String _currentListKey = '';

  static String _generateListKey(List<CalendarEvent> events) {
    // Generate unique key based on list content and length.
    final contentHash =
        events.map((event) => event.hashCode).join(_HistoryEventListConstants.keySeparator);
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    final keyBuffer = StringBuffer()
      ..write(events.length)
      ..write(_HistoryEventListConstants.keySeparator)
      ..write(contentHash)
      ..write(_HistoryEventListConstants.keyContentSeparator)
      ..write(timestamp);

    return keyBuffer.toString();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<CalendarEvent>>(
      builder: (ctx, events, _) {
        if (events.isEmpty) {
          return HomeCard(
            child: SizedBox(
              width: ctx.width,
              child: Padding(
                padding: ctx.paddingDisplay.copyWith(
                  bottom: ctx.paddingMedium.bottom,
                  top: ctx.paddingMedium.top,
                ),
                child: Text(
                  _HistoryEventListConstants.emptyStateMessage,
                  style: AppTextStyles.historyDetailSubtitle.copyWith(
                    fontSize: _HistoryEventListConstants.emptyStateFontSize.sp,
                  ),
                ),
              ),
            ),
          );
        }

        // Generate new key for each list change.
        final newListKey = _generateListKey(events);
        if (_currentListKey != newListKey) {
          _currentListKey = newListKey;
        }

        return Padding(
          padding: ctx.paddingOnlyBottomBar,
          child: ListView.separated(
            itemBuilder: (listContext, itemIndex) {
              return _StaggeredAnimationItem(
                index: itemIndex,
                key: ValueKey(
                  '$_currentListKey${_HistoryEventListConstants.keySeparator}$itemIndex',
                ),
                child: _HistoryEventItem(event: events[itemIndex]),
              );
            },
            itemCount: events.length,
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (separatorContext, separatorIndex) => AppGaps.instance.gapVS16,
            shrinkWrap: true,
          ),
        );
      },
      valueListenable: widget.selectedEvents,
    );
  }
}
