// ignore_for_file: prefer-single-widget-per-file

part of 'history_event_list.dart';

/// Stateless widget for displaying default session details.
class _HistoryEventDefaultDetails extends StatelessWidget {
  const _HistoryEventDefaultDetails({required this.event});

  final CalendarEvent event;

  @override
  Widget build(BuildContext context) {
    return HistoryDetailItemDefaultType(event: event);
  }
}

/// Stateless widget for displaying event details based on session type.
class _HistoryEventDetails extends StatelessWidget {
  const _HistoryEventDetails({required this.event});

  final CalendarEvent event;

  @override
  Widget build(BuildContext context) {
    final isDefaultSession =
        (event.sessionType ?? '') == _HistoryEventListConstants.defaultSessionType;

    return isDefaultSession
        ? _HistoryEventDefaultDetails(event: event)
        : HistoryDetailWithPresetItem(event: event);
  }
}

/// Stateless widget for displaying an event item with expansion tile.
class _HistoryEventItem extends StatelessWidget {
  const _HistoryEventItem({required this.event});

  final CalendarEvent event;

  @override
  Widget build(BuildContext context) {
    return HistoryCard(
      padding: EdgeInsets.zero,
      child: ExpansionTile(
        shape: const Border(),
        title: Text(event.createdAt, style: AppTextStyles.historyDetailTitle),
        children: [
          Padding(
            padding: context.paddingMedium,
            child: _HistoryEventDetails(event: event),
          ),
        ],
      ),
    );
  }
}
