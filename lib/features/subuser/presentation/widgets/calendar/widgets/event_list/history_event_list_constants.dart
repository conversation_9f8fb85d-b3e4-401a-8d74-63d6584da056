part of 'history_event_list.dart';

/// Constants for history event list widget.
abstract final class _HistoryEventListConstants {
  // UI text constants.
  static const emptyStateMessage = 'Please select a date to access session information.';

  // Session type constants.
  static const defaultSessionType = AppStrings.defaultKey;

  // Animation constants.
  static const animationDuration = Duration(milliseconds: 800);
  static const staggerDelayMultiplier = 120;

  // Animation values.
  static const slideBeginValue = 80.0;
  static const slideEndValue = 0.0;
  static const fadeBeginValue = 0.0;
  static const fadeEndValue = 1.0;
  static const scaleBeginValue = 0.8;
  static const scaleEndValue = 1.0;

  // UI sizing constants.
  static const emptyStateFontSize = 14.0;

  // String constants for key generation.
  static const keySeparator = '_';
  static const keyContentSeparator = '-';
}
