part of 'history_event_list.dart';

class _StaggeredAnimationItem extends StatefulWidget {
  const _StaggeredAnimationItem({
    required this.child,
    required this.index,
    super.key,
  });

  final int index;
  final Widget child;

  @override
  State<_StaggeredAnimationItem> createState() => _StaggeredAnimationItemState();
}

class _StaggeredAnimationItemState extends State<_StaggeredAnimationItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Animation<double>? _slideAnimation;
  Animation<double>? _fadeAnimation;
  Animation<double>? _scaleAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: _HistoryEventListConstants.animationDuration,
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: _HistoryEventListConstants.slideBeginValue,
      end: _HistoryEventListConstants.slideEndValue,
    ).animate(CurvedAnimation(
      curve: Curves.easeOutCubic,
      parent: _controller,
    ));

    _fadeAnimation = Tween<double>(
      begin: _HistoryEventListConstants.fadeBeginValue,
      end: _HistoryEventListConstants.fadeEndValue,
    ).animate(CurvedAnimation(curve: Curves.easeOut, parent: _controller));

    _scaleAnimation = Tween<double>(
      begin: _HistoryEventListConstants.scaleBeginValue,
      end: _HistoryEventListConstants.scaleEndValue,
    ).animate(CurvedAnimation(
      curve: Curves.easeOutBack,
      parent: _controller,
    ));

    // Sequential startup - each item starts 120ms later.
    _startAnimation();
  }

  void _startAnimation() {
    final delay =
        Duration(milliseconds: widget.index * _HistoryEventListConstants.staggerDelayMultiplier);
    // Delay animation start to create staggered effect for list items.
    unawaited(Future.delayed(delay, _handleDelayedAnimation));
  }

  /// Handles the delayed animation start for staggered effect.
  void _handleDelayedAnimation() {
    if (mounted && !_controller.isCompleted) {
      unawaited(_controller.forward());
    }
  }

  @override
  void didUpdateWidget(_StaggeredAnimationItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Restart animation when widget is updated.
    if (oldWidget.index != widget.index || oldWidget.key != widget.key) {
      _controller.reset();
      _startAnimation();
    }
  }

  @override
  void dispose() {
    _controller.stop();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (ctx, child) {
        return Transform.translate(
          offset: Offset(_slideAnimation?.value ?? 1, 0),
          child: Transform.scale(
            scale: _scaleAnimation?.value,
            child: Opacity(
              opacity: _fadeAnimation?.value ?? 1,
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}
