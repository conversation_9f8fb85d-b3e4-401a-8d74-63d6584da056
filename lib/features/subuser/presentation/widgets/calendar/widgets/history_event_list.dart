import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/home_card.dart';

import '../../../../../../core/app_config/app_gaps.dart';
import '../../../../../../core/app_config/app_text_styles.dart';
import '../calendar_event.dart';
import 'history_card.dart';
import 'history_detail_item_default_type.dart';
import 'history_detail_with_preset_item.dart';

class HistoryEventList extends StatefulWidget {
  const HistoryEventList({
    this.animationController,
    this.fadeAnimation,
    super.key,
    required this.selectedEvents,
  });

  final AnimationController? animationController;
  final Animation<double>? fadeAnimation;
  final ValueNotifier<List<CalendarEvent>> selectedEvents;

  @override
  State<HistoryEventList> createState() => _HistoryEventListState();
}

class _HistoryEventListState extends State<HistoryEventList> {
  String _currentListKey = '';

  static Widget _buildDefaultSessionDetails(CalendarEvent event) {
    return HistoryDetailItemDefaultType(event: event);
  }

  static Widget _buildEventDetails(CalendarEvent event) {
    const defaultSessionType = 'default';
    final isDefaultSession = (event.sessionType ?? "") == defaultSessionType;

    return isDefaultSession
        ? _buildDefaultSessionDetails(event)
        : HistoryDetailWithPresetItem(event: event);
  }

  static Widget handleBuildEventItem(BuildContext context, CalendarEvent event) {
    return HistoryCard(
      padding: EdgeInsets.zero,
      child: ExpansionTile(
        shape: const Border(),
        title: Text(event.createdAt, style: AppTextStyles.historyDetailTitle),
        children: [
          Padding(
            padding: context.paddingMedium,
            child: _buildEventDetails(event),
          ),
        ],
      ),
    );
  }

  static String _generateListKey(List<CalendarEvent> events) {
    // Liste içeriği ve uzunluğuna göre unique key oluştur.
    final contentHash = events.map((e) => e.hashCode).join('_');
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return '${events.length}_${contentHash}_$timestamp';
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<CalendarEvent>>(
      builder: (ctx, events, _) {
        if (events.isEmpty) {
          final emptyText = "Please select a date to access session information.";
          final fontSize = 14.0;

          return HomeCard(
            child: SizedBox(
              width: ctx.width,
              child: Padding(
                padding: ctx.paddingDisplay
                    .copyWith(bottom: ctx.paddingMedium.bottom, top: ctx.paddingMedium.top),
                child: Text(
                  emptyText,
                  style: AppTextStyles.historyDetailSubtitle.copyWith(fontSize: fontSize.sp),
                ),
              ),
            ),
          );
        }

        // Her liste değişikliğinde yeni key oluştur.
        final newListKey = _generateListKey(events);
        if (_currentListKey != newListKey) {
          _currentListKey = newListKey;
        }

        return Padding(
          padding: ctx.paddingOnlyBottomBar,
          child: ListView.separated(
            itemBuilder: (context, index) {
              return _StaggeredAnimationItem(
                index: index,
                key: ValueKey('${_currentListKey}_$index'),
                child: handleBuildEventItem(context, events[index]),
              );
            },
            itemCount: events.length,
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (context, index) => AppGaps.instance.gapVS16,
            shrinkWrap: true,
          ),
        );
      },
      valueListenable: widget.selectedEvents,
    );
  }
}

class _StaggeredAnimationItem extends StatefulWidget {
  const _StaggeredAnimationItem({
    required this.child,
    required this.index,
    super.key,
  });

  final int index;
  final Widget child;

  @override
  State<_StaggeredAnimationItem> createState() => _StaggeredAnimationItemState();
}

class _StaggeredAnimationItemState extends State<_StaggeredAnimationItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Animation<double>? _slideAnimation;
  Animation<double>? _fadeAnimation;
  Animation<double>? _scaleAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<double>(begin: 80.0, end: 0.0).animate(CurvedAnimation(
      curve: Curves.easeOutCubic,
      parent: _controller,
    ));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
      curve: Curves.easeOut,
      parent: _controller,
    ));

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(CurvedAnimation(
      curve: Curves.easeOutBack,
      parent: _controller,
    ));

    // Sıralı başlatma - her item 120ms sonra başlar.
    _startAnimation();
  }

  void _startAnimation() {
    final delay = Duration(milliseconds: widget.index * 120);
    Future.delayed(delay, () {
      if (mounted && !_controller.isCompleted) {
        _controller.forward();
      }
    });
  }

  @override
  void didUpdateWidget(_StaggeredAnimationItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Widget güncellendiğinde animasyonu yeniden başlat.
    if (oldWidget.index != widget.index || oldWidget.key != widget.key) {
      _controller.reset();
      _startAnimation();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (ctx, child) {
        return Transform.translate(
          offset: Offset(_slideAnimation?.value ?? 1, 0),
          child: Transform.scale(
            scale: _scaleAnimation?.value,
            child: Opacity(
              opacity: _fadeAnimation?.value ?? 1,
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}
