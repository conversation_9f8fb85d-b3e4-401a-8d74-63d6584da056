import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/home_card.dart';

import '../../../../../../core/app_config/app_gaps.dart';
import '../../../../../../core/app_config/app_text_styles.dart';
import '../calendar_event.dart';
import 'history_card.dart';
import 'history_detail_item_default_type.dart';
import 'history_detail_with_preset_item.dart';

class HistoryEventList extends StatefulWidget {
  const HistoryEventList({
    this.animationController,
    this.fadeAnimation,
    super.key,
    required this.selectedEvents,
  });

  final AnimationController? animationController;
  final Animation<double>? fadeAnimation;
  final ValueNotifier<List<CalendarEvent>> selectedEvents;

  @override
  State<HistoryEventList> createState() => _HistoryEventListState();
}

class _HistoryEventListState extends State<HistoryEventList> {
  List<bool> _isExpandedList = <bool>[];

  static Widget _buildDefaultSessionDetails(CalendarEvent event) {
    return HistoryDetailItemDefaultType(event: event);
  }

  static Widget _buildEventDetails(CalendarEvent event) {
    const defaultSessionType = 'default';

    final isDefaultSession = (event.sessionType ?? "") == defaultSessionType;

    return isDefaultSession
        ? _buildDefaultSessionDetails(event)
        : HistoryDetailWithPresetItem(event: event);
  }

  static Widget handleBuildEventItem(BuildContext context, CalendarEvent event) {
    return HistoryCard(
      padding: EdgeInsets.zero,
      child: ExpansionTile(
        shape: const Border(),
        title: Text(event.createdAt, style: AppTextStyles.historyDetailTitle),
        children: [
          Padding(
            padding: context.paddingMedium,
            child: _buildEventDetails(event),
          ),
        ],
      ),
    );
  }

  void _updateExpandedListIfNeeded(List<CalendarEvent> events) {
    if (_isExpandedList.length != events.length) {
      _isExpandedList = List<bool>.generate(events.length, (_) => false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<CalendarEvent>>(
      builder: (ctx, events, _) {
        if (events.isEmpty) {
          final emptyText = "Please select a date to access session information.";
          final fontSize = 14.0;

          return HomeCard(
            child: SizedBox(
              width: ctx.width,
              child: Padding(
                padding: ctx.paddingDisplay
                    .copyWith(bottom: ctx.paddingMedium.bottom, top: ctx.paddingMedium.top),
                child: Text(
                  emptyText,
                  style: AppTextStyles.historyDetailSubtitle.copyWith(fontSize: fontSize.sp),
                ),
              ),
            ),
          );
        }

        _updateExpandedListIfNeeded(events);

        return FadeTransition(
          opacity: widget.fadeAnimation ?? const AlwaysStoppedAnimation(1.0),
          child: Padding(
            padding: ctx.paddingOnlyBottomBar,
            child: ListView.separated(
              itemBuilder: (ctxList, index) => handleBuildEventItem(ctxList, events[index]),
              itemCount: events.length,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (ctxSeparator, index) => AppGaps.instance.gapVS16,
              shrinkWrap: true,
            ),
          ),
        );
      },
      valueListenable: widget.selectedEvents,
    );
  }
}
