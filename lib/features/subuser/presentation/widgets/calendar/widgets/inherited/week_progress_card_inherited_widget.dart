import 'package:flutter/material.dart';

import '../../calendar_helper.dart';

class WeekProgressCardInheritedWidget extends InheritedWidget {
  const WeekProgressCardInheritedWidget({
    required super.child,
    super.key,
    required this.mixin,
  });

  static WeekProgressCardInheritedWidget of(BuildContext context) {
    final result = context.dependOnInheritedWidgetOfExactType<WeekProgressCardInheritedWidget>();
    assert(result != null, 'No NewAdjustParametersInheritedWidget found in context');

    return result!;
  }

  final CalendarHelper mixin;

  @override
  bool updateShouldNotify(WeekProgressCardInheritedWidget oldWidget) => mixin != oldWidget.mixin;
}
