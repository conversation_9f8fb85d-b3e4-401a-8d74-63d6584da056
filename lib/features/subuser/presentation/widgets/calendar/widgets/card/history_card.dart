// ignore_for_file: avoid-similar-names

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

class HistoryCard extends StatelessWidget {
  const HistoryCard({this.child, this.height, super.key, this.padding});
  final Widget? child;
  final double? height;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) {
    final blurRadius = 4.0;
    final offset = const Offset(0, 0);
    final shadowOpacity = 0.25;

    return Container(
      decoration: BoxDecoration(
        borderRadius: AppBorderRadius.circularSize12Radius(),
        boxShadow: [
          BoxShadow(
            blurRadius: blurRadius,
            color: AppColors.individualHomeStoryBackgroundColor.withValues(alpha: shadowOpacity),
            offset: offset,
          ),
        ],
        color: AppColors.historyCardBackground,
      ),
      height: height,
      padding: padding ?? context.paddingMedium,
      child: child,
    );
  }
}
