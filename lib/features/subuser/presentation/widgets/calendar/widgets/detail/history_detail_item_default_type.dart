// ignore_for_file: no-magic-string, prefer-class-destructuring, prefer-moving-to-variable

import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';

import '../../../../../../../core/app_config/app_gaps.dart';
import '../../../../../../../core/enum/assets_enums.dart';
import '../../../../../../../core/remote_config/remote_config_service.dart';
import '../../calendar_event.dart';
import 'history_detail_item.dart';

class HistoryDetailItemDefaultType extends StatefulWidget {
  const HistoryDetailItemDefaultType({required this.event, super.key});

  final CalendarEvent event;

  @override
  State<HistoryDetailItemDefaultType> createState() => _HistoryDetailItemDefaultTypeState();
}

class _HistoryDetailItemDefaultTypeState extends State<HistoryDetailItemDefaultType> {
  static String _formatDuration(int duration) {
    const bleDisconnectedText = 'BLE Disconnected';

    if (duration == 0) return bleDisconnectedText;

    final durationValue = 60;

    final minutes = duration ~/ durationValue;
    final seconds = duration % durationValue;

    return '$minutes m ${seconds.toStringAsFixed(0)} s';
  }

  final _zeroDurationFontSize = 10.0;

  final _onDurationText = "On Duration";

  final _offDurationText = "Off Duration";

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final historyFeatureConfig = remoteConfigService.historyFeatureConfig;

    final CalendarEvent(
      :duration,
      :durationOff,
      :durationOn,
      :frequency,
      :pulseWidth,
    ) = widget.event;

    final frequencyTitle = '$frequency ${historyFeatureConfig.hzUnit}';
    final pulseWidthTitle = '$pulseWidth ${historyFeatureConfig.usUnit}';

    final historyPageConfig = GetIt.instance<RemoteConfigService>().historyFeatureConfig;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 0,
          children: [
            HistoryDetailItem(
              fontSize: duration == 0 ? _zeroDurationFontSize.sp : null,
              icon: AssetsEnums.icTime.toSvg(),
              subtitle: historyPageConfig.sessionDuration,
              title: _formatDuration(duration),
            ),
            AppGaps.instance.gapVS12,
            HistoryDetailItem(
              icon: AssetsEnums.icPower.toSvg(),
              subtitle: _onDurationText,
              title: '$durationOn s',
            ),
          ],
        ),
        AppGaps.instance.gapHS8,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 0,
          children: [
            HistoryDetailItem(
              icon: AssetsEnums.icFrequency.toSvg(),
              subtitle: historyPageConfig.frequency,
              title: frequencyTitle,
            ),
            AppGaps.instance.gapVS12,
            HistoryDetailItem(
              icon: AssetsEnums.icOffDuration.toSvg(),
              subtitle: _offDurationText,
              title: '$durationOff s',
            ),
          ],
        ),
        Expanded(
          child: HistoryDetailItem(
            icon: AssetsEnums.icPulseWidth.toSvg(),
            subtitle: historyPageConfig.pulseWidth,
            title: pulseWidthTitle,
          ),
        ),
      ],
    );
  }
}
