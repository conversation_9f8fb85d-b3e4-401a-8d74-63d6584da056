import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';

import '../../../../../../../../core/app_config/app_colors.dart';
import '../../../../../../../../core/enum/assets_enums.dart';
import '../../../../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../../parameter/presentation/pages/widgets/preset_programs/preset_program.dart';
import '../../calendar_event.dart';
import 'history_detail_item.dart';

class HistoryDetailWithPresetItem extends StatelessWidget {
  const HistoryDetailWithPresetItem({required this.event, super.key});
  final CalendarEvent event;

  @override
  Widget build(BuildContext context) {
    final historyPageConfig = GetIt.instance<RemoteConfigService>().historyFeatureConfig;
    final height = 26.0;
    final width = 26.0;
    final durationValue = 60.0;

    final iconSize = height.h;
    final iconWidth = width.w;

    final programText = "Program";
    final bleDisconnected = 'BLE Disconnected';
    final durationText =
        '${(event.duration ~/ durationValue)} m ${(event.duration % durationValue).toStringAsFixed(0)} s';

    return Column(
      spacing: 0,
      children: [
        HistoryDetailItem(
          icon: SvgPicture.asset(
            PresetProgram.presetProgramFromRequestName(
              event.sessionType ?? "",
            ).getIconPath,
            colorFilter: ColorFilter.mode(
              AppColors.historyDetailIcon,
              BlendMode.srcIn,
            ),
            height: iconSize,
            width: iconWidth,
          ),
          subtitle: programText,
          title: PresetProgram.titleFromRequestName(event.sessionType ?? ""),
        ),
        AppGaps.instance.gapVS8,
        HistoryDetailItem(
          icon: AssetsEnums.icTime.toSvg(height: iconSize, width: iconWidth),
          subtitle: historyPageConfig.sessionDuration,
          title: event.duration == 0 ? bleDisconnected : durationText,
        ),
      ],
    );
  }
}
