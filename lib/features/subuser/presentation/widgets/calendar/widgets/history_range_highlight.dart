import 'package:flutter/material.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../../core/app_config/app_border_radius.dart';
import '../../../../../../core/app_config/app_colors.dart';

class HistoryRangeHighlight extends StatelessWidget {
  const HistoryRangeHighlight({
    this.day,
    required this.hasEvents,
    required this.isRangeEnd,
    required this.isRangeStart,
    super.key,
  });

  final String? day;
  final bool isRangeStart;
  final bool isRangeEnd;
  final bool hasEvents;

  @override
  Widget build(BuildContext context) {
    final marginValue = 2.0;
    final radius = 4.0;

    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: isRangeStart
                ? AppBorderRadius.circularOnlyTopLeftBottomLeft()
                : isRangeEnd
                    ? AppBorderRadius.circularOnlyTopRightBottomRight()
                    : null,
            color: AppColors.historyPrimary,
          ),
          margin: context.paddingLow.copyWith(
            bottom: context.paddingLow.bottom / marginValue,
            left: 0,
            right: 0,
            top: context.paddingLow.top / marginValue,
          ),
        ),
        if (hasEvents)
          Positioned(
            right: 0,
            top: 0,
            child: Padding(
              padding: context.paddingLow,
              child: CircleAvatar(
                backgroundColor: AppColors.historyCircleBackground,
                radius: radius,
              ),
            ),
          ),
      ],
    );
  }
}
