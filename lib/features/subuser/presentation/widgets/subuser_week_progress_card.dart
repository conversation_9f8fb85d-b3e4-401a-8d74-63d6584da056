// ignore_for_file: prefer-correct-handler-name

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/custom_widgets/loader.dart';
import '../../../stimulation/presentation/bloc/stimulation_bloc.dart';
import '../../domain/entities/subuser_entity.dart';
import 'calendar/week_progress_card.dart';

class SubuserWeekProgressCard extends StatelessWidget {
  const SubuserWeekProgressCard({this.isHistory, super.key, this.subuser});
  final bool? isHistory;
  final SubuserEntity? subuser;

  @override
  Widget build(BuildContext context) {
    final firstDay = DateTime(2000);

    return BlocBuilder<StimulationBloc, StimulationState>(
      buildWhen: (previous, current) =>
          current is StimulationLoading ||
          current is StimulationGetSubuserStimulationsSuccess ||
          current is DeviceStatusReadSuccess,
      builder: (ctx, state) {
        if (state is StimulationLoading) return const Loader();

        return WeekProgressCard(
          firstDay: firstDay,
          isHistory: isHistory ?? false,
          padding: EdgeInsets.zero,
          subuser: subuser,
        );
      },
    );
  }
}
