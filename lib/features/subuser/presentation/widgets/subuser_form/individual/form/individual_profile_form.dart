// ignore_for_file: prefer-extracting-callbacks

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/age/age_picker.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/gender/gender.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/gender/hexagon_gender_selection.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/heart_about_us/hear_about_us_picker.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/height/height_picker.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/height/height_unit.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/name/profile_name_and_surname.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/purpose_of_usage/purpose_of_usage_picker.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/weight/weight_picker.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/weight/weight_unit.dart';
import '../../../../../domain/entities/subuser_entity.dart';
import '../../../../bloc/subuser_bloc.dart';

part 'individual_profile_form_mixin.dart';

typedef OnEditChanged = void Function(SubuserEditEvent value);

class IndividualProfileForm extends StatefulWidget {
  const IndividualProfileForm({
    this.isEditMode,
    this.isReadOnly,
    this.isSubuser,
    super.key,
    this.onPageChanged,
    this.pageController,
    this.subuser,
  });
  final SubuserEntity? subuser;
  final PageController? pageController;
  final OnEditChanged? onPageChanged;
  final bool? isEditMode;
  final bool? isReadOnly;
  final bool? isSubuser;

  @override
  State<IndividualProfileForm> createState() => _IndividualProfileFormState();
}

class _IndividualProfileFormState extends State<IndividualProfileForm>
    with IndividualProfileFormMixin {
  @override
  Widget build(BuildContext context) {
    final isContainsCm = _heightController.text.contains("cm");
    final heightTextSplit = _heightController.text.split("'");
    final heightControllerSplit = isContainsCm ? null : heightTextSplit.firstOrNull;
    final weightSplit = _weightController.text.split(" ").firstOrNull;

    return PageView(
      controller: widget.pageController,
      onPageChanged: (value) => _handleOnPageChanged(),
      physics: NeverScrollableScrollPhysics(),
      children: [
        ProfileNameAndSurname(
          controller: _nameController,
          handleNameOnChanged: (value) {
            _nameController.text = value;
            _handleOnPageChanged();
          },
          handleSurnameOnChanged: (value) {
            _lastNameController.text = value;
            _handleOnPageChanged();
          },
          isReadOnly: widget.isReadOnly,
          isSubuser: widget.isSubuser,
          surnameController: _lastNameController,
        ),
        HexagonGenderSelector(
          initialGender: _genderController.text == Gender.male.name ? Gender.male : Gender.female,
          isReadOnly: widget.isReadOnly,
          isSubuser: widget.isSubuser,
          onGenderSelected: (gender) => _genderController.text = gender.name.toLowerCase(),
        ),
        AgePicker(
          initialYear: int.tryParse(_ageController.text),
          isReadOnly: widget.isReadOnly,
          isSubuser: widget.isSubuser,
          onYearChanged: (age) => _ageController.text = age.toString(),
        ),
        WeightPicker(
          initialUnit: (_weightController.text).contains('kg') ? WeightUnit.kg : WeightUnit.lbs,
          initialWeight: weightSplit == null ? null : int.tryParse(weightSplit),
          isReadOnly: widget.isReadOnly,
          isSubuser: widget.isSubuser,
          onWeightChanged: (unit, weight) => _weightController.text = ("$weight ${unit.name}"),
        ),
        HeightPicker(
          initialCm: isContainsCm
              ? int.tryParse((_heightController.text.split(" ").firstOrNull ?? ""))
              : null,
          initialFeet: heightControllerSplit == null ? null : int.tryParse(heightControllerSplit),
          initialInches: heightControllerSplit == null
              ? null
              : int.tryParse((heightTextSplit.lastOrNull ?? "").replaceAll('"', '')),
          initialUnit: _heightController.text.contains('cm') ? HeightUnit.cm : HeightUnit.feetInch,
          isReadOnly: widget.isReadOnly,
          isSubuser: widget.isSubuser,
          onCmChanged: (cmValue) {
            _heightController.text = "$cmValue cm";

            _handleOnPageChanged();
          },
          onHeightChanged: (feet, inches) {
            _heightController.text = "$feet' $inches\"";

            _handleOnPageChanged();
          },
        ),
        if (!(widget.isEditMode ?? false) && !(widget.isSubuser ?? false))
          HearAboutUsPicker(
            onValueChanged: (value) {
              _hearAboutUsValues = value;
              _handleOnPageChanged();
            },
            selectedValues: _hearAboutUsValues,
          ),
        PurposeOfUsagePicker(
          isReadOnly: widget.isReadOnly,
          isSubuser: widget.isSubuser,
          onOtherValueChanged: (otherTags) {
            _otherTags = otherTags;
            _handleOnPageChanged();
          },
          onValueChanged: (value) {
            _purposeOfUsageValues = value;
            _handleOnPageChanged();
          },
          otherTags: _otherTags,
          selectedValues: _purposeOfUsageValues,
        ),
      ],
    );
  }
}
