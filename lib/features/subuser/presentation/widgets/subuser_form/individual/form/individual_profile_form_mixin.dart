part of 'individual_profile_form.dart';

mixin IndividualProfileFormMixin on State<IndividualProfileForm> {
  final _defaultAge = 1980;
  final _defaultWeight = "60 kg";
  final _defaultHeight = "160 cm";

  final _nameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _ageController = TextEditingController();

  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  final _genderController = TextEditingController();

  Map<String, bool> _purposeOfUsageValues = <String, bool>{};
  List<String> _otherTags = [];

  Map<String, bool> _hearAboutUsValues = <String, bool>{};

  SubuserEditEvent? _subuserEditEvent;

  @override
  void initState() {
    super.initState();
    final subuser = widget.subuser;

    _nameController.text = subuser?.name ?? "";
    _lastNameController.text = subuser?.surname ?? "";
    _ageController.text = (subuser?.age ?? _defaultAge).toString();
    _heightController.text = (subuser?.height ?? _defaultHeight).toString();
    _weightController.text = (subuser?.weight ?? _defaultWeight).toString();
    _genderController.text = subuser?.gender ?? Gender.male.name;
    _purposeOfUsageValues = subuser?.purposeOfUsage ?? {};
    _hearAboutUsValues = subuser?.hearAboutUs ?? {};
    _otherTags = subuser?.otherPou ?? [];

    if (subuser == null && !(widget.isSubuser ?? false)) {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser?.displayName != null) {
        final displayName = (currentUser?.displayName ?? "");
        final split = displayName.split(' ');
        _nameController.text = split.firstOrNull ?? "";
        if (split.length > 1) {
          _lastNameController.text = split.lastOrNull ?? "";
        }
      }
    }
    _handleOnPageChanged();
  }

  void _handleOnPageChanged() {
    _subuserEditEvent = SubuserEditEvent(
      age: _ageController.text,
      gender: _genderController.text,
      hearAboutUs: _hearAboutUsValues,
      height: _heightController.text,
      name: _nameController.text,
      otherPou: _otherTags,
      purposeOfUsage: _purposeOfUsageValues,
      surname: _lastNameController.text,
      uid: widget.subuser?.uid ?? "",
      weight: _weightController.text,
    );
    final subuserEditEvent = _subuserEditEvent;
    final onPageChanged = widget.onPageChanged;

    if (subuserEditEvent != null && onPageChanged != null) onPageChanged(subuserEditEvent);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _lastNameController.dispose();
    _ageController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    _genderController.dispose();
    super.dispose();
  }
}
