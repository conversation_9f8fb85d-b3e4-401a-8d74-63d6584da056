import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/stimulation_control_exports.dart';

import '../../../../../../app/flavor/flavor_config.dart';
import '../../../../../../core/app_config/app_colors.dart';
import '../../../../../../core/app_config/app_gaps.dart';
import '../../../../../../core/app_config/app_snackbar.dart';
import '../../../../../../core/app_config/app_strings.dart';
import '../../../../../../core/custom_widgets/common_button.dart';
import '../../../../../../core/navigator/routes/app_route.dart';
import '../../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../../../auth/presentation/pages/login/login_page.dart';
import '../../../../../auth/presentation/pages/profile/hrv/other/hrv_profile_bar.dart';
import '../../../../../auth/presentation/pages/profile/profile_calculating/profile_calculating_view.dart';
import '../../../../domain/entities/subuser_entity.dart';
import '../../../bloc/subuser_bloc.dart';
import 'form/individual_profile_form.dart';

part 'individual_profile_view_mixin.dart';

class IndividualProfileView extends StatefulWidget {
  const IndividualProfileView({
    this.isEditMode,
    this.isMultipleDevice,
    this.isReadOnly,
    this.isSubuser,
    super.key,
    this.subuser,
  });

  final SubuserEntity? subuser;
  final bool? isEditMode;
  final bool? isReadOnly;
  final bool? isSubuser;
  final bool? isMultipleDevice;

  @override
  State<IndividualProfileView> createState() => _IndividualProfileViewState();
}

class _IndividualProfileViewState extends State<IndividualProfileView>
    with IndividualProfileViewMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CupertinoNavigationBar(
        heroTag: UniqueKey(),
        leading: GestureDetector(
          onTap: _handleBackNavigation,
          child: Icon(Icons.arrow_back_ios_new, size: _kIconSize.sp),
        ),
        padding: EdgeInsetsDirectional.all(_kAppbarPadding),
        transitionBetweenRoutes: false,
      ),
      body: SafeArea(
        child: Padding(
          padding: context.paddingMedium,
          child: BlocConsumer<SubuserBloc, SubuserState>(
            builder: (ctx, state) => _handleBuildContent(state),
            listener: (ctx, state) => _handleBlocListener(state),
          ),
        ),
      ),
      resizeToAvoidBottomInset: true,
    );
  }
}
