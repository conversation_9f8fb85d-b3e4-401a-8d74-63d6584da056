part of 'individual_profile_view.dart';

mixin IndividualProfileViewMixin on State<IndividualProfileView> {
  final _kAppbarPadding = 8.0;
  final _kIconSize = 24;
  double _kTotalFormPages = 7.0;

  static const _kTotalMaxFormPages = 7.0;
  final _kTotalFormPagesEditMode = 6.0;

  static const _kMinimumLoadingDuration = Duration(seconds: 15);

  bool _isLoading = false;
  bool _isSuccessReceived = false;

  Timer? _loadingTimer;

  final _remoteConfigService = GetIt.instance.get<RemoteConfigService>();

  final _pageController = PageController();

  static const _kNameSurnamePageIndex = 0;
  static const _kHearAboutUsPageIndex = 5;
  final _kPurposeOfUsagePageIndex = 6;

  @override
  void initState() {
    super.initState();

    _kTotalFormPages = ((widget.isEditMode ?? false) || (widget.isSubuser ?? false))
        ? _kTotalFormPagesEditMode
        : _kTotalMaxFormPages;
  }

  void _handleLoadingState() {
    setState(() {
      _isLoading = true;
      _isSuccessReceived = false;
    });

    final viewHeight = 0.9;

    unawaited(showModalBottomSheet(
      backgroundColor: AppColors.appTextFormFieldBackgroundColor,
      builder: (context) => SizedBox(
        height: ((widget.isEditMode ?? false) || (widget.isSubuser ?? false))
            ? (context.height * viewHeight)
            : context.height,
        child: ProfileCalculatingView(
          isEditMode: (widget.isEditMode ?? false),
          isSubuser: (widget.isSubuser ?? false),
        ),
      ),
      context: context,
      enableDrag: false,
      isDismissible: false,
      isScrollControlled: true,
      useRootNavigator: true,
    ));

    _loadingTimer = Timer(_kMinimumLoadingDuration, () {
      setState(() => _isLoading = false);
      if (_isSuccessReceived) _navigateAfterSuccess();
    });
  }

  void _navigateAfterSuccess() {
    debugPrint("navigate after success");

    if (widget.subuser == null) {
      final bundleId =
          GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

      if (FlavorConstants.isHrv(bundleId) || FlavorConstants.isIndividual(bundleId)) {
        GetIt.instance<AuthBloc>().add(GetUserEvent());
      }
      /* unawaited(
        Navigator.of(context).pushNamedAndRemoveUntil(RouteConstants.landingPage, (route) => false),
      );*/
    } else {
      AppRoute.popUntilFirstScreen(context);
    }
  }

  void _handleFailureState(String errorMessage) {
    if (widget.subuser == null) {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false,
      ));
    } else {
      AppRoute.popUntilFirstScreen(context);
    }
    AppSnackbar.showErrorSnackbar(context, errorMessage);
  }

  void _handleUpdateSuccessState() {
    _isSuccessReceived = true;

    if (!_isLoading) {
      _navigateAfterSuccess();
    }
  }

  void _handleBlocListener(SubuserState state) {
    if (widget.isSubuser ?? false) {
      if (widget.isMultipleDevice ?? false) {
        AppRoute.pop(context);

        return;
      }
      AppRoute.popUntilFirstScreen(context);

      return;
    }
    switch (state) {
      case SubuserLoading():
        _handleLoadingState();
        break;

      case SubuserFailure():
        _handleFailureState(state.message);
        break;

      case SubuserCreatedSuccess():
      case SubuserUpdatedSuccess():
        _handleUpdateSuccessState();
        break;

      default:
        break;
    }
  }

  // Extracted callback to a separate method.
  void _handleOnTap() {
    final subuserEditEvent = _subuserEditEvent;

    if (subuserEditEvent != null) {
      if (widget.subuser == null) {
        context.read<SubuserBloc>().add(SubuserCreateEvent(
              age: _subuserEditEvent?.age ?? " ",
              gender: _subuserEditEvent?.gender ?? " ",
              hearAboutUs: _subuserEditEvent?.hearAboutUs ?? {},
              height: _subuserEditEvent?.height ?? "",
              name: _subuserEditEvent?.name ?? "",
              otherPou: _subuserEditEvent?.otherPou ?? [],
              purposeOfUsage: _subuserEditEvent?.purposeOfUsage ?? {},
              surname: _subuserEditEvent?.surname ?? "",
              weight: _subuserEditEvent?.weight ?? "",
            ));
      } else {
        context.read<SubuserBloc>().add(subuserEditEvent);
      }
    }
  }

  // Extracted callback to a separate method.
  void _handleBackNavigation() {
    if (_selectedIndex == 0) {
      if (widget.isSubuser ?? false) {
        AppRoute.pop(context);

        return;
      }
      if (widget.subuser == null) {
        unawaited(Navigator.of(context).pushAndRemoveUntil(
          CupertinoPageRoute(builder: (context) => const LoginPage()),
          (route) => false,
        ));

        return;
      }

      AppRoute.pop(context);
    } else {
      setState(() => _selectedIndex -= 1);
      unawaited(_pageController.animateToPage(
        _selectedIndex,
        curve: Curves.easeIn,
        duration: context.lowDuration,
      ));
    }
  }

  // Extracted callback to a separate method.
  void _handleContinueButton() {
    FocusScope.of(context).unfocus();

    if (!_isCurrentPageValid) {
      final errorText = "Please fill in all required fields";

      AppSnackbar.showErrorSnackbar(context, errorText);

      return;
    }

    setState(() => _selectedIndex += 1);
    unawaited(_pageController.animateToPage(
      _selectedIndex,
      curve: Curves.easeIn,
      duration: context.lowDuration,
    ));

    if (_selectedIndex == _kTotalFormPages) {
      if (widget.isReadOnly ?? false) {
        AppRoute.pop(context);

        return;
      }

      _handleOnTap();
    }
  }

  bool get _isCurrentPageValid {
    if (_subuserEditEvent == null) return false;
    final purposeOfUsageIndex = ((widget.isSubuser ?? false) || (widget.isEditMode ?? false))
        ? _kPurposeOfUsagePageIndex - 1
        : _kPurposeOfUsagePageIndex;

    if ((_subuserEditEvent?.purposeOfUsage ?? {}).isEmpty &&
        (purposeOfUsageIndex == _selectedIndex)) return false;

    if ((_subuserEditEvent?.purposeOfUsage ?? {})["Other"] == true &&
        (_subuserEditEvent?.otherPou ?? []).isEmpty &&
        (purposeOfUsageIndex == _selectedIndex)) {
      return false;
    }

    switch (_selectedIndex) {
      case _kNameSurnamePageIndex: // Name and Surname page.
        return _subuserEditEvent?.name != null &&
            (_subuserEditEvent?.name ?? "").isNotEmpty &&
            _subuserEditEvent?.surname != null &&
            (_subuserEditEvent?.surname ?? "").isNotEmpty;

      case _kHearAboutUsPageIndex: // Hear About Us (if not edit mode).
        if ((widget.isEditMode ?? false) || (widget.isSubuser ?? false)) return true;

        return _subuserEditEvent?.hearAboutUs != null &&
            (_subuserEditEvent?.hearAboutUs ?? {}).isNotEmpty;

      default:
        return true;
    }
  }

  // Extracted callback to a separate method.
  void _handleOnPageChanged(SubuserEditEvent event) {
    _subuserEditEvent = event;
  }

  // Extracted bloc builder to a separate method.
  Widget _handleBuildContent(SubuserState state) {
    final isLoading = state is SubuserLoading;
    final heightValue = ((widget.isSubuser ?? false)) ? 250 : 230;

    final formHeight = ((((widget.subuser == null && !(widget.isSubuser ?? false))
                ? 0
                : kBottomNavigationBarHeight) +
            kToolbarHeight) +
        heightValue);

    return ListView(children: [
      if (!isLoading)
        HrvProfileBar(
          index: _selectedIndex,
          profileBarLength: (((widget.isEditMode ?? false) || (widget.isSubuser ?? false))
                  ? _kTotalFormPagesEditMode
                  : _kTotalMaxFormPages)
              .toInt(),
        ),
      AppGaps.instance.gapVS16,
      SizedBox(
        height: (context.height - formHeight).sp,
        child: IndividualProfileForm(
          isEditMode: widget.isEditMode,
          isReadOnly: widget.isReadOnly,
          isSubuser: widget.isSubuser,
          onPageChanged: _handleOnPageChanged,
          pageController: _pageController,
          subuser: widget.subuser,
        ),
      ),
      if (!isLoading)
        CommonButton(
          buttonColor: AppColors.neoBlue,
          icon: isLoading ? const CupertinoActivityIndicator() : null,
          isDisabled: isLoading,
          onPress: _handleContinueButton,
          text: _remoteConfigService.deviceControlPageConfig.continueTxt,
        ),
    ]);
  }

  @override
  void dispose() {
    _pageController.dispose();
    _loadingTimer?.cancel();
    super.dispose();
  }

  int _selectedIndex = 0;

  SubuserEditEvent? _subuserEditEvent;
}
