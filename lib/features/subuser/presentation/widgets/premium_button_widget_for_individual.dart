import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/paywall/paywall_helper.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/home/<USER>/bloc/home_bloc.dart';

import '../../../../core/app_config/app_colors.dart';
import '../../../../core/app_config/app_strings.dart';

class PremiumButtonWidgetForIndividual extends StatefulWidget {
  const PremiumButtonWidgetForIndividual({super.key});

  @override
  State<PremiumButtonWidgetForIndividual> createState() => _PremiumButtonWidgetForIndividualState();
}

class _PremiumButtonWidgetForIndividualState extends State<PremiumButtonWidgetForIndividual> {
  static void _handleOnTap(bool isIndividual) {
    unawaited(PaywallHelper.presentPaywall(isIndividual: isIndividual));
  }

  final _iconSize = 30;
  final _iconSizeWidth = 30;
  final _horizontal = 16;
  final _vertical = 12;

  @override
  void initState() {
    super.initState();
    context.read<HomeBloc>().add(GetUserPremiumEvent(context: context));
  }

  @override
  Widget build(BuildContext context) {
    final isIndividual = BlocProvider.of<AuthBloc>(context, listen: false).currentUser?.userType ==
        AppStrings.individualType;

    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final settingsPageConfig = remoteConfigService.settingsPageConfig;

    return BlocBuilder<HomeBloc, HomeState>(
      builder: (ctx, state) {
        return (state.isUserPremium ?? false)
            ? const SizedBox.shrink()
            : Padding(
                padding: ctx.paddingMediumHorizontal.copyWith(bottom: ctx.paddingMedium.bottom),
                child: GestureDetector(
                  onTap: () => _handleOnTap(isIndividual),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: AppBorderRadius.circularSize12Radius(),
                      gradient: AppColors.individualHomePremiumButtonGradient,
                    ),
                    padding:
                        EdgeInsets.symmetric(horizontal: _horizontal.sp, vertical: _vertical.sp),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          spacing: 0,
                          children: [
                            AssetsEnums.crown.toPng(height: _iconSize.sp, width: _iconSizeWidth.sp),
                            AppGaps.instance.gapHS8,
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  settingsPageConfig.vagustimPremium,
                                  style: AppTextStyles.individualHomePremiumButtonTitle,
                                ),
                                Text(
                                  settingsPageConfig.forBetterExperience,
                                  style: AppTextStyles.individualHomePremiumButtonDesc,
                                ),
                              ],
                            ),
                          ],
                        ),
                        Icon(
                          Icons.arrow_forward,
                          color: Colors.white,
                          size: _iconSizeWidth.sp,
                        ),
                      ],
                    ),
                  ),
                ),
              );
      },
    );
  }
}
