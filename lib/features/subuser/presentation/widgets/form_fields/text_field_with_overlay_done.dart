import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../core/app_config/app_text_styles.dart';

// Define typedefs for callbacks.
typedef OnChangedCallback = void Function(String value);

class TextFieldWithOverlayDone extends StatefulWidget {
  const TextFieldWithOverlayDone({
    required this.color,
    required this.controller,
    super.key,
    required this.onChanged,
    required this.paramColor,
    required this.style,
    this.suffixText,
  });

  final TextEditingController controller;
  final Color color;
  final Color paramColor;
  final OnChangedCallback onChanged;
  final TextStyle style;
  final String? suffixText;

  @override
  State<TextFieldWithOverlayDone> createState() => _TextFieldWithOverlayDoneState();
}

class _TextFieldWithOverlayDoneState extends State<TextFieldWithOverlayDone> {
  // Extract magic numbers to constants.
  static const _toolbarHeight = 44.0;
  static const _borderWidth = 0.5;
  static const _minWidth = 40.0;
  static const _maxWidth = 100.0;

  static const _suffixFontSize = 14.0;

  static const _paddingHorizontal = 16.0;
  static const _paddingVertical = 12.0;
  static const _fontSize = 17.0;
  static const _iosBorderColor = Color(0xFFB5B5B5);
  static const _iosBackgroundColor = Color(0xFFD1D5DB);
  static const _iosBlueColor = Color(0xFF007AFF);
  static const _doneText = 'Done';
  final _focusNode = FocusNode();
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      _showOverlay();
    } else {
      _removeOverlay();
    }
  }

  void _showOverlay() {
    _removeOverlay();

    if (!Platform.isIOS) return;

    final overlay = Overlay.of(context);

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        bottom: MediaQuery.viewInsetsOf(context).bottom,
        left: 0,
        right: 0,
        child: Container(
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: _iosBorderColor, width: _borderWidth),
            ),
            color: _iosBackgroundColor,
          ),
          height: _toolbarHeight,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => _focusNode.unfocus(),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: _paddingHorizontal,
                      vertical: _paddingVertical,
                    ),
                    child: Text(
                      _doneText,
                      style: TextStyle(
                        color: _iosBlueColor,
                        fontSize: _fontSize,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );

    overlay.insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _removeOverlay();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        SizedBox(
          width: (widget.suffixText ?? "").isEmpty ? _maxWidth.w : _minWidth.w,
          child: TextField(
            controller: widget.controller,
            cursorColor: widget.color,
            decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
              isDense: true,
            ),
            focusNode: _focusNode,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              TextInputFormatter.withFunction(
                (oldValue, newValue) => newValue.text.isEmpty ? oldValue : newValue,
              ),
            ],
            keyboardType: Platform.isAndroid
                ? TextInputType.numberWithOptions(signed: true)
                : TextInputType.number,
            onChanged: widget.onChanged,
            onSubmitted: Platform.isAndroid ? (value) => _focusNode.unfocus() : null,
            style: widget.style,
            textInputAction: Platform.isAndroid ? TextInputAction.done : null,
          ),
        ),
        GestureDetector(
          onTap: () => _focusNode.requestFocus(),
          child: Text(
            widget.suffixText ?? "",
            style: AppTextStyles.paramControlTitle.copyWith(fontSize: _suffixFontSize.sp),
          ),
        ),
      ],
    );
  }
}
