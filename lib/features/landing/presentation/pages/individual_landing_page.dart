// ignore_for_file: prefer-class-destructuring, prefer-single-widget-per-file, avoid-non-null-assertion

import 'dart:async';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:provider/provider.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../app/flavor/flavor_bottom_bar_config.dart';
import '../../../../app/flavor/flavor_config.dart';
import '../../../../core/app_config/app_border_radius.dart';
import '../../../../core/app_config/app_colors.dart';
import '../../../../core/app_config/app_strings.dart';
import '../../../../core/app_config/app_tab_view.dart';
import '../../../../core/constants/flavor_constants.dart';
import '../../../../core/custom_widgets/lazy_load_indexed_stack.dart';
import '../../../../core/navigator/app_navigator.dart';
import '../../../../core/navigator/routes/constants/route_constants.dart';
import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../auth/presentation/pages/profile/health_professional/professional_profile_view.dart';
import '../../../subuser/domain/entities/subuser_entity.dart';
import '../../../subuser/presentation/widgets/subuser_form/individual/individual_profile_view.dart';

// Constants.
abstract final class _LandingPageConstants {
  static final itemWidth = 72.0.sp;
  static final bottomBarHeight = 64.0.sp;
  static final iconSize = 24.0.sp;
  static const fabElevation = 8.0;
  static final spacingBetweenItems = 72.0.sp;
  static const deviceControl = "device_control_in_app";
  static const leftItemsCount = 2;
  static const animationDuration = Duration(milliseconds: 400);
  static final selectedDotSize = 6.sp;
  static final fabSize = 60.0.sp;
  static final notcMargin = 4.sp;
}

class IndividualLandingPage extends StatefulWidget {
  const IndividualLandingPage({
    this.canPop,
    this.currentIndex,
    required this.flavorConfig,
    this.handleOnTap,
    super.key,
    this.navigatorKeys,
    this.pagesLength,
  });

  final int? currentIndex;
  final bool? canPop;
  final int? pagesLength;
  final List<GlobalKey<NavigatorState>>? navigatorKeys;
  final Function(int index, {ConnectionStateUpdate? connectionStateUpdate})? handleOnTap;
  final FlavorBottomBarConfig flavorConfig;

  @override
  State<IndividualLandingPage> createState() => _IndividualLandingPageState();
}

class _IndividualLandingPageState extends State<IndividualLandingPage>
    with TickerProviderStateMixin {
  bool _isInit = false;
  AnimationController? _animationController;
  Animation<double>? _spacingAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  int get _findDeviceControlTabIndex {
    return widget.flavorConfig.items.indexWhere(
      (item) => item.label == _LandingPageConstants.deviceControl,
    );
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: _LandingPageConstants.animationDuration,
      vsync: this,
    );
    _spacingAnimation = Tween<double>(
      begin: 0.0,
      end: _LandingPageConstants.spacingBetweenItems,
    ).animate(CurvedAnimation(
      curve: Curves.easeInOut,
      parent: _animationController!,
    ));
  }

  void _updateSpacingAnimation(bool isConnected) {
    if (isConnected) {
      unawaited(_animationController?.forward());
    } else {
      unawaited(_animationController?.reverse());
    }
  }

  Widget _buildWithConnectionState(
    BuildContext context,
    ConnectionStateUpdate connectionState,
    Widget? child,
  ) {
    final isConnected = connectionState.connectionState == DeviceConnectionState.connected;
    _updateSpacingAnimation(isConnected);

    return Scaffold(
      body: _LandingPageBody(
        currentIndex: widget.currentIndex ?? 0,
        flavorConfig: widget.flavorConfig,
        navigatorKeys: widget.navigatorKeys ?? [],
        pagesLength: widget.pagesLength ?? 0,
      ),
      bottomNavigationBar: _BottomNavigationBuilder(
        currentIndex: widget.currentIndex ?? 0,
        flavorConfig: widget.flavorConfig,
        isConnected: isConnected,
        onTap: _handleBottomBarTap,
        spacingAnimation: _spacingAnimation!,
      ),
      extendBody: true,
      floatingActionButton: _buildSimpleAnimatedFAB(connectionState, isConnected),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      resizeToAvoidBottomInset: false,
    );
  }

  Widget? _buildSimpleAnimatedFAB(
    ConnectionStateUpdate connectionState,
    bool isConnected,
  ) {
    final deviceControlTabIndex = _findDeviceControlTabIndex;
    if (deviceControlTabIndex == -1) return null;

    return AnimatedScale(
      duration: context.lowDuration,
      scale: isConnected ? 1.0 : 0.0,
      child: FloatingActionButton(
        elevation: isConnected ? _LandingPageConstants.fabElevation : 0.0,
        heroTag: UniqueKey(),
        onPressed: isConnected ? () => _handleFabTap(connectionState, deviceControlTabIndex) : null,
        shape: CircleBorder(),
        child: Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                blurRadius: 6,
                color: AppColors.bottomBarFabButtonShadow,
                offset: const Offset(0, 3),
              ),
            ],
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              colors: AppColors.activityBorderLinear.colors,
              end: Alignment.bottomRight,
              stops: [0.02, 0.5, 0.7, 0.85, 1.0],
            ),
            shape: BoxShape.circle,
          ),
          height: _LandingPageConstants.fabSize,
          width: _LandingPageConstants.fabSize,
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: AssetsEnums.icSLogo.toSvg(),
          ),
        ),
      ),
    );
  }

  void _handleFabTap(ConnectionStateUpdate connectionState, int deviceControlTabIndex) {
    debugPrint(
      'FloatingActionButton tapped - deviceControlTabIndex tab index: $deviceControlTabIndex',
    );
    widget.handleOnTap?.call(deviceControlTabIndex, connectionStateUpdate: connectionState);
  }

  void _handleBottomBarTap(int originalIndex) {
    debugPrint('Bottom bar tapped: original=$originalIndex');
    widget.handleOnTap?.call(originalIndex);
  }

  void _handleAuthStateListener(BuildContext context, AuthState state) {
    if (state is RedirectingToAnotherAppState) {
      _AuthNavigationHandler.handleFirstAppLaunch(
        context,
        state.user.name,
        state.user.defaultSubuser,
      );

      return;
    }

    if (state is! GetUserSuccess || _isInit) return;
    _isInit = true;
  }

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: _handleAuthStateListener,
      child: PopScope(
        canPop: widget.canPop ?? false,
        child: Consumer<ConnectionStateUpdate>(
          builder: _buildWithConnectionState,
        ),
      ),
    );
  }
}

// Widget for the body content instead of getter.
class _LandingPageBody extends StatelessWidget {
  const _LandingPageBody({
    required this.currentIndex,
    required this.flavorConfig,
    required this.navigatorKeys,
    required this.pagesLength,
  });

  final int currentIndex;
  final int pagesLength;
  final List<GlobalKey<NavigatorState>> navigatorKeys;
  final FlavorBottomBarConfig flavorConfig;

  @override
  Widget build(BuildContext context) {
    return LazyLoadIndexedStack(
      index: currentIndex,
      children: List.generate(
        pagesLength,
        (index) => AppTabView(
          builder: (ctx) => flavorConfig.pages.elementAtOrNull(index) ?? const SizedBox(),
          navigatorKey: navigatorKeys[index],
          routes: AppNavigator.routeMap,
        ),
      ),
    );
  }
}

// Separate class for bottom navigation building.
class _BottomNavigationBuilder extends StatelessWidget {
  const _BottomNavigationBuilder({
    required this.currentIndex,
    required this.flavorConfig,
    required this.isConnected,
    required this.onTap,
    required this.spacingAnimation,
  });

  static Widget _resizeIcon(Widget? icon) {
    if (icon is Container && icon.child != null) {
      final child = icon.child;
      if (child is SvgPicture) {
        return SizedBox.square(
          dimension: _LandingPageConstants.iconSize.sp,
          child: child,
        );
      } else if (child is Icon) {
        return Icon(
          child.icon,
          color: child.color,
          size: _LandingPageConstants.iconSize.sp,
        );
      }
    }

    return icon ?? const SizedBox();
  }

  final int currentIndex;
  final bool isConnected;
  final FlavorBottomBarConfig flavorConfig;
  final Function(int) onTap;

  final Animation<double> spacingAnimation;

  _FilteredItemsData _buildFilteredItems() {
    final filteredItems = <BottomNavigationBarItem>[];
    final reverseIndexMap = <int, int>{};
    final filteredToOriginalMap = <int, int>{};
    for (int i = 0; i < flavorConfig.items.length; i += 1) {
      final item = flavorConfig.items[i];

      // Skip device control tab item as it becomes FAB.
      if (item.label == _LandingPageConstants.deviceControl) continue;

      final filteredIndex = filteredItems.length;
      reverseIndexMap[filteredIndex] = i;
      filteredToOriginalMap[i] = filteredIndex;
      filteredItems.add(_buildBottomBarItem(item));
    }

    // Adjust current index for filtered items - FIX for FAB selection issue.
    int adjustedCurrentIndex = -1;
    if (filteredToOriginalMap.containsKey(currentIndex)) {
      adjustedCurrentIndex = filteredToOriginalMap[currentIndex]!;
    }

    return _FilteredItemsData(
      adjustedCurrentIndex: adjustedCurrentIndex,
      items: filteredItems,
      reverseIndexMap: reverseIndexMap,
    );
  }

  List<Widget> _buildBottomBarItems(_FilteredItemsData data) {
    final items = <Widget>[];

    // Left items.
    for (int index = 0;
        index < _LandingPageConstants.leftItemsCount && index < data.items.length;
        index += 1) {
      items.add(_buildBottomBarIcon(data, index));
    }

    // Animated spacing for FAB - always add but with animated width.
    items.add(AnimatedContainer(
      curve: Curves.easeInOut,
      duration: _LandingPageConstants.animationDuration,
      width: spacingAnimation.value,
    ));

    // Right items.
    for (int index = _LandingPageConstants.leftItemsCount; index < data.items.length; index += 1) {
      items.add(_buildBottomBarIcon(data, index));
    }

    return items;
  }

  Widget _buildBottomBarIcon(_FilteredItemsData data, int index) {
    final isSelected = index == data.adjustedCurrentIndex;
    final item = data.items[index];

    final BottomNavigationBarItem(:activeIcon, :icon, :label) = item;

    return Expanded(
      child: InkWell(
        borderRadius: AppBorderRadius.circularSize12Radius(),
        focusColor: Colors.transparent,
        highlightColor: Colors.transparent,
        hoverColor: Colors.transparent,
        onTap: () {
          final originalIndex = data.reverseIndexMap[index];
          if (originalIndex != null) {
            onTap(originalIndex);
          }
        },
        splashColor: Colors.transparent,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            AnimatedScale(
              curve: Curves.easeInOut,
              duration: const Duration(milliseconds: 400),
              scale: isSelected ? 1.1 : 1.0,
              child: isSelected ? (activeIcon ?? icon) : icon,
            ),
            Text(
              label ?? "",
              overflow: TextOverflow.ellipsis,
              style:
                  isSelected ? flavorConfig.selectedLabelStyle : flavorConfig.unselectedLabelStyle,
            ),
            isSelected
                ? Container(
                    decoration: BoxDecoration(
                      color: AppColors.neoBlue,
                      shape: BoxShape.circle,
                    ),
                    height: _LandingPageConstants.selectedDotSize,
                    width: _LandingPageConstants.selectedDotSize,
                  )
                : SizedBox(height: _LandingPageConstants.selectedDotSize),
          ],
        ),
      ),
    );
  }

  BottomNavigationBarItem _buildBottomBarItem(BottomNavigationBarItem item) {
    return BottomNavigationBarItem(
      activeIcon: SizedBox(
        width: _LandingPageConstants.itemWidth.sp,
        child: _resizeIcon(item.activeIcon),
      ),
      icon: SizedBox(
        width: _LandingPageConstants.itemWidth.sp,
        child: _resizeIcon(item.icon),
      ),
      label: FirebaseRemoteConfig.instance.getString(item.label ?? ""),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filteredData = _buildFilteredItems();

    return AnimatedBuilder(
      animation: spacingAnimation,
      builder: (ctx, child) {
        return BottomAppBar(
          color: flavorConfig.backgroundColor,
          elevation: 0,
          height: _LandingPageConstants.bottomBarHeight.sp,
          notchMargin: _LandingPageConstants.notcMargin,
          padding: const EdgeInsets.symmetric(horizontal: 16.0).copyWith(top: 12),
          shape: isConnected ? CircularNotchedRectangle() : null,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: _buildBottomBarItems(filteredData),
          ),
        );
      },
    );
  }
}

// Data class for filtered items.
class _FilteredItemsData {
  final List<BottomNavigationBarItem> items;

  final Map<int, int> reverseIndexMap;
  final int adjustedCurrentIndex;
  const _FilteredItemsData({
    required this.adjustedCurrentIndex,
    required this.items,
    required this.reverseIndexMap,
  });
}

// Separate class for auth navigation handling.
abstract final class _AuthNavigationHandler {
  static void handleFirstAppLaunch(
    BuildContext context,
    String? name,
    SubuserEntity? subuser,
  ) {
    final bundleId =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

    if (FlavorConstants.isHealthPro(bundleId) && (name ?? "").isEmpty) {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        CupertinoPageRoute(
          builder: (ctx) => const ProfessionalProfileView(isEditMode: false),
        ),
        (route) => false,
      ));
    } else if (FlavorConstants.isIndividual(bundleId) && subuser == null) {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        CupertinoPageRoute(
          builder: (ctx) => const IndividualProfileView(isEditMode: false),
        ),
        (route) => false,
      ));
    } else {
      unawaited(Navigator.of(context).pushNamedAndRemoveUntil(
        RouteConstants.loginPage,
        (route) => false,
      ));
    }
  }
}
