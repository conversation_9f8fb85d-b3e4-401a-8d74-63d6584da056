import 'package:flutter/material.dart';
import 'package:vagustimpro/features/ota/presentation/pages/ota_view.dart';

class OtaPage extends StatelessWidget {
  const OtaPage({
    required this.deviceId,
    required this.deviceMac,
    super.key,
    required this.pageContext,
  });
  final BuildContext pageContext;
  final String deviceId;
  final String deviceMac;

  Future<void> show() {
    return showDialog(
      builder: (_) => OtaView(deviceId: deviceId, deviceMac: deviceMac),
      context: pageContext,
    );
  }

  @override
  Widget build(BuildContext context) {
    return OtaView(deviceId: deviceId, deviceMac: deviceMac);
  }
}
