// ignore_for_file: invalid_use_of_visible_for_testing_member

part of 'ota_bloc.dart';

mixin OtaMixin on Bloc<OtaEvent, OtaState> {
  Stream<List<int>>? monitorOtaStatusStream;
  StreamSubscription<List<int>>? _subscription;

  final flavorConfig =
      GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName);

  void listenStreamEventSucces(Stream<List<int>> stream) async {
    monitorOtaStatusStream = stream;
    await _subscription?.cancel();
    _subscription = monitorOtaStatusStream?.listen((data) => listenerMonitorOta(data));
  }

  void listenerMonitorOta(List<int> data) {
    final resultData = String.fromCharCodes(data);
    debugPrint("OTA Status: $resultData");

    if (resultData.contains("wifi-fail")) {
      add(StatusEvent(status: OtaStatus.wifiError));
    } else if (resultData.contains("ota-fail")) {
      add(StatusEvent(status: OtaStatus.updateError));
    } else if (resultData.contains("ota-ok")) {
      add(StatusEvent(status: OtaStatus.updateSucces));
    } else if (resultData.contains("wifi-ok")) {
      add(StatusEvent(status: OtaStatus.updateInstalling));
    }
  }

  void saveDeviceEventSucces(DeviceEntity success) {
    if (state.status == OtaStatus.wifiError ||
        state.status == OtaStatus.updateError ||
        state.status == OtaStatus.updateInstalling ||
        state.status == OtaStatus.deviceDisconnected) {
      return;
    }
    // If user is individual app and connected to health_professional device or researcher device.
    if (handleIndividualAppConnectionToProfessionalOrResearcherDevice(success)) {
      return;
    }

    // If user is health_professional app and connected to individual device or researcher device.
    if (handleHealthProfessionalAppConnectionToIndividualOrResearcherDevice(success)) {
      return;
    }

    if (success.isUpdatedNow ?? false) {
      emit(state.copyWith(isShowingDialog: true, status: OtaStatus.updateSucces));

      return;
    }

    if (success.isUpdateRequired ?? false) {
      emit(state.copyWith(
        isShowingDialog: true,
        isUpdateForced: success.isUpdateForced,
        status: OtaStatus.updateRequired,
      ));

      return;
    }

    if (success.minimumAvailableFirmwareVersion == success.currentFirmwareVersion) {
      emit(state.copyWith(isShowingDialog: true, status: OtaStatus.deviceConnected));

      return;
    }
    emit(state.copyWith(isShowingDialog: true, status: OtaStatus.updateRequired));
  }

  bool handleIndividualAppConnectionToProfessionalOrResearcherDevice(DeviceEntity success) {
    if (flavorConfig.bundleId == FlavorConstants.vagustimProDevBundleId ||
        flavorConfig.bundleId == FlavorConstants.vagustimProProdBundleId) {
      if ((success.deviceType ?? "").isEmpty) {
        return false;
      }
      if (success.deviceType == DeviceTypeEnums.healthProfessional.value ||
          success.deviceType == DeviceTypeEnums.researcher.value) {
        emit(state.copyWith(deviceType: success.deviceType, status: OtaStatus.incorrectDevice));

        return true;
      }
    }

    return false;
  }

  bool handleHealthProfessionalAppConnectionToIndividualOrResearcherDevice(DeviceEntity success) {
    if (flavorConfig.bundleId == FlavorConstants.vagustimProfessionalDevBundleId ||
        flavorConfig.bundleId == FlavorConstants.vagustimProfessionalProdBundleId) {
      if (success.deviceType == DeviceTypeEnums.healthProfessional.value) {
        return false;
      }
      if ((success.deviceType ?? "").isEmpty ||
          success.deviceType == DeviceTypeEnums.individual.value ||
          success.deviceType == DeviceTypeEnums.researcher.value) {
        emit(state.copyWith(deviceType: success.deviceType, status: OtaStatus.incorrectDevice));

        return true;
      }
    }

    return false;
  }
}
