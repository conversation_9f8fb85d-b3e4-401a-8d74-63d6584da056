import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/features/ota/domain/usecases/get_ota_wifi_name_usecase.dart';
import 'package:vagustimpro/features/ota/domain/usecases/ota_listen_streams_usecase.dart';
import 'package:vagustimpro/features/ota/domain/usecases/save_device_usecase.dart';
import 'package:vagustimpro/features/ota/domain/usecases/start_ota_with_wifi_usecase.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_event.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_state.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_status.dart';

import '../../../../app/flavor/flavor_config.dart';
import '../../../../core/app_config/app_strings.dart';
import '../../../../core/enum/device_type_enums.dart';
import '../../domain/entities/device_entity.dart';

part 'ota_mixin.dart';

class OtaBloc extends Bloc<OtaEvent, OtaState> with OtaMixin {
  final GetOtaWifiNameUseCase getOtaWifiNameUseCase;
  final OtaListenStreamsUseCase otaListenStreamsUseCase;
  final SaveDeviceUsecase saveDeviceUsecase;
  final StartOtaWithWifiUsecase startOtaWithWifiUsecase;

  OtaBloc({
    required this.getOtaWifiNameUseCase,
    required this.otaListenStreamsUseCase,
    required this.saveDeviceUsecase,
    required this.startOtaWithWifiUsecase,
  }) : super(const OtaState()) {
    on<GetWifiNameEvent>(_getWifiNameEvent);
    on<ListenOtaStreamsEvent>(_onListenStreamsEvent);
    on<SaveDeviceEvent>(_saveDeviceEvent);
    on<StartOtaWithWifiEvent>(_startOtaWithWifiEvent);
    on<StatusEvent>(_statusEvent);
    on<ChangeIsShowingDialogEvent>(_isShowingDialogEvent);
  }

  Future<void> _getWifiNameEvent(
    GetWifiNameEvent event,
    Emitter<OtaState> emit,
  ) async {
    final res = await getOtaWifiNameUseCase.calculate(event.params);
    res.fold(
      (fail) => emit(
        state.copyWith(failureMessage: fail.message, status: OtaStatus.failure),
      ),
      (otaEntity) => emit(state.copyWith(entity: otaEntity)),
    );
  }

  void _statusEvent(StatusEvent event, Emitter<OtaState> emit) {
    emit(state.copyWith(
      isShowingDialog: (OtaStatus.deviceDisconnected == event.status) ||
              ((OtaStatus.updateSucces == event.status))
          ? true
          : null,
      status: event.status,
    ));
  }

  void _isShowingDialogEvent(ChangeIsShowingDialogEvent event, Emitter<OtaState> emit) {
    emit(state.copyWith(isShowingDialog: event.isShowingDialog));
  }

  Future<void> _onListenStreamsEvent(
    ListenOtaStreamsEvent event,
    Emitter<OtaState> emit,
  ) async {
    final res = await otaListenStreamsUseCase.calculate(
      OtaReadBleDeviceConfigsParams(event.deviceId),
    );

    res.fold(
      (fail) => emit(
        state.copyWith(failureMessage: fail.message, status: OtaStatus.failure),
      ),
      (stream) => listenStreamEventSucces(stream),
    );
  }

  Future<void> _saveDeviceEvent(
    SaveDeviceEvent event,
    Emitter<OtaState> emit,
  ) async {
    final res = await saveDeviceUsecase.calculate(
      SaveDeviceParams(
        bleId: event.bleId,
        deviceId: event.deviceId,
        firmwareVersion: event.firmwareVersion,
      ),
    );

    res.fold(
      (fail) => emit(
        state.copyWith(failureMessage: fail.message, status: OtaStatus.failure),
      ),
      (success) => saveDeviceEventSucces(success),
    );
  }

  void _startOtaWithWifiEvent(
    StartOtaWithWifiEvent event,
    Emitter<OtaState> emit,
  ) async {
    emit(state.copyWith(status: OtaStatus.wifiLoading));

    final res = await startOtaWithWifiUsecase.calculate(
      StartOtaWithWifiParams(
        deviceId: event.deviceId,
        deviceMac: event.deviceMac,
        wifiName: event.wifiName,
        wifiPassword: event.wifiPassword,
      ),
    );

    res.fold(
      (fail) => emit(
        state.copyWith(failureMessage: fail.message, status: OtaStatus.failure),
      ),
      (success) => emit(state.copyWith(status: OtaStatus.wifiLoading)),
    );
  }
}
