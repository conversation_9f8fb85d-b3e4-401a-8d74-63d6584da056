import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';

class AdjustParametersActionButton extends StatelessWidget {
  const AdjustParametersActionButton({
    required this.color,
    this.icon,
    this.isDisabled = false,
    this.isFullWidth = false,
    this.isPrimary = false,
    super.key,
    required this.label,
    required this.onPressed,
  });

  final IconData? icon;
  final String label;
  final Color color;
  final VoidCallback onPressed;
  final bool isDisabled;
  final bool isFullWidth;
  final bool isPrimary;

  @override
  Widget build(BuildContext context) {
    final buttonHeight = 50;
    final iconSize = 20;

    final blurRadius = 8.0;
    final shadowOpacity = 0.3;

    return AnimatedOpacity(
      duration: const Duration(milliseconds: 200),
      opacity: isDisabled ? 0.5 : 1.0,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: AppBorderRadius.circularSize12Radius(),
          onTap: isDisabled ? null : onPressed,
          child: Ink(
            decoration: BoxDecoration(
              borderRadius: AppBorderRadius.circularSize12Radius(),
              boxShadow: isPrimary
                  ? [
                      BoxShadow(
                        blurRadius: blurRadius,
                        color: color.withValues(alpha: shadowOpacity),
                        offset: const Offset(0, 3),
                      ),
                    ]
                  : null,
              color: isDisabled
                  ? AppColors.disabledButtonColor
                  : (isPrimary ? color : color.withValues(alpha: 0.1)),
            ),
            height: buttonHeight.sp,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              spacing: 0,
              children: [
                if (icon != null)
                  Icon(
                    icon,
                    color: isDisabled
                        ? AppColors.disabledTextColor
                        : (isPrimary ? AppColors.pureWhite : color),
                    size: iconSize.sp,
                  ),
                AppGaps.instance.gapHS8,
                Text(
                  label,
                  style: AppTextStyles.paramControlTitle.copyWith(
                    color: isDisabled
                        ? AppColors.disabledTextColor
                        : (isPrimary ? AppColors.pureWhite : color),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
