part of '../new_adjust_parameters_page.dart';

class NewAdjustParametersPageSucces extends StatelessWidget {
  const NewAdjustParametersPageSucces({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: context.paddingMedium,
        child: Column(
          spacing: 0,
          children: [
            const NewAdjustParametersPageParamControlWidgets(),
            AppGaps.instance.gapVS8,
            const NewAdjustParametersPageParamButtonWidgets(),
            AppGaps.gapBottom(context),
          ],
        ),
      ),
    );
  }
}
