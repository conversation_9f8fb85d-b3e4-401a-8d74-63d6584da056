import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:get_it/get_it.dart';
import 'package:provider/provider.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/parameter/presentation/pages/widgets/preset_programs/preset_program.dart';
import 'package:vagustimpro/features/parameter/presentation/pages/widgets/preset_programs/preset_program_item.dart';
import 'package:vagustimpro/features/time_of_usage/presentation/widgets/app_dialog.dart';
import '../../../../../../app/flavor/flavor_config.dart';
import '../../../../../../core/app_config/app_strings.dart';
import '../../../../../../core/navigator/routes/app_route.dart';
import '../../../../../landing/presentation/bloc/landing_bloc.dart';
import '../../../../../stimulation/presentation/bloc/stimulation_bloc.dart';
import '../../../../../stimulation/presentation/pages/ble_search_page.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/parameter/domain/params/session_type.dart';
import 'dart:async';

class PresetProgramView extends StatefulWidget {
  const PresetProgramView({this.isContinuousStimulation, super.key});

  final bool? isContinuousStimulation;

  @override
  State<PresetProgramView> createState() => _PresetProgramViewState();
}

class _PresetProgramViewState extends State<PresetProgramView> {
  // Constants for dialog messages.
  static const _kStopSessionDialogDescription =
      "Please stop the current session before starting a new one.";

  static const _kControlButtonText = "Control Vagustim";

  static bool _isDeviceActive(String? deviceStatus) {
    final start = "start";
    final pause = "pause";
    final zeroValue = "0";

    return deviceStatus == start ||
        deviceStatus == pause ||
        (int.tryParse(deviceStatus ?? zeroValue) ?? 0) > 0;
  }

  final _bundleId =
      GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

  final _childAspectRatio = 1.2;
  final _crossAxisCount = 2;
  final _crossAxisSpacing = 8.0;
  final _mainAxisSpacing = 8.0;

  final _presetsPrograms = [
    PresetProgramItem(presetProgram: PresetProgram.sleep),
    PresetProgramItem(presetProgram: PresetProgram.stress),
    PresetProgramItem(presetProgram: PresetProgram.supportRelaxation),
    PresetProgramItem(presetProgram: PresetProgram.accelerateRecovery),
    PresetProgramItem(presetProgram: PresetProgram.betterGutHealth),
    PresetProgramItem(presetProgram: PresetProgram.easeTension),
    PresetProgramItem(presetProgram: PresetProgram.overallWellBeing),
    PresetProgramItem(presetProgram: PresetProgram.cognitivePerformance),
  ];

  void _handleOnTap(ConnectionStateUpdate connectionStateUpdate, PresetProgram presetProgram) {
    if (FlavorConstants.isHealthPro(_bundleId)) {
      Navigator.of(context).pop(presetProgram);

      return;
    }
    final deviceStatus = context.read<StimulationBloc>().deviceStatus;

    if (_isDeviceActive(deviceStatus) &&
        connectionStateUpdate.connectionState == DeviceConnectionState.connected) {
      _showStopSessionDialog(presetProgram);
    } else {
      _handleNavigateToBleSearchPage(widget.isContinuousStimulation ?? false, false, presetProgram);
    }
  }

  void _showStopSessionDialog(PresetProgram presetProgram) {
    unawaited(AppDialog(
      context: context,
      description: _kStopSessionDialogDescription,
      okButtonOnTap: () => _handleNavigateToBleSearchPage(true, true, presetProgram),
      okButtonText: _kControlButtonText,
    ).show);
  }

  void _handleNavigateToBleSearchPage(
    bool isContinuousStimulation,
    bool isStopSession,
    PresetProgram presetProgram,
  ) {
    if (isStopSession) {
      AppRoute.pop(context);
      context.read<LandingBloc>().add(ChangeBottomBarIndexEvent(currentIndex: 2));

      return;
    }

    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.presetProgramType = presetProgram;
    stimulationBloc.currentSessionType = SessionType.preSetPrograms;

    unawaited(AppRoute.pushNewScreen(
      context,
      BleSearchPage(
        isContinuousStimulation: isContinuousStimulation,
        presetProgram: presetProgram,
        sessionType: SessionType.preSetPrograms,
        subuser: context.read<AuthBloc>().currentUser?.defaultSubuser,
      ),
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: FlavorConstants.isHealthPro(_bundleId)
          ? null
          : CupertinoNavigationBar(middle: Text("Programs")),
      body: Consumer<ConnectionStateUpdate>(
        builder: (ctx, connectionStateUpdate, child) {
          return GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              childAspectRatio: _childAspectRatio,
              crossAxisCount: _crossAxisCount,
              crossAxisSpacing: _crossAxisSpacing,
              mainAxisSpacing: _mainAxisSpacing,
            ),
            itemBuilder: (ctxGridView, index) {
              return InkWell(
                onTap: () =>
                    _handleOnTap(connectionStateUpdate, _presetsPrograms[index].presetProgram),
                child: _presetsPrograms[index],
              );
            },
            itemCount: _presetsPrograms.length,
            padding: ctx.paddingMedium,
          );
        },
      ),
    );
  }
}
