// ignore_for_file: avoid-wrapping-in-padding

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../../core/enum/assets_enums.dart';
import 'preset_program.dart';

class PresetProgramItem extends StatelessWidget {
  const PresetProgramItem({super.key, required this.presetProgram});

  // Constants.
  static const _kWidthValue = 0.45;
  static const _kHeight = 130;
  static const _kRightPosition = 16.0;
  static const _kTopPosition = _kRightPosition;

  final PresetProgram presetProgram;

  @override
  Widget build(BuildContext context) {
    final width = context.dynamicWidth(_kWidthValue);
    final waveColors = presetProgram.getBackgroundWaveColors;

    return Container(
      decoration: BoxDecoration(
        borderRadius: AppBorderRadius.circularSize12Radius(),
        color: presetProgram.getBackgroundColor,
      ),
      height: _kHeight.sp,
      width: width,
      child: Stack(children: [
        Positioned(
          bottom: 0,
          child: ClipRRect(
            borderRadius: AppBorderRadius.circularSize12Radius(),
            child: AssetsEnums.presetWaveForeground.toSvg(
              color: waveColors.firstOrNull,
              width: width,
            ),
          ),
        ),
        Positioned(
          bottom: 0,
          child: ClipRRect(
            borderRadius: AppBorderRadius.circularSize12Radius(),
            child: AssetsEnums.presetWaveBackground.toSvg(
              color: waveColors.elementAtOrNull(1),
              width: width,
            ),
          ),
        ),
        /* Positioned(
          top: 0,
          child: Padding(
            padding: context.paddingDisplay,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: AppBorderRadius.circularSize50Radius(),
                color: waveColors.firstOrNull,
              ),
              padding: context.paddingLargehorizontal.copyWith(
                bottom: _kPaddingBottom,
                top: _kPaddingTop,
              ),
              child: Text(presetProgram.getTag, style: AppTextStyles.presetProgramTag),
            ),
          ),
        ),*/
        Positioned(
          left: _kRightPosition,
          top: _kTopPosition,
          child: SvgPicture.asset(presetProgram.getIconPath),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          child: Padding(
            padding: context.paddingDisplay,
            child: Text(
              presetProgram.getTitle,
              style: AppTextStyles.presetProgramTitle,
            ),
          ),
        ),
      ]),
    );
  }
}
