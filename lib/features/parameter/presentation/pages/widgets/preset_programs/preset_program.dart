import 'package:flutter/cupertino.dart';

import '../../../../../../core/enum/assets_enums.dart';

enum PresetProgram {
  accelerateRecovery,
  ai,
  betterGutHealth,
  cognitivePerformance,
  easeTension,
  overallWellBeing,
  sleep,
  stress,
  supportRelaxation;

  // Title constants.
  static const _kAccelerateRecoveryTitle = "Accelerate\nRecovery";
  static const _kBetterGutHealthTitle = "Better\nGut Health";
  static const _kEaseTensionTitle = "Ease\nTension";
  static const _kOverallWellBeingTitle = "Overall\nWell-being";
  static const _kImproveSleepTitle = "Improve\nSleep";
  static const _kReduceStressTitle = "Reduce\nStress";
  static const _kSupportRelaxationTitle = "Support\nRelaxation";
  static const _kCognitivePerformanceTitle = "Cognitive\nPerformance";
  static const _kAiDrivenTitle = "AI-Driven";

  static const _kAccelerateRecoveryTag = "Recovery";
  static const _kBetterGutHealthTag = "Gut Health";
  static const _kEaseTensionTag = "Tension";
  static const _kOverallWellBeingTag = "Well-Being";
  static const _kImproveSleepTag = "Sleep";
  static const _kReduceStressTag = "Stress";
  static const _kSupportRelaxationTag = "Relaxation";

  static const _kAccelerateRecoveryRequestName = "recovery";
  static const _kBetterGutHealthRequestName = "digestive_health";
  static const _kEaseTensionRequestName = "ease_tension";
  static const _kOverallWellBeingRequestName = "overall_wellbeing";
  static const _kImproveSleepRequestName = "sleep";
  static const _kReduceStressRequestName = "stress";
  static const _kSupportRelaxationRequestName = "support_relaxation";
  static const _kCognitivePerformanceRequestName = "cognitive_performance";
  static const _kAiRequestName = "ai";

  // Color constants.
  static const _kAccelerateRecoveryWaveColor1 = 0xFF89B3A1;
  static const _kAccelerateRecoveryWaveColor2 = 0xFF638F7F;
  static const _kBetterGutHealthWaveColor1 = 0xFFC19877;
  static const _kBetterGutHealthWaveColor2 = 0xFFB5825D;
  static const _kEaseTensionWaveColor1 = 0xFF9CAE76;
  static const _kEaseTensionWaveColor2 = 0xFF6A8A4D;
  static const _kOverallWellBeingWaveColor1 = 0xFF827946;
  static const _kOverallWellBeingWaveColor2 = 0xFF615D42;
  static const _kSleepWaveColor1 = 0xFF505F88;
  static const _kSleepWaveColor2 = 0xFF38425D;
  static const _kStressWaveColor1 = 0xFF6CB3B2;
  static const _kStressWaveColor2 = 0xFF2A9397;
  static const _kSupportRelaxationWaveColor1 = 0xFFCE623F;
  static const _kSupportRelaxationWaveColor2 = 0xFFB47C5E;
  static const _kCognitivePerformanceWaveColor1 = 0xFF6CB3B2;
  static const _kCognitivePerformanceWaveColor2 = 0xFF2A9397;

  static const _kAccelerateRecoveryBgColor = 0xFFB0DAB2;
  static const _kBetterGutHealthBgColor = 0xFFFFC89D;
  static const _kEaseTensionBgColor = 0xFFCEE4A1;
  static const _kOverallWellBeingBgColor = 0xFFE2D691;
  static const _kSleepBgColor = 0xFF9BAFEC;
  static const _kStressBgColor = 0xFF9CDCDA;
  static const _kSupportRelaxationBgColor = 0xFFFEB89B;
  static const _kCognitivePerformanceBgColor = 0xFF9CDCDA;

  String get getTitle {
    switch (this) {
      case accelerateRecovery:
        return _kAccelerateRecoveryTitle;

      case betterGutHealth:
        return _kBetterGutHealthTitle;

      case easeTension:
        return _kEaseTensionTitle;

      case overallWellBeing:
        return _kOverallWellBeingTitle;

      case sleep:
        return _kImproveSleepTitle;

      case stress:
        return _kReduceStressTitle;

      case supportRelaxation:
        return _kSupportRelaxationTitle;

      case ai:
      case cognitivePerformance:
        return _kCognitivePerformanceTitle;
    }
  }

  String get getTag {
    switch (this) {
      case accelerateRecovery:
        return _kAccelerateRecoveryTag;

      case betterGutHealth:
        return _kBetterGutHealthTag;

      case easeTension:
        return _kEaseTensionTag;

      case overallWellBeing:
        return _kOverallWellBeingTag;

      case sleep:
        return _kImproveSleepTag;

      case stress:
        return _kReduceStressTag;

      case ai:
      case cognitivePerformance:
      case supportRelaxation:
        return _kSupportRelaxationTag;
    }
  }

  List<Color> get getBackgroundWaveColors {
    switch (this) {
      case accelerateRecovery:
        return [Color(_kAccelerateRecoveryWaveColor1), Color(_kAccelerateRecoveryWaveColor2)];

      case betterGutHealth:
        return [Color(_kBetterGutHealthWaveColor1), Color(_kBetterGutHealthWaveColor2)];

      case easeTension:
        return [Color(_kEaseTensionWaveColor1), Color(_kEaseTensionWaveColor2)];

      case overallWellBeing:
        return [Color(_kOverallWellBeingWaveColor1), Color(_kOverallWellBeingWaveColor2)];

      case sleep:
        return [Color(_kSleepWaveColor1), Color(_kSleepWaveColor2)];

      case stress:
        return [Color(_kStressWaveColor1), Color(_kStressWaveColor2)];

      case supportRelaxation:
        return [Color(_kSupportRelaxationWaveColor1), Color(_kSupportRelaxationWaveColor2)];

      case ai:
      case cognitivePerformance:
        return [Color(_kCognitivePerformanceWaveColor1), Color(_kCognitivePerformanceWaveColor2)];
    }
  }

  Color get getBackgroundColor {
    switch (this) {
      case accelerateRecovery:
        return Color(_kAccelerateRecoveryBgColor);

      case betterGutHealth:
        return Color(_kBetterGutHealthBgColor);

      case easeTension:
        return Color(_kEaseTensionBgColor);

      case overallWellBeing:
        return Color(_kOverallWellBeingBgColor);

      case sleep:
        return Color(_kSleepBgColor);

      case stress:
        return Color(_kStressBgColor);

      case supportRelaxation:
        return Color(_kSupportRelaxationBgColor);

      case ai:
      case cognitivePerformance:
        return Color(_kCognitivePerformanceBgColor);
    }
  }

  String get getIconPath {
    switch (this) {
      case accelerateRecovery:
        return AssetsEnums.icPresetRecovery.assetSvg;

      case betterGutHealth:
        return AssetsEnums.icPresetHealth.assetSvg;

      case easeTension:
        return AssetsEnums.icPresetEaseTension.assetSvg;

      case overallWellBeing:
        return AssetsEnums.icPresetOverall.assetSvg;

      case sleep:
        return AssetsEnums.icPresetSleep.assetSvg;

      case stress:
        return AssetsEnums.icPresetStress.assetSvg;

      case supportRelaxation:
        return AssetsEnums.icPresetRelaxation.assetSvg;

      case ai:
      case cognitivePerformance:
        return AssetsEnums.icPresetCognitivePerformance.assetSvg;
    }
  }

  String get getRequestName {
    switch (this) {
      case accelerateRecovery:
        return _kAccelerateRecoveryRequestName;

      case betterGutHealth:
        return _kBetterGutHealthRequestName;

      case easeTension:
        return _kEaseTensionRequestName;

      case overallWellBeing:
        return _kOverallWellBeingRequestName;

      case sleep:
        return _kImproveSleepRequestName;

      case stress:
        return _kReduceStressRequestName;

      case supportRelaxation:
        return _kSupportRelaxationRequestName;

      case ai:
        return _kAiRequestName;

      case cognitivePerformance:
        return _kCognitivePerformanceRequestName;
    }
  }

  static String titleFromRequestName(String requestName) {
    switch (requestName) {
      case _kAccelerateRecoveryRequestName:
        return _kAccelerateRecoveryTitle.replaceAll("\n", " ");

      case _kBetterGutHealthRequestName:
        return _kBetterGutHealthTitle.replaceAll("\n", " ");

      case _kEaseTensionRequestName:
        return _kEaseTensionTitle.replaceAll("\n", " ");

      case _kOverallWellBeingRequestName:
        return _kOverallWellBeingTitle.replaceAll("\n", " ");

      case _kImproveSleepRequestName:
        return _kImproveSleepTitle.replaceAll("\n", " ");

      case _kReduceStressRequestName:
        return _kReduceStressTitle.replaceAll("\n", " ");

      case _kSupportRelaxationRequestName:
        return _kSupportRelaxationTitle.replaceAll("\n", " ");

      case _kCognitivePerformanceRequestName:
        return _kCognitivePerformanceTitle.replaceAll("\n", " ");

      case _kAiRequestName:
        return _kAiDrivenTitle.replaceAll("\n", " ");

      default:
        return "";
    }
  }

  static PresetProgram presetProgramFromRequestName(String requestName) {
    switch (requestName) {
      case _kAccelerateRecoveryRequestName:
        return accelerateRecovery;

      case _kBetterGutHealthRequestName:
        return betterGutHealth;

      case _kEaseTensionRequestName:
        return easeTension;

      case _kOverallWellBeingRequestName:
        return overallWellBeing;

      case _kImproveSleepRequestName:
        return sleep;

      case _kReduceStressRequestName:
        return stress;

      case _kSupportRelaxationRequestName:
        return supportRelaxation;

      case _kCognitivePerformanceRequestName:
        return cognitivePerformance;

      case _kAiRequestName:
        return ai;

      default:
        return stress;
    }
  }
}
