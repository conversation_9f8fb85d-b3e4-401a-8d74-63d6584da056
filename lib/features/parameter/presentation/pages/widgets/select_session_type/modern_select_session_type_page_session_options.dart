// ignore_for_file: avoid-non-null-assertion, no-equal-arguments

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:get_it/get_it.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/parameter/domain/params/session_type.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';

import '../../../../../../core/navigator/app_navigator.dart';
import '../../../../../../core/navigator/routes/app_route.dart';
import '../../../../../../core/navigator/routes/constants/route_constants.dart';
import '../../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../../core/services/dialog_service.dart';
import '../../../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../../../stimulation/presentation/bloc/stimulation_bloc.dart';
import '../../../../../stimulation/presentation/pages/ble_search_page.dart';
import '../../../../../time_of_usage/presentation/widgets/app_dialog.dart';
import '../../../widgets/modern_session_options.dart';
import '../preset_programs/preset_program_view.dart';
import 'ai_driven/select_session_type_ai_driven_view.dart';
import 'session_option_data.dart';

class ModernSelectSessionTypePageSessionOptions extends StatefulWidget {
  const ModernSelectSessionTypePageSessionOptions({
    this.isContinuousStimulation,
    super.key,
    required this.subuser,
  });

  final SubuserEntity? subuser;
  final bool? isContinuousStimulation;

  @override
  State<ModernSelectSessionTypePageSessionOptions> createState() =>
      _ModernSelectSessionTypePageSessionOptionsState();
}

class _ModernSelectSessionTypePageSessionOptionsState
    extends State<ModernSelectSessionTypePageSessionOptions> with SingleTickerProviderStateMixin {
  AnimationController? _animationController;
  List<Animation<double>>? _fadeAnimations;
  List<Animation<Offset>>? _slideAnimations;

  // Constants.
  static const _kAnimationDuration = 1000;
  static const _kAnimationIntervalStart = 0.15;
  static const _kAnimationIntervalEnd = 0.5;
  static const _kSlideOffset = 0.2;
  static const _kAiDrivenIndex = 0;
  static const _kPresetProgramsIndex = 1;
  static const _kAdjustParametersIndex = 2;

  // String constants.
  static const _kAiDrivenTitle = 'AI-Powered Customization';
  static const _kAiDrivenDescription = 'Personalized sessions powered by artificial intelligence';
  static const _kPresetProgramsTitle = 'Programs';
  static const _kPresetProgramsDescription = 'Choose from our expert-designed programs';
  static const _kAdjustParametersTitle = 'Adjust Your Own Parameters';
  static const _kAdjustParametersDescription =
      'Fine-tune every aspect of your session'; // Color constants.

  static bool _isDeviceActive(String? deviceStatus) {
    final start = "start";
    final pause = "pause";
    final zeroValue = "0";

    return deviceStatus == start ||
        deviceStatus == pause ||
        (int.tryParse(deviceStatus ?? zeroValue) ?? 0) > 0;
  }

  final _homePageConfig = GetIt.instance<RemoteConfigService>().homePageConfig;

  @override
  void initState() {
    super.initState();
    _initializeSessionOptions();
    _initializeAnimations();
  }

  void _handleBleStatusPoweredOff() async {
    final dialogService = GetIt.instance.get<DialogService>();
    final okText = _homePageConfig.okText;
    final title = _homePageConfig.bleOffMessage;

    await dialogService.showDialog(
      confirmButtonText: okText,
      message: _homePageConfig.bleOffMessage,
      navigatorKey: AppNavigator.navigatorKey,
      title: title,
    );
  }

  void _handleBleReady() async {
    PermissionStatus status = await Permission.bluetoothConnect.status;
    if (!status.isGranted) {
      final dialogService = GetIt.instance.get<DialogService>();

      final goSettings = _homePageConfig.goToSettings;
      final settingsMessage = _homePageConfig.goToSettingsMessage;
      final settingsTitle = _homePageConfig.blePermission;

      await dialogService.showDialog(
        confirmButtonText: goSettings,
        message: settingsMessage,
        navigatorKey: AppNavigator.navigatorKey,
        onConfirm: () async => await openAppSettings(),
        title: settingsTitle,
      );
    }
  }

  void _handleAiDrivenTap() {
    final connectionStateUpdate = context.read<ConnectionStateUpdate>();
    final deviceStatus = context.read<StimulationBloc>().deviceStatus;

    if (_isDeviceActive(deviceStatus) &&
        connectionStateUpdate.connectionState == DeviceConnectionState.connected) {
      _showStopSessionDialog();
    } else {
      unawaited(AppRoute.pushNewScreen(context, SelectSessionTypeAiDrivenView()));
    }
  }

  void _initializeSessionOptions() {
    // Create a new list instead of mutating the existing one.
    final sessionOptions = [
      SessionOptionData(
        backgroundColor: AppColors.aiDrivenBgColor,
        description: _kAiDrivenDescription,
        iconBackgroundColor: AppColors.aiDrivenIconBgColor,
        iconColor: AppColors.aiDrivenIconBgColor,
        imagePath: AssetsEnums.icAiDriven.assetSvg,
        title: _kAiDrivenTitle,
      ),
      SessionOptionData(
        backgroundColor: AppColors.presetProgramsBgColor,
        description: _kPresetProgramsDescription,
        iconBackgroundColor: AppColors.presetProgramsIconBgColor,
        iconColor: AppColors.presetProgramsIconBgColor,
        imagePath: AssetsEnums.icPresetPrograms.assetSvg,
        title: _kPresetProgramsTitle,
      ),
      SessionOptionData(
        backgroundColor: AppColors.adjustParametersBgColor,
        description: _kAdjustParametersDescription,
        iconBackgroundColor: AppColors.adjustParametersIconBgColor,
        iconColor: AppColors.adjustParametersIconBgColor,
        imagePath: AssetsEnums.icAdjustYourParameters.assetSvg,
        title: _kAdjustParametersTitle,
      ),
    ];

    _sessionOptions = [];
    _sessionOptions.addAll(sessionOptions);
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: Duration(milliseconds: _kAnimationDuration.toInt()),
      vsync: this,
    );

    _createFadeAnimations();
    _createSlideAnimations();

    unawaited(_animationController?.forward());
  }

  void _createFadeAnimations() {
    _fadeAnimations = List.generate(
      _sessionOptions.length,
      (index) => Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
        curve: Interval(
          index * _kAnimationIntervalStart,
          _kAnimationIntervalEnd + index * _kAnimationIntervalStart,
          curve: Curves.easeOut,
        ),
        parent: _animationController!,
      )),
    );
  }

  void _createSlideAnimations() {
    _slideAnimations = List.generate(
      _sessionOptions.length,
      (index) => Tween<Offset>(
        begin: const Offset(0, _kSlideOffset),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        curve: Interval(
          index * _kAnimationIntervalStart,
          _kAnimationIntervalEnd + index * _kAnimationIntervalStart,
          curve: Curves.easeOutCubic,
        ),
        parent: _animationController!,
      )),
    );
  }

  void _navigateToPresetProgramView() {
    unawaited(AppRoute.pushNewScreen(
      context,
      PresetProgramView(
        isContinuousStimulation:
            context.read<StimulationBloc>().currentSessionType == SessionType.preSetPrograms
                ? widget.isContinuousStimulation
                : false,
      ),
    ));
  }

  void _navigateToAdjustParametersPage() {
    final connectionStateUpdate = context.read<ConnectionStateUpdate>();
    final deviceStatus = context.read<StimulationBloc>().deviceStatus;

    if (_isDeviceActive(deviceStatus) &&
        connectionStateUpdate.connectionState == DeviceConnectionState.connected) {
      _showStopSessionDialog();
    } else {
      AppRoute.pushNamed(
        context,
        RouteConstants.adjustParametersPage,
        arguments: widget.subuser,
      );
    }
  }

  void _showStopSessionDialog() {
    const kStopSessionDialogDescription =
        "Please stop the current session before starting a new one.";
    const kControlButtonText = "Control Vagustim";

    unawaited(AppDialog(
      context: context,
      description: kStopSessionDialogDescription,
      okButtonOnTap: () => _handleNavigateToBleSearchPage(true),
      okButtonText: kControlButtonText,
    ).show);
  }

  void _handleNavigateToBleSearchPage(bool isContinuousStimulation) {
    final stimBloc = context.read<StimulationBloc>();

    unawaited(AppRoute.pushNewScreen(
      context,
      BleSearchPage(
        isContinuousStimulation: isContinuousStimulation,
        presetProgram: stimBloc.presetProgramType,
        sessionType: stimBloc.currentSessionType ?? SessionType.quickStart,
        subuser: context.read<AuthBloc>().currentUser?.defaultSubuser,
      ),
    ));
  }

  void _handleOptionTap(BleStatus? bleStatus, int index) {
    if (bleStatus == BleStatus.poweredOff) {
      _handleBleStatusPoweredOff();

      return;
    }

    if (bleStatus != BleStatus.ready) {
      _handleBleReady();

      return;
    }

    switch (index) {
      case _kAiDrivenIndex:
        _handleAiDrivenTap();
        break;

      case _kPresetProgramsIndex:
        _navigateToPresetProgramView();
        break;

      case _kAdjustParametersIndex:
        _navigateToAdjustParametersPage();
        break;
    }
  }

  List<SessionOptionData> _sessionOptions = <SessionOptionData>[];

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BleStatus?>(
      builder: (ctx, bleStatus, child) {
        return ListView.separated(
          itemBuilder: (ctx, index) {
            return FadeTransition(
              opacity: _fadeAnimations?.elementAtOrNull(index) ?? const AlwaysStoppedAnimation(1.0),
              child: SlideTransition(
                position: _slideAnimations?.elementAtOrNull(index) ??
                    const AlwaysStoppedAnimation(Offset.zero),
                child: ModernSessionOptions(
                  backgroundColor: _sessionOptions[index].backgroundColor,
                  description: _sessionOptions[index].description,
                  iconBackgroundColor: _sessionOptions[index].iconBackgroundColor,
                  iconColor: _sessionOptions[index].iconColor,
                  imagePath: _sessionOptions[index].imagePath,
                  isComingSoon: _sessionOptions[index].imagePath == AssetsEnums.icAiDriven.assetSvg,
                  isRequiredPremium: _sessionOptions[index].isRequiredPremium,
                  onTap: () => _handleOptionTap(bleStatus, index),
                  title: _sessionOptions[index].title,
                ),
              ),
            );
          },
          itemCount: _sessionOptions.length,
          padding: ctx.paddingMedium,
          separatorBuilder: (ctxSeparator, index) => AppGaps.gapH12,
        );
      },
    );
  }
}
