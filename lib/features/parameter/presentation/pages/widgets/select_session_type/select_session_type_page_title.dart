import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

import '../../../../../../core/app_config/app_text_styles.dart';
import '../../../../../../core/remote_config/remote_config_service.dart';

class SelectSessionTypePageTitle extends StatelessWidget {
  const SelectSessionTypePageTitle({super.key});

  @override
  Widget build(BuildContext context) {
    final selectYourSessionTypePageConfig =
        GetIt.instance.get<RemoteConfigService>().selectYourSessionTypePageConfig;

    return Text(
      selectYourSessionTypePageConfig.selectYourSessionType,
      style: AppTextStyles.selectSessionTypeHeader,
    );
  }
}
