// ignore_for_file: prefer-extracting-callbacks, prefer-extracting-function-callbacks

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vagustimpro/core/app_config/app_error_widget.dart';
import 'package:vagustimpro/core/navigator/routes/app_route.dart';
import 'package:vagustimpro/features/hrv/presentation/bloc/hrv_bloc.dart';
import 'package:vagustimpro/features/hrv/presentation/bloc/hrv_status.dart';
import 'package:vagustimpro/features/parameter/domain/params/session_type.dart';
import 'package:vagustimpro/features/parameter/presentation/pages/widgets/preset_programs/preset_program.dart';
import 'package:vagustimpro/features/parameter/presentation/pages/widgets/select_session_type/ai_driven_result/ai_driven_calculate_widget.dart';
import 'package:vagustimpro/features/stimulation/presentation/pages/ble_search_page.dart';

import '../../../../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../../../../hrv/presentation/bloc/hrv_state.dart';

class AiDrivenResultWidget extends StatefulWidget {
  const AiDrivenResultWidget({this.isNavigateBleSearch, super.key});
  final bool? isNavigateBleSearch;

  @override
  State<AiDrivenResultWidget> createState() => _AiDrivenResultWidgetState();
}

class _AiDrivenResultWidgetState extends State<AiDrivenResultWidget> {
  static const _minimumLoadingDuration = Duration(seconds: 6);

  bool _isMinimumTimeElapsed = false;
  Timer? _loadingTimer;

  @override
  void initState() {
    super.initState();
    // Start the minimum loading timer when the widget is created.
    _loadingTimer = Timer(_minimumLoadingDuration, () {
      if (mounted) setState(() => _isMinimumTimeElapsed = true);
    });
  }

  @override
  void dispose() {
    _loadingTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final subuser = context.read<AuthBloc>().currentUser?.defaultSubuser;

    return Scaffold(
      body: BlocBuilder<HrvBloc, HrvState>(
        builder: (ctx, state) {
          final HrvState(:entity, :failureMessage, :status) = state;

          if (!_isMinimumTimeElapsed || status != HrvStatus.success) {
            // For failure state, we still show the error widget.
            if (status == HrvStatus.failure) {
              return AppErrorWidget(message: failureMessage);
            }

            // For all other states, or success before minimum time, show loading widget.
            return AiDrivenCalculateWidget();
          }

          // At this point, we know status is Success AND minimum 6 seconds have passed.
          if (entity == null) return SizedBox.shrink();

          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (!mounted) return;
            Navigator.of(ctx).pop();

            AppRoute.popUntilFirstScreen(ctx);

            if (widget.isNavigateBleSearch ?? true) {
              unawaited(AppRoute.pushNewScreen(
                ctx,
                BleSearchPage(
                  presetProgram: PresetProgram.ai,
                  sessionType: SessionType.aiDriven,
                  subuser: subuser,
                ),
              ));
            }
          });

          return AiDrivenCalculateWidget();
        },
      ),
    );
  }
}
