import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/enum/assets_enums_lottie.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../../insight/presentation/pages/widgets/info_card/info_card_score/insight_comprehensive_loading/random_percentage_animation.dart';

class AiDrivenCalculateWidget extends StatelessWidget {
  const AiDrivenCalculateWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final square = 100;
    final totalDuration = 6;
    final imageHeight = 0.4;
    final minPause = 0.3;
    final maxPause = 1.0;
    final incrementSecond = 1.0;
    final shortDelayMs = 200;

    final selectSessionTypePageConfig =
        GetIt.instance.get<RemoteConfigService>().selectYourSessionTypePageConfig;

    return Padding(
      padding: context.paddingMedium,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AppGaps.instance.gapVS16,
          AssetsEnums.aiDrivenCalculate.toSvg(
            height: context.height * imageHeight,
          ),
          Column(
            spacing: 0,
            children: [
              Text(
                selectSessionTypePageConfig.aiDrivenCalculatingTitle,
                style: AppTextStyles.comprehensiveLoadingTitle,
                textAlign: TextAlign.center,
              ),
              AppGaps.instance.gapVS16,
              Text(
                selectSessionTypePageConfig.aiDrivenCalculatingDesc,
                style: AppTextStyles.comprehensiveLoadingDescription,
                textAlign: TextAlign.center,
              ),
            ],
          ),
          AppGaps.instance.gapVS32,
          RandomPercentageAnimation(
            incrementSecond: incrementSecond,
            maxPause: maxPause,
            minPause: minPause,
            shortDelayMs: shortDelayMs,
            totalDuration: Duration(seconds: totalDuration),
          ),
          SizedBox.square(
            dimension: square.sp,
            child: AssetsEnumsLottie.comprehensiveLoading.toLottie(),
          ),
          AppGaps.instance.gapVS8,
        ],
      ),
    );
  }
}
