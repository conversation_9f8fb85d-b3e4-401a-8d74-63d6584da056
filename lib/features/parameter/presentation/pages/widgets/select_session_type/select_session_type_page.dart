import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:vagustimpro/features/parameter/presentation/pages/widgets/select_session_type/modern_select_session_type_page_session_options.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';
import '../../../../../../core/app_config/app_strings.dart';
import '../../../../../../core/helpers/analytic_helper.dart';
import '../../../../../auth/presentation/bloc/auth_bloc.dart';

class SelectSessionTypePage extends StatefulWidget {
  const SelectSessionTypePage({this.isContinuousStimulation, super.key, this.subuser});

  final SubuserEntity? subuser;
  final bool? isContinuousStimulation;

  @override
  State<SelectSessionTypePage> createState() => _SelectSessionTypePageState();
}

class _SelectSessionTypePageState extends State<SelectSessionTypePage> {
  @override
  void initState() {
    super.initState();
    // Sends an analytics event to track user.
    AnalyticHelper.instance.track(AppStrings.selectSessionTypePage);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CupertinoNavigationBar(middle: Text("Select Session Type")),
      body: SafeArea(
        child: ModernSelectSessionTypePageSessionOptions(
          isContinuousStimulation: widget.isContinuousStimulation,
          subuser: widget.subuser ?? context.read<AuthBloc>().currentUser?.defaultSubuser,
        ),
      ),
    );
  }
}
