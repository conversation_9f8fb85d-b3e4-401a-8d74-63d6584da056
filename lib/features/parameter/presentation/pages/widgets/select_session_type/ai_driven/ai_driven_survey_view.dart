// ignore_for_file: prefer-correct-callback-field-name

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/custom_widgets/common_button.dart';
import 'package:vagustimpro/core/custom_widgets/common_button_with_border.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../../../core/remote_config/select_your_session_type_page_config.dart';

class AiDrivenSurveyView extends StatelessWidget {
  const AiDrivenSurveyView({
    required this.handleOnTap,
    required this.handleOnTapAiDrivenSkip,
    super.key,
  });

  final VoidCallback handleOnTap;
  final VoidCallback handleOnTapAiDrivenSkip;

  @override
  Widget build(BuildContext context) {
    final selectYourSessionTypePageConfig =
        GetIt.instance.get<RemoteConfigService>().selectYourSessionTypePageConfig;

    final SelectYourSessionTypePageConfig(
      :aiDrivenFillOut,
      :aiDrivenFillOutDesc,
      :aiDrivenSkipBtn,
    ) = selectYourSessionTypePageConfig;

    return Padding(
      padding: context.paddingMedium,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AssetsEnums.aiDrivenSurvey.toSvg(),
          Text(
            aiDrivenFillOutDesc,
            style: AppTextStyles.aiDrivenSurveyTitle,
          ),
          Column(
            spacing: 0,
            children: [
              CommonButton(
                buttonColor: AppColors.neoBlue,
                onPress: handleOnTap,
                text: aiDrivenFillOut,
              ),
              AppGaps.instance.gapVS16,
              CommonButtonWithBorder(
                buttonBorderColor: AppColors.selectSessionTypeCardBackgroundColor,
                onPress: handleOnTapAiDrivenSkip,
                text: aiDrivenSkipBtn,
                textColor: AppColors.aiDrivenSkipButton,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
