import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_error_widget.dart';
import 'package:vagustimpro/core/custom_widgets/loader.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/remote_config/select_your_session_type_page_config.dart';
import 'package:vagustimpro/features/parameter/presentation/pages/widgets/select_session_type/ai_driven/ai_driven_heart_rate_view.dart';
import 'package:vagustimpro/features/parameter/presentation/pages/widgets/select_session_type/ai_driven/ai_driven_survey_view.dart';
import 'package:vagustimpro/features/survey/presentation/bloc/survey_status.dart';
import 'package:vagustimpro/features/survey/presentation/page/survey_page.dart';
import 'package:vagustimpro/features/time_of_usage/presentation/widgets/app_dialog.dart';

import '../../../../../../../core/navigator/routes/app_route.dart';
import '../../../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../../../../hrv/presentation/bloc/hrv_bloc.dart';
import '../../../../../../hrv/presentation/pages/start_camera_view.dart';
import '../../../../../../stimulation/presentation/pages/ble_search_page.dart';
import '../../../../../../survey/domain/entities/subuser_score_entity.dart';
import '../../../../../../survey/presentation/bloc/survey_bloc.dart';
import '../../../../../../survey/presentation/bloc/survey_event.dart';
import '../../../../../../survey/presentation/bloc/survey_state.dart';
import '../../../../../domain/params/session_type.dart';
import '../../preset_programs/preset_program.dart';
import '../ai_driven_result/ai_driven_result_widget.dart';

part 'select_session_type_ai_driven_view_mixin.dart';

class SelectSessionTypeAiDrivenView extends StatefulWidget {
  const SelectSessionTypeAiDrivenView({super.key});

  @override
  State<SelectSessionTypeAiDrivenView> createState() => _SelectSessionTypeAiDrivenViewState();
}

class _SelectSessionTypeAiDrivenViewState extends State<SelectSessionTypeAiDrivenView>
    with SelectSessionTypeAiDrivenViewMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CupertinoNavigationBar(
        heroTag: UniqueKey(),
        leading: GestureDetector(
          onTap: _handleBackNavigation,
          child: Icon(Icons.arrow_back_ios_new, size: _kIconSize.sp),
        ),
        padding: EdgeInsetsDirectional.all(_kAppbarPadding),
        transitionBetweenRoutes: false,
      ),
      body: SafeArea(
        child: BlocBuilder<SurveyBloc, SurveyState>(
          builder: (ctx, state) {
            switch (state.status) {
              case SurveyStatus.comprehensive:
              case SurveyStatus.comprehensiveSucces:
              case SurveyStatus.failure:
                return AppErrorWidget(message: state.failureMessage);

              case SurveyStatus.initial:
              case SurveyStatus.loading:
                return Loader();

              case SurveyStatus.success:
                List<Widget> pageChildren = [
                  AiDrivenSurveyView(
                    handleOnTap: _handleOnTapFillOut,
                    handleOnTapAiDrivenSkip: _handleOnTapFillOutSkip,
                  ),
                  AiDrivenHeartRateView(
                    handleOnTap: _handleOnTapHeartRate,
                    handleOnTapAiDrivenSkip: _handleOnTapHeartRateSkip,
                  ),
                ];
                if (hasRecentGeneralSurvey(state.subuserScoreList)) {
                  final _ = pageChildren.removeAt(0);
                }

                return PageView(
                  controller: _pageController,
                  physics: NeverScrollableScrollPhysics(),
                  children: pageChildren,
                );
            }
          },
        ),
      ),
    );
  }
}
