// ignore_for_file: prefer-extracting-function-callbacks

part of 'select_session_type_ai_driven_view.dart';

mixin SelectSessionTypeAiDrivenViewMixin on State<SelectSessionTypeAiDrivenView> {
  int _selectedIndex = 0;
  final _kAppbarPadding = 8.0;
  final _kIconSize = 24;
  final _last7Days = 7;
  final _general = 'general';

  final _pageController = PageController();

  void _handleOnTapFillOutSkip() {
    final selectSessionTypePageConfig =
        GetIt.instance.get<RemoteConfigService>().selectYourSessionTypePageConfig;

    final SelectYourSessionTypePageConfig(
      :aiDrivenSkipBtnDesc,
      :aiDrivenSkipBtnTitle,
      :laterBtn,
      :startBtn,
    ) = selectSessionTypePageConfig;

    unawaited(AppDialog(
      cancelButtonOnTap: _handleIncrementPageIndex,
      cancelButtonText: laterBtn,
      context: context,
      description: aiDrivenSkipBtnDesc,
      isCancelBtnNavigatePop: true,
      okButtonOnTap: _handleOnTapFillOut,
      okButtonText: startBtn,
      textAlign: TextAlign.center,
      title: aiDrivenSkipBtnTitle,
    ).show);
  }

  void _handleOnTapHeartRateSkip() {
    final selectSessionTypePageConfig =
        GetIt.instance.get<RemoteConfigService>().selectYourSessionTypePageConfig;

    final SelectYourSessionTypePageConfig(
      :aiDrivenHrvSkipBtnDesc,
      :aiDrivenHrvSkipBtnTitle,
      :laterBtn,
      :startBtn,
    ) = selectSessionTypePageConfig;

    unawaited(AppDialog(
      cancelButtonOnTap: _handleHeartRateLaterOnTap,
      cancelButtonText: laterBtn,
      context: context,
      description: aiDrivenHrvSkipBtnDesc,
      isCancelBtnNavigatePop: true,
      okButtonOnTap: _handleOnTapHeartRate,
      okButtonText: startBtn,
      title: aiDrivenHrvSkipBtnTitle,
    ).show);
  }

  // Extracted callback to a separate method.
  void _handleBackNavigation() {
    if (_selectedIndex == 0) {
      AppRoute.pop(context);
    } else {
      setState(() => _selectedIndex -= 1);

      unawaited(_pageController.animateToPage(
        _selectedIndex,
        curve: Curves.easeIn,
        duration: context.lowDuration,
      ));
    }
  }

  void _handleOnTapFillOut() async {
    final surveyBloc = context.read<SurveyBloc>();
    final subuserId =
        surveyBloc.state.subuserId ?? (context.read<AuthBloc>().currentUser?.defaultSubuser?.uid);

    surveyBloc.add(GetSubuserSurveysEvent(subuserId: subuserId ?? ''));
    _handleIncrementPageIndex();
    final response = await AppRoute.pushNewScreen(context, SurveyPage(isAiDriven: true));

    if (response == null) {
      _handleBackNavigation();
    }
  }

  void _handleOnTapHeartRate() async {
    final subuser = (context.read<AuthBloc>().currentUser?.defaultSubuser);
    final response = await AppRoute.pushNewScreen(
      context,
      StartCameraView(isAiDriven: true, isCanPop: false, subuser: subuser),
      specificIndex: 1,
      withNavBar: false,
    );

    if (response != null) {
      unawaited(showModalBottomSheet(
        builder: (ctx) => BlocProvider<HrvBloc>.value(
          value: GetIt.instance.get(),
          child: AiDrivenResultWidget(),
        ),
        context: context,
        isScrollControlled: true,
        useRootNavigator: true,
        useSafeArea: true,
      ));
    }
  }

  void _handleHeartRateLaterOnTap() {
    final subuser = context.read<AuthBloc>().currentUser?.defaultSubuser;

    unawaited(AppRoute.pushNewScreen(
      context,
      BleSearchPage(
        presetProgram: PresetProgram.ai,
        sessionType: SessionType.aiDriven,
        subuser: subuser,
      ),
    ));
  }

  void _handleIncrementPageIndex() {
    setState(() => _selectedIndex += 1);

    unawaited(_pageController.animateToPage(
      _selectedIndex,
      curve: Curves.easeIn,
      duration: context.lowDuration,
    ));
  }

  void getSubuserSurveys() {
    final surveyBloc = context.read<SurveyBloc>();

    final uid = (context.read<AuthBloc>().currentUser?.defaultSubuser?.uid);

    if ((uid ?? "").isNotEmpty) surveyBloc.add(GetSubuserScoresEvent(subuserId: uid!));
  }

  /// Checks if there is at least one “general” score created within the last 7 days.
  bool hasRecentGeneralSurvey(List<SubuserScoreEntity>? scores) {
    // If the list is null or empty, there cannot be any recent “general” scores.
    if (scores == null || scores.isEmpty) return false;

    // Calculate the cutoff date (7 days ago from now).
    final sevenDaysAgo = DateTime.now().subtract(Duration(days: _last7Days));

    // Iterate through scores and return true if any meet both conditions:
    // 1. scoreType equals “general”
    // 2. createdAt is after the cutoff date.
    return scores.any((score) {
      // Determine if this score’s type is “general” (case-insensitive).
      final isGeneral = score.scoreType?.toLowerCase() == _general;
      // Get the creation timestamp of this score.
      final createdAt = score.createdAt ?? DateTime.now();

      // Return true only if both conditions are satisfied.
      return isGeneral && createdAt.isAfter(sevenDaysAgo);
    });
  }

  @override
  void initState() {
    getSubuserSurveys();
    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}
