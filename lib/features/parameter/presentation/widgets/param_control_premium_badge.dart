import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';

import '../../../../core/app_config/app_border_radius.dart';
import '../../../../core/app_config/app_colors.dart';
import '../../../../core/enum/assets_enums.dart';

class ParamControlPremiumBadge extends StatelessWidget {
  const ParamControlPremiumBadge({super.key});

  @override
  Widget build(BuildContext context) {
    final horizontal = 8;
    final vertical = 4;
    final fontSize = 10;
    final iconSize = 12;

    return Container(
      decoration: BoxDecoration(
        borderRadius: AppBorderRadius.circularSize12Radius(),
        gradient: const LinearGradient(
          colors: [
            AppColors.sessionTypePremiumGradientStart,
            AppColors.sessionTypePremiumGradientEnd,
          ],
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: horizontal.sp,
        vertical: vertical.sp,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AssetsEnums.icPremium.toSvg(color: AppColors.pureWhite, height: iconSize.sp),
          AppGaps.instance.gapHS4,
          Text(
            'Premium',
            style: TextStyle(
              color: AppColors.pureWhite,
              fontSize: fontSize.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
