// ignore_for_file: avoid-returning-widgets, avoid-non-null-assertion

import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

class SessionOptionMinimalWidget extends StatefulWidget {
  const SessionOptionMinimalWidget({
    this.backgroundColor,
    required this.description,
    this.iconBackgroundColor,
    required this.imagePath,
    this.isComingSoon = false,
    this.isRequiredPremium,
    super.key,
    this.onTap,
    required this.title,
  });

  final String title;
  final String description;
  final String imagePath;
  final bool isComingSoon;
  final VoidCallback? onTap;
  final bool? isRequiredPremium;
  final Color? backgroundColor;
  final Color? iconBackgroundColor;

  @override
  State<SessionOptionMinimalWidget> createState() => _SessionOptionMinimalWidgetState();
}

class _SessionOptionMinimalWidgetState extends State<SessionOptionMinimalWidget>
    with SingleTickerProviderStateMixin {
  // Animation constants.
  static const _kAnimationDuration = Duration(milliseconds: 150);
  static const _kScaleBegin = 1.0;
  static const _kScaleEnd = 0.95;
  static const _kFadeBegin = 1.0;
  static const _kFadeEnd = 0.8;

  // Opacity constants.
  static const _kPressedOverlayOpacity = 0.1;
  static const _kIconBorderOpacity = 0.2;
  static const _kComingSoonBorderOpacity = _kIconBorderOpacity;

  // Size constants.
  static const _kIconSize = 20.0;
  static const _kFontSize = 10.0;
  static const _kComingSoonFontSize = 9.0;
  static const _kLineHeight = 1.1;
  static const _kIconContainerPadding = 6.0;

  // Spacing constants.
  static const _kIconSpacing = 12.0;
  // Static const _kPremiumBadgePosition = _kIconContainerPadding;.
  static const _kComingSoonHorizontalPadding = 8.0;
  static const _kComingSoonVerticalPadding = 4.0;

  AnimationController? _animationController;
  Animation<double>? _scaleAnimation;
  Animation<double>? _fadeAnimation;
  bool _isPressed = false;

  static Widget get _buildPressedOverlay {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: AppBorderRadius.circularSize12Radius(),
          color: AppColors.neoBlue.withValues(alpha: _kPressedOverlayOpacity),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: _kAnimationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: _kScaleBegin, end: _kScaleEnd).animate(CurvedAnimation(
      curve: Curves.easeInOut,
      parent: _animationController!,
    ));

    _fadeAnimation = Tween<double>(begin: _kFadeBegin, end: _kFadeEnd).animate(CurvedAnimation(
      curve: Curves.easeInOut,
      parent: _animationController!,
    ));
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    unawaited(_animationController?.forward());
  }

  void _handleTapUp(TapUpDetails details) {
    _resetPressedState();
  }

  void _handleTapCancel() {
    _resetPressedState();
  }

  void _resetPressedState() {
    setState(() => _isPressed = false);
    unawaited(_animationController?.reverse());
  }

  Widget get _buildAnimatedWidget {
    return Transform.scale(
      scale: _scaleAnimation?.value ?? _kScaleBegin,
      child: Opacity(
        opacity: _fadeAnimation?.value ?? _kFadeBegin,
        child: _buildGestureDetector,
      ),
    );
  }

  Widget get _buildGestureDetector {
    return GestureDetector(
      onTap: widget.isComingSoon ? null : widget.onTap,
      onTapCancel: _handleTapCancel,
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      child: _buildContainer,
    );
  }

  Widget get _buildContainer {
    return Container(
      decoration: _mainBoxDecoration,
      child: ClipRRect(
        borderRadius: AppBorderRadius.circularSize12Radius(),
        child: _buildStackContent,
      ),
    );
  }

  Widget get _buildStackContent {
    const blurValue = 1.2;
    final imageFilter = ImageFilter.blur(sigmaX: blurValue, sigmaY: blurValue);

    return Stack(
      children: [
        if (!widget.isComingSoon) _buildMainContent,
        if (widget.isComingSoon) ImageFiltered(imageFilter: imageFilter, child: _buildMainContent),
        if (widget.isComingSoon) _buildComingSoonOverlay,
        // If (widget.isRequiredPremium ?? false) _buildPremiumBadge,.
        if (_isPressed) _buildPressedOverlay,
      ],
    );
  }

  Widget get _buildMainContent {
    return Padding(
      padding: context.paddingDisplay,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildIconContainer,
          SizedBox(height: _kIconSpacing.sp),
          _buildTitle,
        ],
      ),
    );
  }

  Widget get _buildIconContainer {
    return Container(
      decoration: _iconContainerDecoration,
      padding: EdgeInsets.all(_kIconContainerPadding.sp),
      child: _buildIcon,
    );
  }

  Widget get _buildIcon {
    return SvgPicture.asset(
      widget.imagePath,
      colorFilter: ColorFilter.mode(
        widget.isComingSoon
            ? AppColors.lightTextColor
            : (widget.iconBackgroundColor ?? AppColors.sessionTypeTitle),
        BlendMode.srcIn,
      ),
      height: _kIconSize.sp,
      width: _kIconSize.sp,
    );
  }

  Widget get _buildTitle {
    return Text(
      widget.title,
      overflow: TextOverflow.ellipsis,
      style: AppTextStyles.presetProgramTag.copyWith(
        color: widget.isComingSoon ? AppColors.lightTextColor : AppColors.sessionTypeTitle,
        fontSize: _kFontSize.sp,
        fontWeight: widget.isComingSoon ? FontWeight.w900 : FontWeight.w600,
        height: _kLineHeight,
      ),
      textAlign: TextAlign.start,
    );
  }

  /* Widget get _buildPremiumBadge {
    return Positioned(
      right: _kPremiumBadgePosition.sp,
      top: _kPremiumBadgePosition.sp,
      child: FutureBuilder<bool>(
        builder: (context, snapshot) {
          return snapshot.connectionState == ConnectionState.waiting ||
                  snapshot.hasError ||
                  (snapshot.data ?? false)
              ? const SizedBox.shrink()
              : _buildPremiumButton;
        },
        future: PaywallHelper.isUserPremium(context),
      ),
    );
  }*/

  /* Widget get _buildPremiumButton {
    return AssetsEnums.icPremium.toSvg();
  }*/

  Widget get _buildComingSoonOverlay {
    return Positioned.fill(child: Center(child: _buildComingSoonBadge));
  }

  Widget get _buildComingSoonBadge {
    return Container(
      decoration: _comingSoonBadgeDecoration,
      padding: EdgeInsets.symmetric(
        horizontal: _kComingSoonHorizontalPadding.sp,
        vertical: _kComingSoonVerticalPadding.sp,
      ),
      child: Text(
        'Coming Soon',
        style: TextStyle(
          color: AppColors.pureWhite,
          fontSize: _kComingSoonFontSize.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  // Decoration getters.
  BoxDecoration get _mainBoxDecoration {
    final kOffset = 4.0;
    final opacity = 0.2;

    return BoxDecoration(
      border: Border.all(color: Colors.grey.shade200),
      borderRadius: AppBorderRadius.circularSize12Radius(),
      boxShadow: [
        BoxShadow(
          blurRadius: AppBorderRadius.circularSize12Radius().bottomLeft.x,
          color: (widget.iconBackgroundColor ?? AppColors.sessionTypeDefaultIconBackgroundColor)
              .withValues(alpha: opacity),
          offset: Offset(0, kOffset),
        ),
      ],
      color: widget.backgroundColor ?? AppColors.pureWhite,
    );
  }

  BoxDecoration get _iconContainerDecoration {
    final opacity = 0.1;

    return BoxDecoration(
      border: Border.all(
        color: AppColors.greyButtonColor.withValues(alpha: _kIconBorderOpacity),
      ),
      borderRadius: AppBorderRadius.circularSize12Radius(),
      color: widget.iconBackgroundColor?.withValues(alpha: opacity),
    );
  }

  BoxDecoration get _comingSoonBadgeDecoration {
    return BoxDecoration(
      border: Border.all(
        color: AppColors.neoBlue.withValues(alpha: _kComingSoonBorderOpacity),
      ),
      borderRadius: AppBorderRadius.circularSize8Radius(),
      color: AppColors.sessionTypeComingSoonColor,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController!,
      builder: (ctx, child) => _buildAnimatedWidget,
    );
  }
}
