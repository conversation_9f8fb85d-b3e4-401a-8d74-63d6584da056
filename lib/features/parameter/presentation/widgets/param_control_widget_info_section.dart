import 'package:flutter/material.dart';
import 'package:info_popup/info_popup.dart';

import '../../../../core/app_config/app_colors.dart';

class ParamControlWidgetInfoSection extends StatelessWidget {
  const ParamControlWidgetInfoSection({this.color, super.key, this.text});
  final String? text;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return InfoPopupWidget(
      arrowTheme: const InfoPopupArrowTheme(color: AppColors.lightTextColor),
      contentTitle: text,
      child: Icon(Icons.info_outline, color: color ?? AppColors.neoBlue),
    );
  }
}
