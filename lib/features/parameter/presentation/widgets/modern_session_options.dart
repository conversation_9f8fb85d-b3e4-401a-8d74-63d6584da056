// ignore_for_file: avoid-returning-widgets, avoid-long-functions

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

class ModernSessionOptions extends StatefulWidget {
  const ModernSessionOptions({
    this.backgroundColor,
    required this.description,
    this.iconBackgroundColor,
    this.iconColor,
    required this.imagePath,
    this.isComingSoon = false,
    this.isRequiredPremium = false,
    super.key,
    required this.onTap,
    required this.title,
  });

  final String title;
  final String description;
  final String imagePath;
  final VoidCallback onTap;
  final bool isComingSoon;
  final bool isRequiredPremium;
  final Color? backgroundColor;
  final Color? iconBackgroundColor;
  final Color? iconColor;

  @override
  State<ModernSessionOptions> createState() => _ModernSessionOptionsState();
}

class _ModernSessionOptionsState extends State<ModernSessionOptions>
    with SingleTickerProviderStateMixin {
  // Constants.
  static const _kOffset = 4.0;
  static const _kIconSize = 28.0;
  static const _kIconBackgroundSize = 56.0;
  static const _kPremiumLetterSpacing = 0.5;

  // String constants.
  static const _kProText = 'PRO';
  static const _kComingSoonText = 'COMING SOON';

  static Widget get _buildPremiumBadge {
    const kPremiumPaddingH = 12.0;
    const kPremiumPaddingV = 6.0;
    const kPremiumBlurRadius = 8.0;
    const kPremiumOffset = 2.0;
    const kPremiumIconSize = 16.0;
    const kPremiumIconSpacing = 4.0;
    const kPremiumFontSize = 12.0;

    return Container(
      decoration: BoxDecoration(
        borderRadius: AppBorderRadius.circularSize16Radius(),
        boxShadow: [
          BoxShadow(
            blurRadius: kPremiumBlurRadius,
            color: AppColors.sessionTypePremiumGradientStart.withValues(alpha: 0.3),
            offset: const Offset(0, kPremiumOffset),
          ),
        ],
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          colors: [
            AppColors.sessionTypePremiumGradientStart,
            AppColors.sessionTypePremiumGradientEnd,
          ],
          end: Alignment.bottomRight,
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: kPremiumPaddingH.w,
        vertical: kPremiumPaddingV.h,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star_rounded,
            color: AppColors.pureWhite,
            size: kPremiumIconSize.sp,
          ),
          SizedBox(width: kPremiumIconSpacing.w),
          Text(
            _kProText,
            style: TextStyle(
              color: AppColors.pureWhite,
              fontSize: kPremiumFontSize.sp,
              fontWeight: FontWeight.w700,
              letterSpacing: _kPremiumLetterSpacing,
            ),
          ),
        ],
      ),
    );
  }

  static Widget get _buildComingSoonOverlay {
    const kComingSoonPaddingH = 20.0;
    const kComingSoonPaddingV = 10.0;
    const kComingSoonFontSize = 13.0;

    return Positioned.fill(
      child: Center(
        child: Container(
          decoration: BoxDecoration(
            borderRadius: AppBorderRadius.circularSize16Radius(),
            color: AppColors.sessionTypeComingSoonColor,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: kComingSoonPaddingH.w,
            vertical: kComingSoonPaddingV.h,
          ),
          child: Text(
            _kComingSoonText,
            style: TextStyle(
              color: AppColors.pureWhite,
              fontSize: kComingSoonFontSize.sp,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ),
    );
  }

  void _handleTap() {
    if (widget.isComingSoon) return;
    widget.onTap();
  }

  Widget get _buildCardContent {
    const kPadding = 20.0;
    const kSpacing = 16.0;

    final iconBackgroundColor =
        widget.iconBackgroundColor ?? AppColors.sessionTypeDefaultIconBackgroundColor;

    return Padding(
      padding: EdgeInsets.all(kPadding.w),
      child: Row(
        spacing: kSpacing.sp,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: AppBorderRadius.circularSize16Radius(),
              color: widget.iconBackgroundColor?.withValues(alpha: 0.1),
            ),
            height: _kIconBackgroundSize.sp,
            width: _kIconBackgroundSize.w,
            child: Center(
              child: SvgPicture.asset(
                widget.imagePath,
                colorFilter: ColorFilter.mode(
                  (widget.iconColor ?? AppColors.sessionTypeDefaultIconBackgroundColor),
                  BlendMode.srcIn,
                ),
                height: _kIconSize.w,
                width: _kIconSize.sp,
              ),
            ),
          ),

          // Title and description.
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 0,
              children: [
                Text(widget.title, style: AppTextStyles.sessionTypeTitle),
                AppGaps.instance.gapVS16,
                Text(
                  widget.description,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.sessionTypeDesc,
                ),
              ],
            ),
          ),

          // Premium badge or arrow.
          if (widget.isRequiredPremium)
            _buildPremiumBadge
          else if (!widget.isComingSoon)
            _buildArrowIcon(iconBackgroundColor),
        ],
      ),
    );
  }

  Widget _buildArrowIcon(Color iconBackgroundColor) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: AppBorderRadius.circularSize16Radius(),
        color: AppColors.sessionTypeGreyColor,
      ),
      padding: context.paddingLow,
      child: Icon(
        Icons.arrow_forward_rounded,
        color: (widget.iconColor ?? iconBackgroundColor),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final opacity = 0.2;
    const blurValue = 2.0;
    final imageFilter = ImageFilter.blur(sigmaX: blurValue, sigmaY: blurValue);

    return GestureDetector(
      onTap: _handleTap,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade200),
          borderRadius: AppBorderRadius.circularSize16Radius(),
          boxShadow: [
            BoxShadow(
              blurRadius: AppBorderRadius.circularSize16Radius().bottomLeft.x,
              color: (widget.iconBackgroundColor ?? AppColors.sessionTypeDefaultIconBackgroundColor)
                  .withValues(alpha: opacity),
              offset: const Offset(0, _kOffset),
            ),
          ],
          color: widget.backgroundColor ?? AppColors.pureWhite,
        ),
        padding: EdgeInsets.only(bottom: widget.isComingSoon ? 0 : context.paddingDisplay.bottom),
        child: Stack(
          children: [
            if (!widget.isComingSoon) _buildCardContent,
            if (widget.isComingSoon)
              ImageFiltered(
                imageFilter: imageFilter,
                child: _buildCardContent,
              ),
            if (widget.isComingSoon) _buildComingSoonOverlay,
          ],
        ),
      ),
    );
  }
}
