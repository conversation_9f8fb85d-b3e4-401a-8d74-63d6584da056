// ignore_for_file: no-equal-arguments

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';

import '../../../../../../core/enum/assets_enums_lottie.dart';

class BleSearchScanLoading extends StatelessWidget {
  const BleSearchScanLoading({super.key});

  @override
  Widget build(BuildContext context) {
    final scanSize = 150.0;
    final loadingSize = 320.0;

    return Stack(
      children: [
        Center(child: AssetsEnums.icScan.toSvg(height: scanSize.h, width: scanSize.w)),
        Center(
          child: AssetsEnumsLottie.scanLoading.toLottie(
            height: loadingSize.sp,
            width: loadingSize.sp,
          ),
        ),
      ],
    );
  }
}
