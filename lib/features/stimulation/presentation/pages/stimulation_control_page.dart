import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:get_it/get_it.dart';
import 'package:provider/provider.dart';
import 'package:vagustimpro/core/ble/ble_device_connector.dart';
import 'package:vagustimpro/core/ble/ble_device_interactor.dart';
import 'package:vagustimpro/core/ble/ble_scanner.dart';
import 'package:vagustimpro/core/navigator/routes/app_route.dart';
import 'package:vagustimpro/core/navigator/routes/constants/route_constants.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/stimulation_control_widget.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';

import '../../../../app/flavor/flavor_config.dart';
import '../../../../core/app_config/app_strings.dart';
import '../../../../core/constants/flavor_constants.dart';
import '../../../landing/presentation/bloc/landing_bloc.dart';
import '../../../multiple_device/domain/entities/device_status_enum.dart';
import '../../../multiple_device/domain/entities/multiple_device_entity.dart';
import '../../../multiple_device/presentation/bloc/multiple_device_bloc.dart';
import '../../../multiple_device/presentation/bloc/multiple_device_event.dart';
import '../../../parameter/domain/params/session_type_params.dart';

class StimulationControlPage extends StatelessWidget {
  const StimulationControlPage({
    required this.deviceId,
    this.deviceName,
    required this.isContinuousStimulation,
    super.key,
    required this.sessionTypeParams,
    required this.subuser,
  });

  static CupertinoPageRoute<StimulationControlPage> route({
    required String deviceId,
    required bool isContinuousStimulation,
    required SessionTypeParams sessionTypeParams,
    required SubuserEntity subuser,
  }) =>
      CupertinoPageRoute(
        builder: (context) => StimulationControlPage(
          deviceId: deviceId,
          isContinuousStimulation: isContinuousStimulation,
          sessionTypeParams: sessionTypeParams,
          subuser: subuser,
        ),
      );

  final SubuserEntity subuser;
  final String deviceId;
  final bool isContinuousStimulation;
  final SessionTypeParams sessionTypeParams;
  final String? deviceName;

  @override
  Widget build(BuildContext context) {
    final flavorConfig =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName);

    return Scaffold(
      body: Consumer4<BleDeviceInteractor, ConnectionStateUpdate, BleScanner, BleDeviceConnector>(
        builder: (
          _,
          bleDeviceInteractor,
          connectionStateUpdate,
          bleScanner,
          bleDeviceConnector,
          child,
        ) {
          final isIndividual = FlavorConstants.isIndividual(flavorConfig.bundleId);

          if (connectionStateUpdate.connectionState == DeviceConnectionState.disconnected &&
              isIndividual) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (FlavorConstants.isIndividual(flavorConfig.bundleId)) {
                bleScanner.startScan();

                context.read<LandingBloc>().add(ChangeBottomBarIndexEvent(currentIndex: 0));

                return;
              }
              final specificIndex = FlavorConstants.isIndividual(flavorConfig.bundleId) ? 1 : 0;
              bleScanner.startScan();
              AppRoute.pushReplacementNamed(
                context,
                RouteConstants.bleSearchPage,
                arguments: SessionTypeParams(
                  presetProgram: sessionTypeParams.presetProgram,
                  sessionType: sessionTypeParams.sessionType,
                  subuser: subuser,
                  subuserId: subuser.uid!,
                ),
                specificIndex: specificIndex,
              );
            });
            /*context.read<StimulationBloc>().add(
                  StimulationCreateEvent(
                    commandType: 'disconnect',
                    subuserUid: subuser.uid!,
                  ),
                );*/
          }
          if (connectionStateUpdate.connectionState == DeviceConnectionState.disconnected &&
              FlavorConstants.isHealthPro(flavorConfig.bundleId)) {
            final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();
            final devices = multipleDeviceBloc.state.devices;

            for (MultipleDeviceEntity element in devices ?? []) {
              if (element.bleId == connectionStateUpdate.deviceId &&
                  element.deviceId == deviceName) {
                multipleDeviceBloc.add(
                  ChangeConnectedDeviceStatus(
                    device: element,
                    status: DeviceStatusEnum.disconnected,
                  ),
                );

                WidgetsBinding.instance.addPostFrameCallback((ctx) {
                  AppRoute.pop(_);
                });
              }
            }
          }

          return StimulationControlWidget(
            deviceConnector: bleDeviceConnector,
            deviceId: deviceId,
            deviceInteractor: bleDeviceInteractor,
            deviceName: deviceName,
            isContinuousStimulation: isContinuousStimulation,
            sessionTypeParams: sessionTypeParams,
            subuser: subuser,
          );
        },
      ),
    );
  }
}
