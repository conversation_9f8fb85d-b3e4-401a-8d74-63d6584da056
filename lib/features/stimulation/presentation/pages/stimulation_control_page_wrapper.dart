import 'package:flutter/material.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:provider/provider.dart';

import '../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../parameter/domain/params/session_type.dart';
import '../../../parameter/domain/params/session_type_params.dart';
import '../../../parameter/presentation/pages/widgets/preset_programs/preset_program.dart';
import '../bloc/stimulation_bloc.dart';
import 'stimulation_control_page.dart';

class StimulationControlPageWrapper extends StatefulWidget {
  const StimulationControlPageWrapper({super.key});

  @override
  State<StimulationControlPageWrapper> createState() => _StimulationControlPageWrapperState();
}

class _StimulationControlPageWrapperState extends State<StimulationControlPageWrapper> {
  ({
    DeviceConnectionState connectionState,
    PresetProgram? presetProgram,
    SessionType? sessionType,
  })? _previousData;

  @override
  Widget build(BuildContext context) {
    return Selector2<
        ConnectionStateUpdate,
        StimulationBloc,
        ({
          DeviceConnectionState connectionState,
          PresetProgram? presetProgram,
          SessionType? sessionType,
        })>(
      builder: (ctx, data, child) {
        final connectionStateUpdate = ctx.read<ConnectionStateUpdate>();
        final authBloc = ctx.read<AuthBloc>();
        final subuser = authBloc.currentUser?.defaultSubuser;

        bool isSelectorChanged = _previousData != null &&
            (_previousData?.connectionState != data.connectionState ||
                _previousData?.presetProgram != data.presetProgram ||
                _previousData?.sessionType != data.sessionType);

        _previousData = data;

        bool isContinuousStimulation = !isSelectorChanged;

        return (connectionStateUpdate.deviceId).isEmpty || subuser == null
            ? const Scaffold(
                body: Center(child: Text('Device connection required')),
              )
            : StimulationControlPage(
                deviceId: connectionStateUpdate.deviceId,
                isContinuousStimulation: isContinuousStimulation,
                key: ValueKey(
                  "${data.connectionState.name}${data.sessionType?.name}${data.presetProgram?.name}",
                ),
                sessionTypeParams: SessionTypeParams(
                  presetProgram:
                      data.sessionType == SessionType.preSetPrograms ? data.presetProgram : null,
                  sessionType: data.sessionType ?? SessionType.quickStart,
                  subuserId: subuser.uid!,
                ),
                subuser: subuser,
              );
      },
      selector: (ctx, connectionStateUpdate, stimulationBloc) => (
        connectionState: connectionStateUpdate.connectionState,
        sessionType: stimulationBloc.currentSessionType,
        presetProgram: stimulationBloc.presetProgramType,
      ),
    );
  }
}
