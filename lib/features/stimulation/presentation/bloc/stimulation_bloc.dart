// ignore_for_file: prefer-extracting-function-callbacks, avoid-long-functions, avoid-long-files, unnecessary-trailing-comma

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/ble/ble_constants.dart';
import 'package:vagustimpro/core/ble/ble_device_connector.dart';
import 'package:vagustimpro/core/ble/ble_device_interactor.dart';
import 'package:vagustimpro/core/navigator/app_navigator.dart';
import 'package:vagustimpro/core/services/secure_storage_service.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/parameter/domain/params/session_type.dart';
import 'package:vagustimpro/features/stimulation/domain/entities/stimulation_entity.dart';
import 'package:vagustimpro/features/stimulation/domain/params/change_intensity_params.dart';
import 'package:vagustimpro/features/stimulation/domain/params/get_subuser_stimulations_params.dart';
import 'package:vagustimpro/features/stimulation/domain/params/stimulation_connect_ble_device_params.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/create_stimulation_history_usecase.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/delete_stimulation_usecase.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/get_subuser_stimulations.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/change_intensity_usecase.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/stimulation_connect_ble_device_usecase.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/stimulation_disconect_ble_device.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/stimulation_get_default_configs.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/stimulation_pause.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/stimulation_read_ble_device_configs.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/stimulation_start.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/stimulation_stop.dart';
import 'package:vagustimpro/features/stimulation/domain/usecases/stimulation_update_ble_device_configs.dart';

import '../../../../core/app_config/app_strings.dart';
import '../../../../core/helpers/analytic_helper.dart';
import '../../../home/<USER>/params/activity_params.dart';
import '../../../home/<USER>/bloc/home_bloc.dart';
import '../../../parameter/presentation/pages/widgets/preset_programs/preset_program.dart';
import '../widgets/utils/device_streams.dart';

part 'stimulation_event.dart';
part 'stimulation_state.dart';
part 'stimulation_mixin.dart';

class StimulationBloc extends Bloc<StimulationEvent, StimulationState> with StimulationMixin {
  String deviceStatus = 'stop';
  int batteryLevel = 0;
  int currentLeftIntensity = 1;
  int currentRightIntensity = 1;

  String currentLeft = '';
  String currentRight = '';
  String voltageFeedback = '';
  String statusOnOff = '';
  int timeLeft = 0;

  String embeddedVersion = '';
  String embeddedMAC = '';
  StimulationEntity? currentStimulation;

  int historyFrequency = 0;
  int historyPulseWidth = 0;
  int historyTotalDuration = 0;
  int historyOnDuration = 0;
  int historyDurationForOffTime = 0;

  List<StimulationEntity> currentStimulations = [];

  SessionType? currentSessionType;
  PresetProgram? presetProgramType;

  // Ble Streams.
  StreamSubscription<List<int>>? mainBleStream;
  StreamSubscription<List<int>>? stimulationStatusStream;
  StreamSubscription<List<int>>? batteryLevelStream;
  StreamSubscription<List<int>>? stimulationTimeAndIntensityStream;
  final deviceStreamsMap = <String, DeviceStreams>{};

  final StimulationListenStreamsUseCase _stimulationReadBleDeviceConfigs;
  final ChangeIntensityUseCase _stimulationChangeEarElectrodesIntensity;
  final StimulationConnectBleDeviceUseCase _stimulationConnectBleDevice;
  final StimulationDisconnectBleDeviceUseCase _stimulationDisconnectBleDevice;
  final StimulationGetDefaultConfigsUseCase _stimulationGetDefaultConfigs;
  final StimulationStartUseCase _stimulationStart;
  final StimulationPauseUseCase _stimulationPause;
  final StimulationStopUseCase _stimulationStop;
  final StimulationUpdateBleDeviceConfigsUseCase _stimulationUpdateBleDeviceConfigs;

  final SecureStorageService _secureStorageService;

  final CreateStimulationHistoryUseCase _createStimulationHistoryUseCase;
  final DeleteStimulationUseCase _deleteStimulationUseCase;
  final GetSubuserStimulations _getSubuserStimulations;

  StimulationBloc({
    required CreateStimulationHistoryUseCase createStimulationHistoryUseCase,
    required DeleteStimulationUseCase deleteStimulationUseCase,
    required GetSubuserStimulations getSubuserStimulations,
    required SecureStorageService secureStorageService,
    required ChangeIntensityUseCase stimulationChangeEarElectrodesIntensity,
    required StimulationConnectBleDeviceUseCase stimulationConnectBleDevice,
    required StimulationDisconnectBleDeviceUseCase stimulationDisconnectBleDevice,
    required StimulationGetDefaultConfigsUseCase stimulationGetDefaultConfigs,
    required StimulationPauseUseCase stimulationPause,
    required StimulationListenStreamsUseCase stimulationReadBleDeviceConfigs,
    required StimulationStartUseCase stimulationStart,
    required StimulationStopUseCase stimulationStop,
    required StimulationUpdateBleDeviceConfigsUseCase stimulationUpdateBleDeviceConfigs,
  })  : _stimulationReadBleDeviceConfigs = stimulationReadBleDeviceConfigs,
        _secureStorageService = secureStorageService,
        _stimulationChangeEarElectrodesIntensity = stimulationChangeEarElectrodesIntensity,
        _stimulationConnectBleDevice = stimulationConnectBleDevice,
        _stimulationDisconnectBleDevice = stimulationDisconnectBleDevice,
        _stimulationGetDefaultConfigs = stimulationGetDefaultConfigs,
        _stimulationStart = stimulationStart,
        _stimulationPause = stimulationPause,
        _stimulationStop = stimulationStop,
        _stimulationUpdateBleDeviceConfigs = stimulationUpdateBleDeviceConfigs,
        _createStimulationHistoryUseCase = createStimulationHistoryUseCase,
        _deleteStimulationUseCase = deleteStimulationUseCase,
        _getSubuserStimulations = getSubuserStimulations,
        super(StimulationInitial()) {
    on<StimulationReadBleDeviceConfigsEvent>(_onReadBleDeviceConfigs);
    on<DeviceStatusReadEvent>(_onReadDeviceStatus);
    on<DeviceInfoReadEvent>(_onReadDeviceInfo);
    on<BatteryLevelReadEvent>(_onReadBatteryLevel);
    on<StimulationTimeAndIntensityReadEvent>(_onReadStimulationTimeAndIntensity);
    on<StimulationParametersReadEvent>(_onReadStimulationParameters);
    on<ChangeEarIntensityEvent>(_onChangeEarElectrodesIntensity);
    on<StimulationConnectBleDeviceEvent>(_onConnectBleDevice);
    on<StimulationDisconnectBleDeviceEvent>(_onDisconnectBleDevice);
    on<StimulationGetDefaultConfigsEvent>(_onGetDefaultConfigs);
    on<StimulationStartEvent>(_onStart);
    on<StimulationPauseEvent>(_onPause);
    on<StimulationStopEvent>(_onStop);
    on<StimulationUpdateBleDeviceConfigsEvent>(_onUpdateBleDeviceConfigs);
    on<StimulationCreateEvent>(_onCreateStimulationHistory);
    on<StimulationDeleteEvent>(_onDeleteStimulation);
    on<StimulationGetSubuserStimulationsEvent>(_onGetSubuserStimulations);
    on<FailureEvent>(_onFailure);
  }

  static int findValueForLetter(String inputString, String letter) {
    final parts = inputString.split(';');
    for (final part in parts) {
      if (part.startsWith(letter)) {
        // Harf bulunursa, sayıyı alır, ancak substring işlemi sırasında eksik değerler varsa hata vermez.
        final number = part.substring(1);
        if (number.isNotEmpty) {
          return int.parse(number);
        }
      }
    }

    return 0;
  }

  Future<void> _onCreateStimulationHistory(
    StimulationCreateEvent event,
    Emitter<StimulationState> emit,
  ) async {
    _stimulationReqAnalytic(event);
    final res = await _createStimulationHistoryUseCase.calculate(
      CreateStimulationParams(
        commandType: event.commandType,
        frequency: historyFrequency,
        offDuration: historyDurationForOffTime,
        onDuration: historyOnDuration,
        pulseWidth: historyPulseWidth,
        sessionType: event.sessionType,
        subuserUid: event.subuserUid,
        totalDuration: historyTotalDuration * 60,
      ),
    );

    res.fold(
      (fail) {
        _stimulationFailedAnalytic(event, fail.message);
        emit(StimulationFailure(fail.message));
      },
      (_) {
        _stimulationSuccessAnalytic(event);
        emit(StimulationCreateSuccess());
      },
    );
  }

  void _onFailure(
    FailureEvent event,
    Emitter<StimulationState> emit,
  ) {
    emit(StimulationFailure(event.message));
    add(StimulationGetSubuserStimulationsEvent(
      GetIt.instance.get<AuthBloc>().currentUser?.defaultSubuser?.uid ?? '',
    ));

    _stimulationFailedAnalytic(event, event.message);
  }

  Future<void> _onDeleteStimulation(
    StimulationDeleteEvent event,
    Emitter<StimulationState> emit,
  ) async {
    _stimulationReqAnalytic(event);
    final res = await _deleteStimulationUseCase.calculate(
      DeleteStimulationParams(uid: event.uid),
    );

    res.fold(
      (fail) {
        _stimulationFailedAnalytic(event, fail.message);
        emit(StimulationFailure(fail.message));
      },
      (_) {
        _stimulationSuccessAnalytic(event);
        emit(StimulationDeleteSuccess());
      },
    );
  }

  Future<void> _onGetSubuserStimulations(
    StimulationGetSubuserStimulationsEvent event,
    Emitter<StimulationState> emit,
  ) async {
    emit(StimulationLoading());

    _stimulationReqAnalytic(event);
    final res = await _getSubuserStimulations.calculate(
      GetSubuserStimulationsParams(subuserUid: event.subuserUid),
    );

    res.fold(
      (fail) {
        _stimulationFailedAnalytic(event, fail.message);
        emit(StimulationFailure(fail.message));
      },
      (stimulations) {
        currentStimulations = stimulations;
        _stimulationSuccessAnalytic(
          event,
          data: currentStimulations.isNotEmpty ? currentStimulations.first : null,
        );
        emit(StimulationGetSubuserStimulationsSuccess(stimulations));
      },
    );
  }

  Future<void> _onReadBleDeviceConfigs(
    StimulationReadBleDeviceConfigsEvent event,
    Emitter<StimulationState> emit,
  ) async {
    _stimulationReqAnalytic(event);
    emit(StimulationLoading());
    final res = await _stimulationReadBleDeviceConfigs.calculate(
      StimulationReadBleDeviceConfigsParams(deviceId: event.deviceId),
    );

    res.fold(
      (fail) {
        _stimulationFailedAnalytic(event, fail.message);
        emit(StimulationFailure(fail.message));
      },
      (bleStreams) async {
        await deviceStreamsMap[event.deviceId]?.disposeAll();

        if (!deviceStreamsMap.containsKey(event.deviceId)) {
          deviceStreamsMap[event.deviceId] = DeviceStreams(event.deviceId);
        }

        deviceStreamsMap[event.deviceId]?.mainBleStream = bleStreams.first.listen(
          (data) {
            final resultData = String.fromCharCodes(data);

            currentLeftIntensity = findValueForLetter(resultData, BleConstants.leftIntensityChar);
            currentRightIntensity = findValueForLetter(resultData, BleConstants.rightIntensityChar);
            currentStimulation = StimulationEntity(
              deviceId: event.deviceId,
              durationOff: findValueForLetter(resultData, BleConstants.durOffChar),
              durationOn: findValueForLetter(resultData, BleConstants.durOnChar),
              frequency: findValueForLetter(resultData, BleConstants.frequencyChar),
              leftIntensity: currentLeftIntensity,
              outMode: findValueForLetter(resultData, BleConstants.outModeChar),
              pulseWidth: findValueForLetter(resultData, BleConstants.pulseWidthChar),
              rightIntensity: currentRightIntensity,
              totalDuration: findValueForLetter(resultData, BleConstants.totalDurationChar),
              voltage: findValueForLetter(resultData, BleConstants.voltageChar),
              waveform: findValueForLetter(resultData, BleConstants.waveformChar),
            );
            _stimulationSuccessAnalytic(event, data: currentStimulation);
            add(StimulationParametersReadEvent(currentStimulation!));
          },
          onError: (error) {
            debugPrint("Main stream subscription error: $error");
            if (!isClosed) {
              add(FailureEvent("Device connection lost: $error"));
            }
          },
        );

        await event.bleDeviceInteractor.readCharacteristic(
          characteristic: QualifiedCharacteristic(
            characteristicId: BleConstants.controlChar0UUID,
            deviceId: event.deviceId,
            serviceId: BleConstants.controlServiceUUID,
          ),
        );

        deviceStreamsMap[event.deviceId]?.stimulationStatusStream =
            bleStreams[1].listen((data) async {
          deviceStatus = String.fromCharCodes(data);

          if (deviceStatus == 'stop' && (currentLeftIntensity != 0 || currentRightIntensity != 0)) {
            await Future.delayed(const Duration(milliseconds: 200));
            add(ChangeEarIntensityEvent(
              bleDeviceInteractor: event.bleDeviceInteractor,
              deviceId: event.deviceId,
              leftElectrodeIntensity: '0',
              rightElectrodeIntensity: '0',
            ));
          }
          add(DeviceStatusReadEvent(deviceStatus));
        });

        // Status Service Char 1.
        await event.bleDeviceInteractor.readCharacteristic(
          characteristic: QualifiedCharacteristic(
            characteristicId: BleConstants.controlChar1UUID,
            deviceId: event.deviceId,
            serviceId: BleConstants.controlServiceUUID,
          ),
        );

        await Future.delayed(const Duration(seconds: 1));

        // Status Service Char 0 - Device Information.
        final data = await event.bleDeviceInteractor.readCharacteristic(
          characteristic: QualifiedCharacteristic(
            characteristicId: BleConstants.statusChar0UUID,
            deviceId: event.deviceId,
            serviceId: BleConstants.statusServiceUUID,
          ),
        );

        final resultData = String.fromCharCodes(data);
        final resultDataList = resultData.split('_').toList();

        embeddedVersion = resultDataList.first;
        embeddedMAC = resultDataList[1];

        if (resultDataList.length >= 2) {
          embeddedVersion = resultDataList.first;
          embeddedMAC = resultDataList[1];

          add(DeviceInfoReadEvent(embeddedMAC: embeddedMAC, embeddedVersion: embeddedVersion));
        } else {
          debugPrint('Invalid product info data received: $resultData');

          AnalyticHelper.instance
              .track(AppStrings.stimulationReadBleDeviceConfigsFailed, properties: {
            "device_id": event.deviceId,
            "device_mac": embeddedMAC,
            "device_version": embeddedVersion,
            "result_data": resultData,
            "result_data_list": resultDataList,
          });
        }

        deviceStreamsMap[event.deviceId]?.batteryLevelStream = bleStreams[2].listen((dynamic data) {
          final resultData = String.fromCharCodes(data as List<int>);
          if (kDebugMode) {
            print("Battery Level: $resultData");
          }
          batteryLevel = int.parse(resultData);
          add(BatteryLevelReadEvent(batteryLevel));
        });
        deviceStreamsMap[event.deviceId]?.stimulationTimeAndIntensity =
            bleStreams[3].asBroadcastStream();
        deviceStreamsMap[event.deviceId]?.stimulationTimeAndIntensityStream =
            deviceStreamsMap[event.deviceId]?.stimulationTimeAndIntensity?.listen((data) {
          final resultData = String.fromCharCodes(data);
          /*  if (kDebugMode) {
            print("Stimulation Time and Intensity: $resultData");
          }*/
          final resultDataList = resultData.split('_').toList();
          currentLeft = resultDataList.first;
          currentRight = resultDataList[1];
          voltageFeedback = resultDataList[2];
          statusOnOff = resultDataList[3];
          timeLeft = resultDataList.length > 4 && resultDataList[4].trim().isNotEmpty
              ? int.tryParse(resultDataList[4].trim()) ?? 0
              : 0;
          add(StimulationTimeAndIntensityReadEvent(
            currentLeft: currentLeft,
            currentRight: currentRight,
            deviceId: event.deviceId,
            statusOnOff: statusOnOff,
            timeLeft: timeLeft,
            voltageFeedback: voltageFeedback,
          ));
        });
      },
    );
  }

  void _onReadDeviceStatus(
    DeviceStatusReadEvent event,
    Emitter<StimulationState> emit,
  ) {
    _stimulationReqAnalytic(event);

    if (event.deviceStatus == 'fin') {
      _stimulationSuccessAnalytic(event);
      emit(StimFinishSuccess(event.deviceStatus));
    } else {
      _stimulationSuccessAnalytic(event);
      emit(DeviceStatusReadSuccess(event.deviceStatus));
    }
  }

  void _onReadDeviceInfo(
    DeviceInfoReadEvent event,
    Emitter<StimulationState> emit,
  ) {
    _stimulationSuccessAnalytic(event);
    emit(DeviceInfoReadSuccess(
      embeddedMAC: event.embeddedMAC,
      embeddedVersion: event.embeddedVersion,
    ));
  }

  void _onReadBatteryLevel(
    BatteryLevelReadEvent event,
    Emitter<StimulationState> emit,
  ) {
    _stimulationSuccessAnalytic(event);
    emit(BatteryLevelReadSuccess(event.batteryLevel));
  }

  static void _onReadStimulationTimeAndIntensity(
    StimulationTimeAndIntensityReadEvent event,
    Emitter<StimulationState> emit,
  ) {
    emit(StimulationTimeAndIntensityReadSuccess(
      currentLeft: event.currentLeft,
      currentRight: event.currentRight,
      deviceId: event.deviceId,
      statusOnOff: event.statusOnOff,
      timeLeft: event.timeLeft,
      voltageFeedback: event.voltageFeedback,
    ));
  }

  static void _onReadStimulationParameters(
    StimulationParametersReadEvent event,
    Emitter<StimulationState> emit,
  ) {
    emit(StimulationParametersReadSuccess(event.stimulation));
  }

  FutureOr<void> _onChangeEarElectrodesIntensity(
    ChangeEarIntensityEvent event,
    Emitter<StimulationState> emit,
  ) {
    _stimulationSuccessAnalytic(event);
    _stimulationChangeEarElectrodesIntensity.calculate(ChangeIntensityParams(
      bleDeviceInteractor: event.bleDeviceInteractor,
      deviceId: event.deviceId,
      leftIntensity: event.leftElectrodeIntensity,
      rightIntensity: event.rightElectrodeIntensity,
    ));
  }

  FutureOr<void> _onConnectBleDevice(
    StimulationConnectBleDeviceEvent event,
    Emitter<StimulationState> emit,
  ) {
    _stimulationSuccessAnalytic(event);
    _stimulationConnectBleDevice.calculate(StimulationConnectBleDeviceParams(
      bleDeviceConnector: event.bleDeviceConnector,
      deviceId: event.deviceId,
    ));
  }

  FutureOr<void> _onDisconnectBleDevice(
    StimulationDisconnectBleDeviceEvent event,
    Emitter<StimulationState> emit,
  ) {
    _stimulationSuccessAnalytic(event);
    _stimulationDisconnectBleDevice.calculate(StimulationDisconnectBleDeviceParams(
      bleDeviceConnector: event.bleDeviceConnector,
      deviceId: event.deviceId,
    ));
  }

  FutureOr<void> _onGetDefaultConfigs(
    StimulationGetDefaultConfigsEvent event,
    Emitter<StimulationState> emit,
  ) {
    _stimulationSuccessAnalytic(event);
    _stimulationGetDefaultConfigs.calculate(StimulationGetDefaultConfigsParams(
      bleDeviceInteractor: event.bleDeviceInteractor,
      deviceId: event.deviceId,
    ));
  }

  FutureOr<void> _onStart(
    StimulationStartEvent event,
    Emitter<StimulationState> emit,
  ) {
    _stimulationSuccessAnalytic(event);
    _stimulationStart.calculate(StimulationStartParams(
      bleDeviceInteractor: event.bleDeviceInteractor,
      deviceId: event.deviceId,
    ));
  }

  FutureOr<void> _onPause(
    StimulationPauseEvent event,
    Emitter<StimulationState> emit,
  ) {
    _stimulationSuccessAnalytic(event);
    _stimulationPause.calculate(StimulationPauseParams(
      bleDeviceInteractor: event.bleDeviceInteractor,
      deviceId: event.deviceId,
    ));
  }

  FutureOr<void> _onStop(
    StimulationStopEvent event,
    Emitter<StimulationState> emit,
  ) {
    _stimulationSuccessAnalytic(event);
    _stimulationStop.calculate(StimulationStopParams(
      bleDeviceInteractor: event.bleDeviceInteractor,
      deviceId: event.deviceId,
    ));

    add(StimulationGetSubuserStimulationsEvent(event.subuserUid));

    GetIt.instance<HomeBloc>().add(GetActivityEvent(
      params: ActivityParams(subuserUid: event.subuserUid),
    ));
  }

  Future<FutureOr<void>> _onUpdateBleDeviceConfigs(
    StimulationUpdateBleDeviceConfigsEvent event,
    Emitter<StimulationState> emit,
  ) async {
    _stimulationSuccessAnalytic(event);

    String confString = '${BleConstants.pulseWidthChar}${event.pulseWidth};'
        '${BleConstants.frequencyChar}${event.frequency};'
        '${BleConstants.durOnChar}${event.durationForOnTime};'
        '${BleConstants.durOffChar}${event.durationForOffTime};'
        '${BleConstants.totalDurationChar}${int.parse(event.totalDuration) * 60};';

    confString += '+${confString.length}';

    historyFrequency = int.parse(event.frequency);
    historyPulseWidth = int.parse(event.pulseWidth);
    historyTotalDuration = int.parse(event.totalDuration);
    historyOnDuration = int.parse(event.durationForOnTime);
    historyDurationForOffTime = int.parse(event.durationForOffTime);

    await _stimulationUpdateBleDeviceConfigs.calculate(StimulationUpdateBleDeviceConfigsParams(
      bleDeviceInteractor: event.bleDeviceInteractor,
      confString: confString,
      deviceId: event.deviceId,
    ));
  }
}
