part of '../stimulation_control_widget.dart';

mixin StimulationProgressMixin on State<StimulationControlWidget> {
  // Constants for intensity thresholds and adjustments.
  static const _lowIntensityThreshold = 150.0;
  static const _mediumIntensityThreshold = 300.0;
  static const _lowIntensityIncrement = 30.0;
  static const _mediumIntensityIncrement = 10.0;
  static const _highIntensityIncrement = 5.0;
  static const _minIntensity = 0.0;
  static const _maxIntensity = 1000.0;

  bool isHeadphoneLSelected = true;
  double leftProgress = 0;
  double rightProgress = 0;
  bool isHeadLConnected = true;
  bool isHeadRConnected = true;

  void onPressedLeftPlus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }

    _incrementLeftProgress();
    _updateEarIntensity();
  }

  void _incrementLeftProgress() {
    setState(() {
      if (leftProgress < _lowIntensityThreshold) {
        leftProgress += _lowIntensityIncrement;
      } else if (leftProgress < _mediumIntensityThreshold) {
        leftProgress += _mediumIntensityIncrement;
      } else {
        leftProgress += _highIntensityIncrement;
      }

      if (leftProgress > _maxIntensity) {
        leftProgress = _maxIntensity;
      }
    });
  }

  void _updateEarIntensity() {
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      ChangeEarIntensityEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
        leftElectrodeIntensity: leftProgress.toStringAsFixed(0),
        rightElectrodeIntensity: rightProgress.toStringAsFixed(0),
      ),
    );
  }

  void onPressedLeftMinus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }
    _decrementLeftProgress();
    _updateEarIntensity();
  }

  void _decrementLeftProgress() {
    setState(() {
      if (leftProgress > _mediumIntensityThreshold) {
        leftProgress -= _highIntensityIncrement;
      } else if (leftProgress > _lowIntensityThreshold) {
        leftProgress -= _mediumIntensityIncrement;
      } else {
        leftProgress -= _lowIntensityIncrement;
      }

      if (leftProgress < _minIntensity) {
        leftProgress = _minIntensity;
      }
    });
  }

  void onPressedRightMinus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }
    _decrementRightProgress();
    _updateEarIntensity();
  }

  void _decrementRightProgress() {
    setState(() {
      if (rightProgress > _mediumIntensityThreshold) {
        rightProgress -= _highIntensityIncrement;
      } else if (rightProgress > _lowIntensityThreshold) {
        rightProgress -= _mediumIntensityIncrement;
      } else {
        rightProgress -= _lowIntensityIncrement;
      }

      if (rightProgress < _minIntensity) {
        rightProgress = _minIntensity;
      }
    });
  }

  void onPressedRightPlus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }

    _incrementRightProgress();
    _updateEarIntensity();
  }

  void _incrementRightProgress() {
    setState(() {
      if (rightProgress < _lowIntensityThreshold) {
        rightProgress += _lowIntensityIncrement;
      } else if (rightProgress < _mediumIntensityThreshold) {
        rightProgress += _mediumIntensityIncrement;
      } else {
        rightProgress += _highIntensityIncrement;
      }

      if (rightProgress > _maxIntensity) {
        rightProgress = _maxIntensity;
      }
    });
  }

  void _resetProgress() {
    setState(() {
      leftProgress = 0.0;
      rightProgress = 0.0;
    });
  }

  void _updateProgressFromParameters({
    required double leftIntensity,
    required double rightIntensity,
  }) {
    setState(() {
      leftProgress = leftIntensity;
      rightProgress = rightIntensity;
    });
  }
}
