part of '../stimulation_control_widget.dart';

mixin StimulationLifecycleMixin on State<StimulationControlWidget> {
  final bundleId =
      GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

  @override
  void initState() {
    super.initState();

    // Sends an analytics event to track user.
    AnalyticHelper.instance.track(AppStrings.stimulationControlPage);
    Future.microtask(() {
      if (!widget.isContinuousStimulation && this is StimulationControlTutorialMixin) {
        (this as StimulationControlTutorialMixin).createAndShowTutorial();
      }

      if (mounted) {
        context.read<OtaBloc>().add(StatusEvent(status: OtaStatus.initial));

        context.read<StimulationBloc>().add(StimulationReadBleDeviceConfigsEvent(
              bleDeviceInteractor: widget.deviceInteractor,
              deviceId: widget.deviceId,
              deviceName: widget.deviceName ?? "",
            ));
      }
    });
    if (!widget.isContinuousStimulation) {
      context.read<NewParameterBloc>().add(ParameterGetRequstedEvent(
            params: widget.sessionTypeParams,
          ));
    }
    final stimulationBloc = context.read<StimulationBloc>();

    stimulationBloc.currentSessionType = widget.sessionTypeParams.sessionType;
    stimulationBloc.presetProgramType = widget.sessionTypeParams.presetProgram;

    if (FlavorConstants.isHealthPro(bundleId)) {
      final multiDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();
      final device = multiDeviceBloc.state.devices
          ?.firstWhereOrNull((element) => element.bleId == widget.deviceId);

      device?.isConnectedStim = true;

      if (device != null) multiDeviceBloc.add(ChangeConnectedDeviceStatus(device: device));
    }
  }

  void _handleOnPopInvoked() async {
    final bloc = context.read<StimulationBloc>();
    await bloc.deviceStreamsMap[widget.deviceId]?.disposeAll();
  }

  @override
  void dispose() {
    if (this is StimulationControlTutorialMixin) {
      (this as StimulationControlTutorialMixin).tutorialCoachMark?.finish();
    }
    super.dispose();
  }
}
