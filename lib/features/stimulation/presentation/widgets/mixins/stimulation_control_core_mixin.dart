part of '../stimulation_control_widget.dart';

mixin StimulationControlCoreMixin on State<StimulationControlWidget> {
  // Constants for command types.
  static const _startCommand = 'start';
  static const _stopCommand = 'stop';
  static const _pauseCommand = 'pause';

  // Constants for device status.
  static const _pauseStatus = 'pause';

  bool isStimOffTime = false;

  // Get Total duration for user.
  int? timeLeft;

  final bundleId =
      GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

  void onPressedStart() {
    /*final timeOfUsageEnums = context.read<TimeOfUsageBloc>().state.timeOfUsageEnums;
    final userMaxRemainingSecond = context.read<TimeOfUsageBloc>().state.userMaxRemainingSecond;
    final userMaxRemainingMinute =
        ((userMaxRemainingSecond ?? 0) ~/ 60) > 0 ? ((userMaxRemainingSecond ?? 0) ~/ 60) : 0;

    if (timeOfUsageEnums != null) {
      AppDialog(
        cancelButtonOnTap: () => timeOfUsageEnums.timeOfUsageCancelButtonOnTap(context)(),
        cancelButtonText: timeOfUsageEnums.timeOfUsageCancelButtonText,
        context: context,
        description: timeOfUsageEnums.timeOfUsageDescription(userMaxRemainingMinute),
        okButtonOnTap: () => timeOfUsageEnums.timeOfUsageOkButtonOnTap(context)(),
        okButtonText: timeOfUsageEnums.timeOfUsageOkButtonText,
        title: timeOfUsageEnums.timeOfUsageTitle,
      ).show;
      return;
    }*/

    if (FlavorConstants.isHealthPro(bundleId)) {
      final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();

      final connectedDevices = multipleDeviceBloc.state.devices;

      for (MultipleDeviceEntity element in connectedDevices ?? []) {
        if (element.bleId == widget.deviceId) {
          multipleDeviceBloc.add(
            ChangeConnectedDeviceStatus(
              device: element,
              status: DeviceStatusEnum.ongoing,
              subuser: widget.subuser,
            ),
          );
        }
      }
    }
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      StimulationStartEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
      ),
    );

    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: _startCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
  }

  void onPressedStop(DeviceStatusReadSuccess state) {
    _updateMultipleDevicesStatus(state);

    if (state.deviceStatus == _pauseStatus) {
      _stopStimulation();
    } else {
      _pauseStimulation();
    }
  }

  void _updateMultipleDevicesStatus(DeviceStatusReadSuccess state) {
    if (!FlavorConstants.isHealthPro(bundleId)) return;

    final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();
    final connectedDevices = multipleDeviceBloc.state.devices;

    for (MultipleDeviceEntity element in connectedDevices ?? []) {
      if (element.bleId == widget.deviceId) {
        final status = state.deviceStatus == _pauseStatus
            ? DeviceStatusEnum.completed
            : DeviceStatusEnum.paused;

        multipleDeviceBloc.add(
          ChangeConnectedDeviceStatus(
            device: element,
            status: status,
            subuser: widget.subuser,
          ),
        );
      }
    }
  }

  void _stopStimulation() {
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: _stopCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
    stimulationBloc.add(
      StimulationStopEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
        subuserUid: _safeSubuserUid,
      ),
    );

    _resetProgress();
    _handleOnTapHeartRateDialog();
  }

  void _pauseStimulation() {
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: _pauseCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
  }

  String get _safeSubuserUid => widget.subuser.uid ?? '';

  void onPressedHelp() {
    final stimulationBloc = context.read<StimulationBloc>();

    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: _stopCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
    stimulationBloc.add(
      StimulationStopEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
        subuserUid: _safeSubuserUid,
      ),
    );

    _resetProgress();
  }

  // These methods need to be implemented by the main mixin.
  void _resetProgress();

  void _handleOnTapHeartRateDialog();
}
