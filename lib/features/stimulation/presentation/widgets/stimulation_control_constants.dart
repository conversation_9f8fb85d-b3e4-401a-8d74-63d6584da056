/// Constants used throughout the stimulation control functionality.
/// This class centralizes all magic strings, numbers, and configuration values
/// to improve maintainability and comply with DCM linting rules.
abstract final class StimulationControlConstants {
  // Command types for stimulation control.
  static const startCommand = 'start';
  static const stopCommand = 'stop';
  static const pauseCommand = 'pause';

  // Device status values.
  static const pauseStatus = 'pause';
  static const stopStatus = 'stop';

  // Dialog messages and titles.
  static const parameterNotFoundTitle = 'Parameter Not Found';
  static const parameterNotFoundDescription =
      'Session type parameter not found. Please choose a different session type to proceed.';

  // Intensity thresholds and adjustments.
  static const lowIntensityThreshold = 150.0;
  static const mediumIntensityThreshold = 300.0;
  static const lowIntensityIncrement = 30.0;
  static const mediumIntensityIncrement = 10.0;
  static const highIntensityIncrement = 5.0;
  static const minIntensity = 0.0;
  static const maxIntensity = 1000.0;

  // Progress reset values.
  static const resetProgressValue = 0.0;

  // User type constants.
  static const individualsUserType = 'individuals';

  // Device name constants.
  static const incorrectDeviceMessage = "Incorrect Product";

  // Permission names.
  static const currentIntensityPermission = "current_intensity";

  // Magic numbers for UI layout.
  static const lottieHeight = 60.0;
  static const heightMultiplier = 0.7;
  static const headphoneControlHeightMultiplier = 0.44;

  // Device status string literals used in UI comparisons.
  static const deviceStatusPause = 'pause';
  static const deviceStatusStop = 'stop';

  // Empty string default.
  static const emptyString = '';

  // Default device name fallback.
  static const defaultDeviceName = "";
}
