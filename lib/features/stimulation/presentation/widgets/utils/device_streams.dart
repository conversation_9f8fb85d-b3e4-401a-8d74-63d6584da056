import 'dart:async';

class DeviceStreams {
  final String deviceId;
  StreamSubscription<List<int>>? mainBleStream;
  StreamSubscription<List<int>>? stimulationStatusStream;
  StreamSubscription<List<int>>? batteryLevelStream;
  StreamSubscription<List<int>>? stimulationTimeAndIntensityStream;
  Stream<List<int>>? stimulationTimeAndIntensity;

  static final _instances = <String, DeviceStreams>{};

  DeviceStreams._internal(this.deviceId);

  factory DeviceStreams(String deviceId) {
    if (_instances.containsKey(deviceId)) {
      return _instances[deviceId]!;
    }
    final instance = DeviceStreams._internal(deviceId);
    _instances[deviceId] = instance;

    return instance;
  }

  Future<void> disposeAll() async {
    await mainBleStream?.cancel();
    await stimulationStatusStream?.cancel();
    await batteryLevelStream?.cancel();
    await stimulationTimeAndIntensityStream?.cancel();

    mainBleStream = null;
    stimulationStatusStream = null;
    batteryLevelStream = null;
    stimulationTimeAndIntensityStream = null;
  }

  Future<void> disposeAllWithTimeAndIntensity() async {
    await mainBleStream?.cancel();
    await stimulationStatusStream?.cancel();
    await batteryLevelStream?.cancel();
    await stimulationTimeAndIntensityStream?.cancel();

    mainBleStream = null;
    stimulationStatusStream = null;
    batteryLevelStream = null;
    stimulationTimeAndIntensityStream = null;
    stimulationTimeAndIntensity = null;
  }
}
