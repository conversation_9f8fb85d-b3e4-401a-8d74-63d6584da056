import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/parameter/presentation/pages/widgets/preset_programs/preset_program.dart';

import '../../../../../core/app_config/app_colors.dart';
import '../../../../subuser/presentation/widgets/calendar/widgets/history_detail_item.dart';

class StimulationControlPresetItem extends StatelessWidget {
  const StimulationControlPresetItem({super.key, required this.presetProgram});
  final PresetProgram presetProgram;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: context.paddingLow.bottom),
      child: HistoryDetailItem(
        icon: SvgPicture.asset(
          presetProgram.getIconPath,
          colorFilter: ColorFilter.mode(
            AppColors.stimulationControlTitle,
            BlendMode.srcIn,
          ),
          height: 24,
          width: 24,
        ),
        subtitle: "",
        title: PresetProgram.titleFromRequestName(presetProgram.getRequestName),
        titleStyle: AppTextStyles.stimulationControlBattery,
      ),
    );
  }
}
