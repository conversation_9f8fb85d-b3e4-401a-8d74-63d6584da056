// ignore_for_file: avoid-non-null-assertion

import 'package:flutter/material.dart';

import '../stimulation_control_widget.dart';

class StimulationControlInheritedWidget extends InheritedWidget {
  const StimulationControlInheritedWidget({
    required super.child,
    required this.dialogMixin,
    super.key,
    required this.methodsMixin,
    required this.mixin,
    required this.tutorialMixin,
  });

  static StimulationControlInheritedWidget of(BuildContext context) {
    final result = context.dependOnInheritedWidgetOfExactType<StimulationControlInheritedWidget>();
    assert(result != null, 'No StimulationControlInheritedWidget found in context');

    return result!;
  }

  final StimulationControlMixin mixin;
  final StimulationDialogMixin dialogMixin;
  final StimulationControlTutorialMixin tutorialMixin;

  final StimulationControlTutorialMethodsMixin methodsMixin;

  @override
  bool updateShouldNotify(StimulationControlInheritedWidget oldWidget) =>
      mixin != oldWidget.mixin ||
      methodsMixin != oldWidget.methodsMixin ||
      tutorialMixin != oldWidget.tutorialMixin ||
      dialogMixin != oldWidget.dialogMixin;
}
