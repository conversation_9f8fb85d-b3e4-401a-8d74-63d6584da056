// ignore_for_file: avoid-non-null-assertion

import 'package:flutter/material.dart';

import '../stimulation_control_widget.dart';

class StimulationControlInheritedWidget extends InheritedWidget {
  const StimulationControlInheritedWidget({
    required super.child,
    super.key,
    required this.methodsMixin,
    required this.mixin,
    required this.tutorialMixin,
  });

  final StimulationControlMixin mixin;

  final StimulationControlTutorialMixin tutorialMixin;
  final StimulationControlTutorialMethodsMixin methodsMixin;

  @override
  bool updateShouldNotify(StimulationControlInheritedWidget oldWidget) =>
      mixin != oldWidget.mixin ||
      methodsMixin != oldWidget.methodsMixin ||
      tutorialMixin != oldWidget.tutorialMixin;
}
