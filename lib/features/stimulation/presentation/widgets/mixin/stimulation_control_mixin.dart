// ignore_for_file: avoid-unrelated-type-casts, avoid-type-casts, prefer-extracting-function-callbacks

part of '../stimulation_control_widget.dart';

mixin StimulationControlMixin on State<StimulationControlWidget> {
  final remoteConfigService = GetIt.instance<RemoteConfigService>();

  bool isHeadphoneLSelected = true;
  double leftProgress = 0;
  double rightProgress = 0;
  bool isHeadLConnected = true;
  bool isHeadRConnected = true;
  bool isStimOffTime = false;

  // Get Total duration for user.
  int? timeLeft;

  void handleOnPressedStart() {
    if (FlavorConstants.isHealthPro(bundleId)) {
      final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();

      final connectedDevices = multipleDeviceBloc.state.devices;

      for (MultipleDeviceEntity element in connectedDevices ?? []) {
        if (element.bleId == widget.deviceId) {
          multipleDeviceBloc.add(
            ChangeConnectedDeviceStatus(
              device: element,
              status: DeviceStatusEnum.ongoing,
              subuser: widget.subuser,
            ),
          );
        }
      }
    }
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      StimulationStartEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
      ),
    );

    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: StimulationControlConstants.startCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
  }

  final bundleId =
      GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

  void handleOnPressedStop(DeviceStatusReadSuccess state) {
    _updateMultipleDevicesStatus(state);

    if (state.deviceStatus == StimulationControlConstants.pauseStatus) {
      _stopStimulation();
    } else {
      _pauseStimulation();
    }
  }

  void _updateMultipleDevicesStatus(DeviceStatusReadSuccess state) {
    if (!FlavorConstants.isHealthPro(bundleId)) return;

    final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();
    final connectedDevices = multipleDeviceBloc.state.devices;

    for (MultipleDeviceEntity element in connectedDevices ?? []) {
      if (element.bleId == widget.deviceId) {
        multipleDeviceBloc.add(ChangeConnectedDeviceStatus(
          device: element,
          status: state.deviceStatus == StimulationControlConstants.pauseStatus
              ? DeviceStatusEnum.connected
              : DeviceStatusEnum.paused,
          subuser: widget.subuser,
        ));
      }
    }
  }

  void _stopStimulation() {
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: StimulationControlConstants.stopCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
    stimulationBloc.add(
      StimulationStopEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
        subuserUid: _safeSubuserUid,
      ),
    );

    _resetProgress();
    (this as StimulationDialogMixin).handleOnTapHeartRateDialog();
  }

  void _pauseStimulation() {
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      StimulationPauseEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
      ),
    );
    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: StimulationControlConstants.pauseCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
  }

  void _resetProgress() {
    setState(() {
      leftProgress = StimulationControlConstants.resetProgressValue;
      rightProgress = StimulationControlConstants.resetProgressValue;
    });
  }

  void _handleUpdateProgressFromParameters({
    required double leftIntensity,
    required double rightIntensity,
  }) {
    setState(() {
      leftProgress = leftIntensity;
      rightProgress = rightIntensity;
    });
  }

  String get _safeSubuserUid => widget.subuser.uid ?? '';

  void onPressedHelp() {
    final stimulationBloc = context.read<StimulationBloc>();

    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: StimulationControlConstants.stopCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
    stimulationBloc.add(
      StimulationStopEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
        subuserUid: _safeSubuserUid,
      ),
    );

    setState(() {
      leftProgress = StimulationControlConstants.resetProgressValue;
      rightProgress = StimulationControlConstants.resetProgressValue;
    });
  }

  void handleOnPressedLeftPlus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }

    _incrementLeftProgress();
    _updateEarIntensity();
  }

  void _incrementLeftProgress() {
    setState(() {
      if (leftProgress < StimulationControlConstants.lowIntensityThreshold) {
        leftProgress = (leftProgress + StimulationControlConstants.lowIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      } else if (leftProgress >= StimulationControlConstants.lowIntensityThreshold &&
          leftProgress < StimulationControlConstants.mediumIntensityThreshold) {
        leftProgress = (leftProgress + StimulationControlConstants.mediumIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      } else {
        leftProgress = (leftProgress + StimulationControlConstants.highIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      }
    });
  }

  void _updateEarIntensity() {
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      ChangeEarIntensityEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
        leftElectrodeIntensity: leftProgress.toStringAsFixed(0),
        rightElectrodeIntensity: rightProgress.toStringAsFixed(0),
      ),
    );
  }

  void handleOnPressedLeftMinus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }
    _decrementLeftProgress();
    _updateEarIntensity();
  }

  void _decrementLeftProgress() {
    setState(() {
      if (leftProgress <= StimulationControlConstants.lowIntensityThreshold) {
        leftProgress = (leftProgress - StimulationControlConstants.lowIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      } else if (leftProgress > StimulationControlConstants.lowIntensityThreshold &&
          leftProgress <= StimulationControlConstants.mediumIntensityThreshold) {
        leftProgress = (leftProgress - StimulationControlConstants.mediumIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      } else {
        leftProgress = (leftProgress - StimulationControlConstants.highIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      }
    });
  }

  void handleOnPressedRightMinus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }
    _decrementRightProgress();
    _updateEarIntensity();
  }

  void _decrementRightProgress() {
    setState(() {
      if (rightProgress <= StimulationControlConstants.lowIntensityThreshold) {
        rightProgress = (rightProgress - StimulationControlConstants.lowIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      } else if (rightProgress > StimulationControlConstants.lowIntensityThreshold &&
          rightProgress <= StimulationControlConstants.mediumIntensityThreshold) {
        rightProgress =
            (rightProgress - StimulationControlConstants.mediumIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      } else {
        rightProgress = (rightProgress - StimulationControlConstants.highIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      }
    });
  }

  void handleOnPressedRightPlus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }

    _incrementRightProgress();
    _updateEarIntensity();
  }

  void _incrementRightProgress() {
    setState(() {
      if (rightProgress < StimulationControlConstants.lowIntensityThreshold) {
        rightProgress = (rightProgress + StimulationControlConstants.lowIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      } else if (rightProgress >= StimulationControlConstants.lowIntensityThreshold &&
          rightProgress < StimulationControlConstants.mediumIntensityThreshold) {
        rightProgress =
            (rightProgress + StimulationControlConstants.mediumIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      } else {
        rightProgress = (rightProgress + StimulationControlConstants.highIntensityIncrement).clamp(
          StimulationControlConstants.minIntensity,
          StimulationControlConstants.maxIntensity,
        );
      }
    });
  }

  @override
  void initState() {
    super.initState();

    // Sends an analytics event to track user.
    AnalyticHelper.instance.track(AppStrings.stimulationControlPage);
    Future.microtask(() {
      if (!widget.isContinuousStimulation) {
        (this as StimulationControlTutorialMixin).createAndShowTutorial();
      }

      if (mounted) {
        context.read<OtaBloc>().add(StatusEvent(status: OtaStatus.initial));

        context.read<StimulationBloc>().add(StimulationReadBleDeviceConfigsEvent(
              bleDeviceInteractor: widget.deviceInteractor,
              deviceId: widget.deviceId,
              deviceName: widget.deviceName ?? "",
            ));
      }
    });
    if (!widget.isContinuousStimulation) {
      context.read<NewParameterBloc>().add(ParameterGetRequstedEvent(
            params: widget.sessionTypeParams,
          ));
    }
    final stimulationBloc = context.read<StimulationBloc>();

    stimulationBloc.currentSessionType = widget.sessionTypeParams.sessionType;
    stimulationBloc.presetProgramType = widget.sessionTypeParams.presetProgram;

    if (FlavorConstants.isHealthPro(bundleId)) {
      final multiDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();
      final device = multiDeviceBloc.state.devices
          ?.firstWhereOrNull((element) => element.bleId == widget.deviceId);

      device?.isConnectedStim = true;

      if (device != null) multiDeviceBloc.add(ChangeConnectedDeviceStatus(device: device));
    }
  }

  void _handleOnPopInvoked() async {
    final bloc = context.read<StimulationBloc>();
    await bloc.deviceStreamsMap[widget.deviceId]?.disposeAll();
  }

  void _handleAppBarTrailingOnTap() {
    onPressedHelp();
    (this as StimulationControlTutorialMixin).createAndShowTutorial(isHideSkip: false);
  }

  @override
  void dispose() {
    final tutorialCoachMark = (this as StimulationControlTutorialMixin).tutorialCoachMark;
    // If tutorial is showing, finish it (if it's not showing, it will be null.
    if (tutorialCoachMark != null) {
      tutorialCoachMark.finish();
    }

    super.dispose();
  }
}

// For time of usage.
/*final timeOfUsageEnums = context.read<TimeOfUsageBloc>().state.timeOfUsageEnums;
    final userMaxRemainingSecond = context.read<TimeOfUsageBloc>().state.userMaxRemainingSecond;
    final userMaxRemainingMinute =
        ((userMaxRemainingSecond ?? 0) ~/ 60) > 0 ? ((userMaxRemainingSecond ?? 0) ~/ 60) : 0;

    if (timeOfUsageEnums != null) {
      AppDialog(
        cancelButtonOnTap: () => timeOfUsageEnums.timeOfUsageCancelButtonOnTap(context)(),
        cancelButtonText: timeOfUsageEnums.timeOfUsageCancelButtonText,
        context: context,
        description: timeOfUsageEnums.timeOfUsageToDialogDescription(
          value: (userMaxRemainingMinute > 0) ? (userMaxRemainingMinute).toString() : "0",
        ),
        icon: timeOfUsageEnums.timeOfUsageAlertIcon,
        isDynamicButtonResize: false,
        okButtonOnTap: () => timeOfUsageEnums.timeOfUsageOkButtonOnTap(
          context,
          widget.subuser,
          value: userMaxRemainingMinute == 0
              ? timeOfUsageEnums.timeOfUsageToOkButtonUpdateDurationParameters()
              : userMaxRemainingMinute,
        )(),
      ).show;

      bool isRedAlert = (timeOfUsageEnums == TimeOfUsageEnums.redAlert);
      if (isRedAlert) return;
    }
  */
