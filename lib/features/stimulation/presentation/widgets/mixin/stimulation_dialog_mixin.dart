part of '../stimulation_control_widget.dart';

mixin StimulationDialogMixin on State<StimulationControlWidget> {
  bool isShowingDialog = false;

  final bundleId =
      GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

  void showParameterGetFailedDialog() {
    if (!context.mounted) return;

    isShowingDialog = true;
    unawaited(AppDialog(
      barrierDismissible: false,
      context: context,
      description: StimulationControlConstants.parameterNotFoundDescription,
      isDisableCancelButton: true,
      isDisableCloseButton: true,
      okButtonOnTap: () {
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
      },
      title: StimulationControlConstants.parameterNotFoundTitle,
    ).show);
  }

  void handleOnTapHeartRateDialog() {
    if (!context.mounted) return;
    if (widget.sessionTypeParams.sessionType != SessionType.aiDriven) return;

    final selectSessionTypePageConfig =
        GetIt.instance.get<RemoteConfigService>().selectYourSessionTypePageConfig;

    final SelectYourSessionTypePageConfig(
      :aiDrivenHrvDialogTitle,
      :aiDrivenHrvSkipBtnDesc,
      :laterBtn,
      :startBtn,
    ) = selectSessionTypePageConfig;

    unawaited(AppDialog(
      cancelButtonText: laterBtn,
      context: context,
      description: aiDrivenHrvSkipBtnDesc,
      isCancelBtnNavigatePop: true,
      okButtonOnTap: _handleOnTapHeartRate,
      okButtonText: startBtn,
      title: aiDrivenHrvDialogTitle,
    ).show);
  }

  void _handleOnTapHeartRate() async {
    final subuser = (context.read<AuthBloc>().currentUser?.defaultSubuser);
    final response = await AppRoute.pushNewScreen(
      context,
      StartCameraView(isAiDriven: true, isCanPop: false, subuser: subuser),
      specificIndex: 1,
      withNavBar: false,
    );

    if (response != null) {
      unawaited(showModalBottomSheet(
        builder: (ctx) => BlocProvider<HrvBloc>.value(
          value: GetIt.instance.get(),
          child: AiDrivenResultWidget(isNavigateBleSearch: false),
        ),
        context: context,
        isScrollControlled: true,
        useRootNavigator: true,
        useSafeArea: true,
      ));
    }
  }

  void _handleStimulationFinish(BuildContext ctx) {
    handleOnTapHeartRateDialog();

    final dialogService = GetIt.instance.get<DialogService>();
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final deviceControlPageConfig = remoteConfigService.deviceControlPageConfig;

    // Extract dialog configuration properties to avoid repeated access.
    // ignore_for_file: prefer-class-destructuring
    final confirmButtonText = deviceControlPageConfig.stimulationCompleteAlertButton;
    final message = deviceControlPageConfig.stimulationCompleteAlertMessage;
    final title = deviceControlPageConfig.stimulationCompleteAlertTitle;

    unawaited(dialogService.showDialog(
      confirmButtonText: confirmButtonText,
      message: message,
      navigatorKey: AppNavigator.navigatorKey,
      onConfirm: () => _handleDialogConfirm(ctx),
      title: title,
    ));
  }

  void _handleDialogConfirm(BuildContext ctx) {
    if (!ctx.mounted) return;

    _updateMultipleDeviceStatusOnFinish();
    _navigateToSubuserDetailIfIndividual(ctx);
  }

  void _navigateToSubuserDetailIfIndividual(BuildContext ctx) {
    final isIndividual = ctx.read<AuthBloc>().currentUser?.userType == AppStrings.individualType;
    if (!isIndividual) return;

    unawaited(AppRoute.pushNewScreen(
      ctx,
      SubuserDetailView(
        isHistory: true,
        subuser: widget.subuser,
        userType: StimulationControlConstants.individualsUserType,
      ),
    ));
  }

  void _updateMultipleDeviceStatusOnFinish() {
    if (!FlavorConstants.isHealthPro(bundleId)) return;

    final bloc = GetIt.instance.get<MultipleDeviceBloc>();
    final currentEntity =
        bloc.state.devices?.firstWhereOrNull((element) => element.bleId == widget.deviceId);

    if (currentEntity == null) return;
    if (currentEntity.deviceStatus == DeviceStatusEnum.completed) return;

    bloc.add(ChangeConnectedDeviceStatus(
      device: currentEntity,
      status: DeviceStatusEnum.completed,
    ));
  }
}
