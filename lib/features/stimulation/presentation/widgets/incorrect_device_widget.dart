import 'package:flutter/cupertino.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../core/app_config/app_gaps.dart';
import '../../../../core/app_config/app_text_styles.dart';
import '../../../../core/enum/assets_enums_lottie.dart';

class IncorrectDeviceWidget extends StatelessWidget {
  const IncorrectDeviceWidget({super.key});

  @override
  Widget build(BuildContext context) {
    const incorrectDevice = "Incorrect Product";
    final lottieHeight = 60.0;

    return Padding(
      padding: context.paddingMedium,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        spacing: 0,
        children: [
          Center(
            child: AssetsEnumsLottie.error.toLottie(height: lottieHeight),
          ),
          AppGaps.instance.gapHS16,
          Center(
            child: Text(
              incorrectDevice,
              style: AppTextStyles.appDialogTitleStyle,
            ),
          ),
        ],
      ),
    );
  }
}
