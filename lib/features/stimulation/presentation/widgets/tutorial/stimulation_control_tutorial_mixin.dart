// ignore_for_file: use_build_context_synchronously, avoid-long-functions, avoid-unrelated-type-assertions, avoid-unrelated-type-casts, avoid-type-casts

part of '../stimulation_control_widget.dart';

mixin StimulationControlTutorialMixin on State<StimulationControlWidget> {
  GlobalKey leftEarIncrementButtonKey = GlobalKey();
  GlobalKey leftEarMinusButtonKey = GlobalKey();

  GlobalKey rightEarIncrementButtonKey = GlobalKey();
  GlobalKey rightEarMinusButtonKey = GlobalKey();

  GlobalKey startButtonKey = GlobalKey();
  GlobalKey headPhoneControlKey = GlobalKey();
  GlobalKey remainingKey = GlobalKey();
  GlobalKey pauseButtonKey = GlobalKey();
  GlobalKey adjustParametersButtonKey = GlobalKey();

  TutorialCoachMark? tutorialCoachMark;
  double paddingFocus = 20;
  SharedPreferences? sharedPreferences;
  bool? hideSkip;

  void get createTutorial {
    tutorialCoachMark = TutorialCoachMark(
      alignSkip: Alignment.topRight,
      hideSkip: !(hideSkip ?? false),
      onClickOverlay: (target) {
        if (this is StimulationControlTutorialMethodsMixin) {
          return (this as StimulationControlTutorialMethodsMixin).onClickOverlay();
        }
      },
      onClickTarget: (target) {
        if (this is StimulationControlTutorialMethodsMixin) {
          return (this as StimulationControlTutorialMethodsMixin).onClickTarget(target.identify);
        }
      },
      onFinish: () => tutorialFinish(),
      onSkip: () {
        tutorialFinish();

        return true;
      },
      paddingFocus: paddingFocus,
      skipWidget: StimulationControlTutorialSkip(
        onPressed: () => tutorialCoachMark?.finish(),
      ),
      targets: tutorialTargetFocuses,
    );
  }

  void get showTutorial {
    showWalkThrough;
    /* Future.delayed(context.veryLowDuration)
        .then((value) => tutorialCoachMark?.show(context: context));*/
  }

  void createAndShowTutorial({bool? isHideSkip}) async {
    if ((isHideSkip ?? true)) {
      final sharedPreferencesInstance = await SharedPreferences.getInstance();
      bool isFirstTutorial =
          sharedPreferencesInstance.getBool(StimulationControlTargetIdentifyConstants.isFirstTutorial) ??
              false;
      hideSkip = isFirstTutorial;
      if (isFirstTutorial) return;
      createTutorial;
      showWalkThrough;
    } else {
      hideSkip = !(isHideSkip ?? false);
      createTutorial;
      showWalkThrough;
    }
  }

  List<TargetFocus> get tutorialTargetFocuses {
    final stimulationControlPageTutorialConfig =
        GetIt.instance.get<RemoteConfigService>().stimulationControlPageTutorialConfig;

    return [
      TargetFocus(
        contents: [
          TargetContent(
            child: const StimulationControlTutorialStartButton(),
          ),
        ],
        enableOverlayTab: true,
        identify: StimulationControlTargetIdentifyConstants.startButton,
        keyTarget: startButtonKey,
        paddingFocus: 0,
      ),
      TargetFocus(
        contents: [
          TargetContent(
            align: ContentAlign.top,
            child: StimulationControlTutorialLeftEar(
              onPressed: () => tutorialCoachMark?.goTo(2),
              reduceButtonOnPressed: () {
                if (this is StimulationControlMixin) {
                  (this as StimulationControlMixin).onPressedLeftMinus();
                }
              },
              text: stimulationControlPageTutorialConfig.feltItNoPain,
            ),
          ),
        ],
        enableOverlayTab: true,
        identify: StimulationControlTargetIdentifyConstants.leftEarIncrementButton,
        keyTarget: leftEarIncrementButtonKey,
      ),
      TargetFocus(
        contents: [
          TargetContent(
            align: ContentAlign.top,
            child: StimulationControlTutorialRightEar(
              onPressed: () => tutorialCoachMark?.goTo(4),
              reduceButtonOnPressed: () {
                if (this is StimulationControlMixin) {
                  (this as StimulationControlMixin).onPressedRightMinus();
                }
              },
              text: stimulationControlPageTutorialConfig.feltItNoPain,
            ),
          ),
        ],
        enableOverlayTab: true,
        identify: StimulationControlTargetIdentifyConstants.rightEarIncrementButton,
        keyTarget: rightEarIncrementButtonKey,
      ),
      TargetFocus(
        contents: [
          TargetContent(
            align: ContentAlign.top,
            padding: EdgeInsets.zero,
            child: StimulationControlTutorialHeadPhoneControl(
              onPressed: () => tutorialCoachMark?.goTo(8),
            ),
          ),
        ],
        enableOverlayTab: true,
        identify: StimulationControlTargetIdentifyConstants.headPhoneControlKey,
        keyTarget: headPhoneControlKey,
        shape: ShapeLightFocus.RRect,
      ),
      TargetFocus(
        contents: [
          TargetContent(
            padding: EdgeInsets.zero,
            child: StimulationControlTutorialRemaining(onPressed: () => tutorialCoachMark?.goTo(5)),
          ),
        ],
        enableOverlayTab: true,
        identify: StimulationControlTargetIdentifyConstants.remainingKey,
        keyTarget: remainingKey,
        shape: ShapeLightFocus.RRect,
      ),
      TargetFocus(
        contents: [
          TargetContent(
            padding: EdgeInsets.zero,
            child: StimulationControlTutorialPauseButton(
              onPressed: () => tutorialCoachMark?.goTo(6),
            ),
          ),
        ],
        enableOverlayTab: true,
        identify: StimulationControlTargetIdentifyConstants.pauseButtonKey,
        keyTarget: pauseButtonKey,
        paddingFocus: 0,
      ),
      TargetFocus(
        contents: [
          TargetContent(
            padding: EdgeInsets.zero,
            child: StimulationControlTutorialAdjustParameters(
              onPressed: () => tutorialCoachMark?.goTo(7),
            ),
          ),
        ],
        enableOverlayTab: true,
        identify: StimulationControlTargetIdentifyConstants.adjustParametersButtonKey,
        keyTarget: adjustParametersButtonKey,
        paddingFocus: 0,
      ),
    ];
  }

  void tutorialFinish() async {
    final sharedPreferencesInstance = await SharedPreferences.getInstance();

    await sharedPreferencesInstance.setBool(
      StimulationControlTargetIdentifyConstants.isFirstTutorial,
      true,
    );
  }

  void get showWalkThrough {
    showModalBottomSheet(
      builder: (context) => StimulationControlWalktroughTutorial(isHideSkip: hideSkip),
      context: context,
      isScrollControlled: true,
    ).whenComplete(() {
      Future.delayed(context.veryLowDuration)
          .then((value) => tutorialCoachMark?.show(context: context));
    });
  }
}
