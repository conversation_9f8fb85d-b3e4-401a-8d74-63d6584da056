// ignore_for_file: avoid-similar-names, avoid-unrelated-type-assertions, avoid-unrelated-type-casts

part of '../stimulation_control_widget.dart';

mixin StimulationControlTutorialMethodsMixin on State<StimulationControlWidget> {
  final _blockerCompleter = Completer<void>();

  FutureOr<void> onClickTarget(String targetIdentify) {
    StimulationControlMixin? stimulationControlMixin;
    StimulationControlTutorialMixin? stimulationControlTutorialMixin;
    if (this is StimulationControlMixin) {
      stimulationControlMixin = (this as StimulationControlMixin);
    }
    if (this is StimulationControlTutorialMixin) {
      stimulationControlTutorialMixin = (this as StimulationControlTutorialMixin);
    }
    switch (targetIdentify) {
      case StimulationControlTargetIdentifyConstants.leftEarIncrementButton:
        stimulationControlMixin?.onPressedLeftPlus();

        return _blockerCompleter.future;

      case StimulationControlTargetIdentifyConstants.startButton:
        stimulationControlMixin?.handleOnPressedStart();
        stimulationControlTutorialMixin?.tutorialCoachMark?.goTo(1);
        break;

      case StimulationControlTargetIdentifyConstants.headPhoneControlKey:
        stimulationControlTutorialMixin?.tutorialCoachMark?.goTo(8);
        break;

      case StimulationControlTargetIdentifyConstants.remainingKey:
        stimulationControlTutorialMixin?.tutorialCoachMark?.goTo(5);
        break;

      case StimulationControlTargetIdentifyConstants.pauseButtonKey:
        stimulationControlTutorialMixin?.tutorialCoachMark?.goTo(6);
        break;

      case StimulationControlTargetIdentifyConstants.adjustParametersButtonKey:
        stimulationControlTutorialMixin?.tutorialCoachMark?.finish();

      case StimulationControlTargetIdentifyConstants.leftEarMinusButton:
        stimulationControlMixin?.onPressedLeftMinus();

        return _blockerCompleter.future;

      case StimulationControlTargetIdentifyConstants.rightEarIncrementButton:
        stimulationControlMixin?.onPressedRightPlus();

        return _blockerCompleter.future;

      case StimulationControlTargetIdentifyConstants.rightEarMinusButton:
        stimulationControlMixin?.onPressedRightMinus();

        return _blockerCompleter.future;
    }
  }

  Future<void> onClickOverlay() {
    return _blockerCompleter.future;
  }
}
