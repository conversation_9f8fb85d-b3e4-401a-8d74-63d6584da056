import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/features/time_of_usage/presentation/widgets/app_dialog.dart';

import '../../../../../core/enum/assets_enums.dart';
import '../../../../../core/remote_config/remote_config_service.dart';
import '../../bloc/stimulation_bloc.dart';

class StimulationControlAppbarTrailing extends StatelessWidget {
  const StimulationControlAppbarTrailing({super.key, required this.onTap});

  final Function(DeviceStatusReadSuccess onTap) onTap;

  @override
  Widget build(BuildContext context) {
    final commonPageConfig = GetIt.instance.get<RemoteConfigService>().commonPageConfig;

    return BlocBuilder<StimulationBloc, StimulationState>(
      buildWhen: (previous, current) {
        return current is DeviceStatusReadSuccess && previous != current;
      },
      builder: (context, state) {
        bool? isStimStarted;
        if (state is DeviceStatusReadSuccess) {
          isStimStarted = state.deviceStatus != 'pause' && state.deviceStatus != 'stop';
        }

        return state is DeviceStatusReadSuccess
            ? GestureDetector(
                onTap: () {
                  if (isStimStarted!) {
                    unawaited(AppDialog(
                      context: context,
                      description: commonPageConfig.stimulationControlAppbarTrailingDesc,
                      okButtonOnTap: () {
                        onTap(state);
                      },
                      okButtonText: commonPageConfig.stopStimulation,
                      useRootNavigator: false,
                    ).show);
                  } else {
                    onTap(state);
                  }
                },
                child: AssetsEnums.icHelp.toSvg(),
              )
            : const SizedBox();
      },
    );
  }
}
