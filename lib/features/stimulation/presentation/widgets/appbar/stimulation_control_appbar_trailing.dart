import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/features/time_of_usage/presentation/widgets/app_dialog.dart';

import '../../../../../core/ble/stimulation_control_constants.dart';
import '../../../../../core/enum/assets_enums.dart';
import '../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../core/remote_config/common_page_config.dart';
import '../../bloc/stimulation_bloc.dart';

/// Callback type for handling device status events.
typedef OnDeviceStatusCallback = void Function();

class StimulationControlAppbarTrailing extends StatelessWidget {
  const StimulationControlAppbarTrailing({super.key, required this.onTap});

  final OnDeviceStatusCallback onTap;

  /// Handles tap events on the trailing widget.
  void _handleTap(
    CommonPageConfig commonPageConfig,
    BuildContext context,
    DeviceStatusReadSuccess state,
  ) {
    final isStimStarted = state.deviceStatus != StimulationControlConstants.deviceStatusPause &&
        state.deviceStatus != StimulationControlConstants.deviceStatusStop;

    if (isStimStarted) {
      _showStopDialog(commonPageConfig, context);
    } else {
      onTap();
    }
  }

  /// Shows the stop stimulation dialog.
  void _showStopDialog(
    CommonPageConfig commonPageConfig,
    BuildContext context,
  ) {
    unawaited(
      AppDialog(
        context: context,
        description: commonPageConfig.stimulationControlAppbarTrailingDesc,
        okButtonOnTap: () => _handleStopConfirmation(),
        okButtonText: commonPageConfig.stopStimulation,
        useRootNavigator: false,
      ).show,
    );
  }

  /// Handles the stop confirmation callback.
  void _handleStopConfirmation() {
    onTap();
  }

  @override
  Widget build(BuildContext context) {
    final commonPageConfig = GetIt.instance.get<RemoteConfigService>().commonPageConfig;

    return BlocBuilder<StimulationBloc, StimulationState>(
      buildWhen: (previous, current) => current is DeviceStatusReadSuccess && previous != current,
      builder: (ctx, state) {
        return switch (state) {
          DeviceStatusReadSuccess() => GestureDetector(
              onTap: () => _handleTap(commonPageConfig, ctx, state),
              child: AssetsEnums.icHelp.toSvg(),
            ),
          _ => const SizedBox(),
        };
      },
    );
  }
}
