import 'package:flutter/material.dart';

import '../stimulation_control_exports.dart';

class StimulationControlAppBar extends StatelessWidget implements PreferredSizeWidget {
  const StimulationControlAppBar({super.key, required this.onTrailingTap});

  final VoidCallback onTrailingTap;

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final deviceControlPageConfig = remoteConfigService.deviceControlPageConfig;

    return CupertinoNavigationBar(
      heroTag: UniqueKey(),
      middle: Text(deviceControlPageConfig.advanceDeviceControlPageNavbarTxt),
      trailing: StimulationControlAppbarTrailing(onTap: onTrailingTap),
      transitionBetweenRoutes: false,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kMinInteractiveDimension);
}
