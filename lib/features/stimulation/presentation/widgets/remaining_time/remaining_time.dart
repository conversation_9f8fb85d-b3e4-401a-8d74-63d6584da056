import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../core/app_config/app_colors.dart';
import '../../../../../core/app_config/app_text_styles.dart';
import '../../../../../core/remote_config/device_control_page_config.dart';
import '../../bloc/stimulation_bloc.dart';

class RemainingTime extends StatelessWidget {
  const RemainingTime({
    required this.deviceControlPageConfig,
    required this.deviceId,
    required this.hasCurrentIntensityPermission,
    super.key,
    required this.state,
  });

  // Constants for text strings.
  static const _loadingText = 'Loading...';
  static const _leftCurrentPrefix = 'left current: ';
  static const _rightCurrentPrefix = ' mA - right current: ';
  static const _currentSuffix = ' mA';
  static const _spacePrefix = ' ';
  static const _timeFormatSeparator = ':';

  // Constants for numeric values.
  static const _currentDivisor = 1000.0;
  static const _currentDecimalPlaces = 2;
  static const _secondsPerMinute = 60;
  static const _fontSizeSmall = 12.0;
  static const _containerHeight = 52.0;

  final DeviceControlPageConfig deviceControlPageConfig;
  final bool hasCurrentIntensityPermission;
  final StimulationTimeAndIntensityReadSuccess state;
  final String deviceId;

  /// Builds the remaining time text with proper formatting.
  String get _remainingTimeText => _spacePrefix + deviceControlPageConfig.remainingTimeTxt;

  /// Builds the time display text based on device state.
  String get _timeDisplayText {
    if (state.deviceId == deviceId) {
      final minutes = state.timeLeft ~/ _secondsPerMinute;
      final seconds = state.timeLeft % _secondsPerMinute;

      return _spacePrefix + minutes.toString() + _timeFormatSeparator + seconds.toString();
    }

    return _loadingText;
  }

  /// Builds the current intensity display text.
  String get _currentIntensityText {
    final leftCurrent =
        (double.parse(state.currentLeft) / _currentDivisor).toStringAsFixed(_currentDecimalPlaces);
    final rightCurrent =
        (double.parse(state.currentRight) / _currentDivisor).toStringAsFixed(_currentDecimalPlaces);

    return _leftCurrentPrefix + leftCurrent + _rightCurrentPrefix + rightCurrent + _currentSuffix;
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          SizedBox(
            height: _containerHeight.sp,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  _remainingTimeText,
                  style: AppTextStyles.stimulationControlRemainingText
                      .copyWith(fontSize: _fontSizeSmall.sp, fontWeight: FontWeight.w400),
                ),
                Text(
                  _timeDisplayText,
                  style: AppTextStyles.stimulationControlRemainingText,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          // Current intensity permission container will be shown if permission is granted.
          if (hasCurrentIntensityPermission)
            Align(
              alignment: Alignment.center,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.fromBorderSide(
                    BorderSide(
                      color: AppColors.stimulationControlTutorialSkipButtonBackgroundColor,
                    ),
                  ),
                  borderRadius: AppBorderRadius.circularSize8Radius(),
                  color: AppColors.pureWhite,
                ),
                padding: context.paddingLow,
                child: Text.rich(
                  TextSpan(children: [TextSpan(text: _currentIntensityText)]),
                  style: AppTextStyles.historyCalendarWeek.copyWith(fontWeight: FontWeight.w500),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
