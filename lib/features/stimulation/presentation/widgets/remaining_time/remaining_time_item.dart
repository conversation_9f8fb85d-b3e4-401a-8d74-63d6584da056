import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/ble/stimulation_control_constants.dart';
import '../../../../../core/custom_widgets/minimal_loader.dart';
import '../../../../../core/remote_config/device_control_page_config.dart';
import '../../../../app_permission/presentation/bloc/app_permission_bloc.dart';
import '../../../../time_of_usage/presentation/bloc/time_of_usage_bloc.dart';
import '../../bloc/stimulation_bloc.dart';
import '../device_streams.dart';
import 'remaining_time.dart';

class RemainingTimeItem extends StatelessWidget {
  const RemainingTimeItem({
    required this.deviceControlPageConfig,
    required this.deviceId,
    super.key,
    required this.remainingKey,
  });

  final String deviceId;

  final DeviceControlPageConfig deviceControlPageConfig;
  final GlobalKey remainingKey;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StimulationBloc, StimulationState>(
      buildWhen: (previous, current) =>
          current is StimulationTimeAndIntensityReadSuccess && current.deviceId == deviceId,
      builder: (ctx, state) {
        if (state is StimulationLoading) {
          return const MinimalLoader();
        }

        if (state is! StimulationTimeAndIntensityReadSuccess) {
          return const MinimalLoader();
        }

        if (state.deviceId != deviceId) {
          return const MinimalLoader();
        }
        final appPermissionState = ctx.read<AppPermissionBloc>().state;

        final hasCurrentIntensityPermission = appPermissionState is AppPermissionSuccess &&
            appPermissionState.appPermissions.any(
              (permission) =>
                  permission.name == StimulationControlConstants.currentIntensityPermission,
            );

        // Update time of usage.
        ctx.read<TimeOfUsageBloc>().add(
              GetTimeOfUsageEvent(scheduledDuration: state.timeLeft),
            );

        return DeviceStreams(state.deviceId).stimulationTimeAndIntensityStream == null
            ? const MinimalLoader()
            : RemainingTime(
                deviceControlPageConfig: deviceControlPageConfig,
                deviceId: deviceId,
                hasCurrentIntensityPermission: hasCurrentIntensityPermission,
                key: remainingKey,
                state: state,
              );
      },
    );
  }
}
