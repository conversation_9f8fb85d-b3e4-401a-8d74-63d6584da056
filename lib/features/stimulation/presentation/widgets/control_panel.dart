import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';

class ControlPanel extends StatelessWidget {
  const ControlPanel({
    this.adjustParametersButtonKey,
    this.isDisableAdjustParameters = false,
    required this.isStimPaused,
    required this.isStimStarted,
    super.key,
    required this.onPressAdjustParameters,
    required this.onPressStart,
    required this.onPressStop,
    this.pauseButtonKey,
    this.startButtonKey,
  });
  final bool isStimStarted;
  final bool isStimPaused;
  final VoidCallback onPressStart;
  final VoidCallback onPressStop;
  final VoidCallback onPressAdjustParameters;
  final GlobalKey? startButtonKey;
  final GlobalKey? pauseButtonKey;
  final GlobalKey? adjustParametersButtonKey;
  final bool isDisableAdjustParameters;

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final deviceControlPageConf = remoteConfigService.deviceControlPageConfig;

    const circleDiameter = 56.0;

    bool isStopAndPausedColor = isStimStarted && !isStimPaused;
    Color stopAndPauseColor = isStimStarted
        ? (isStimPaused
            ? AppColors.stimulationControlPauseColor
            : AppColors.controlPanelPauseButtonColor)
        : isStimPaused
            ? AppColors.neoStopButtoColor
            : AppColors.stimulationControlPauseColor;

    return Padding(
      padding: context.paddingLargehorizontal.copyWith(t),
      child: Row(
        mainAxisAlignment: isDisableAdjustParameters
            ? MainAxisAlignment.spaceAround
            : MainAxisAlignment.spaceBetween,
        children: [
          Column(
            key: pauseButtonKey,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: isStimStarted || isStimPaused ? onPressStop : null,
                child: SizedBox.square(
                  dimension: circleDiameter,
                  child: Card(
                    color: isStopAndPausedColor
                        ? stopAndPauseColor
                        : (isStimPaused ? AppColors.neoStopButtoColor : Colors.transparent),
                    elevation: isStopAndPausedColor ? 12 : 0, // Bu kodu bi ben bi Allah biliyor.
                    margin: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: AppBorderRadius.circularSize50Radius(),
                      side: BorderSide(color: stopAndPauseColor, width: 3),
                    ),
                    child: Icon(
                      isStimPaused ? Icons.stop : Icons.pause,
                      color: isStopAndPausedColor
                          ? AppColors.controlPanelInContainerBackgroundColor
                          : (isStimPaused
                              ? AppColors.controlPanelInContainerBackgroundColor
                              : AppColors.stimulationControlPauseColor),
                      size: 28,
                    ),
                  ),
                ),
              ),
              AppGaps.gapH4,
              Text(
                isStimPaused
                    ? "${deviceControlPageConf.stopTxt}${isDisableAdjustParameters ? '' : '\n'}"
                    : "${deviceControlPageConf.pauseTxt}${isDisableAdjustParameters ? '' : '\n'}",
                style: AppTextStyles.stimulationControlPanelPauseText.copyWith(
                  color: isStimPaused ? AppColors.neoStopButtoColor : null,
                  fontWeight: isStimPaused ? FontWeight.bold : null,
                ),
              ),
            ],
          ),
          Column(
            key: startButtonKey,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 0,
            children: [
              GestureDetector(
                onTap: isStimStarted && !isStimPaused ? null : onPressStart,
                child: SizedBox.square(
                  dimension: circleDiameter,
                  child: Card(
                    color: isStimStarted && !isStimPaused
                        ? Colors.transparent
                        : AppColors.controlPanelStartButton,
                    // Refactoring.
                    elevation: isStimStarted && !isStimPaused ? 0 : 12,
                    margin: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: AppBorderRadius.circularSize50Radius(),
                      side: BorderSide(
                        color: AppColors.stimulationControlPauseColor,
                        width: isStimStarted && !isStimPaused ? 3 : 0,
                      ),
                    ),
                    child: Icon(
                      Icons.play_arrow,
                      color: isStimStarted && !isStimPaused
                          ? AppColors.stimulationControlPauseColor
                          : Colors.white,
                      size: 28,
                    ),
                  ),
                ),
              ),
              AppGaps.gapH4,
              Text(
                isStimPaused
                    ? "${deviceControlPageConf.continueTxt}${isDisableAdjustParameters ? '' : '\n'}"
                    : "${deviceControlPageConf.startTxt}${isDisableAdjustParameters ? '' : '\n'}",
                style: AppTextStyles.stimulationControlPanelStartText.copyWith(
                  color: isStimStarted && !isStimPaused
                      ? AppColors.stimulationControlPauseColor
                      : null,
                ),
              ),
            ],
          ),
          if (!isDisableAdjustParameters)
            Column(
              key: adjustParametersButtonKey,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: 0,
              children: [
                GestureDetector(
                  onTap: onPressAdjustParameters,
                  child: SizedBox.square(
                    dimension: circleDiameter,
                    child: Card(
                      color: AppColors.controlPanelInContainerBackgroundColor,
                      margin: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: AppBorderRadius.circularSize50Radius(),
                      ),
                      child: Padding(
                        padding: context.paddingMedium,
                        child: AssetsEnums.adjustParameters.toPng(),
                      ),
                    ),
                  ),
                ),
                AppGaps.gapH4,
                Text(
                  remoteConfigService.adjustParametersPageConfig.adjustParametersBtn,
                  style: AppTextStyles.stimulationControlPanelAdjustParameterText,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
        ],
      ),
    );
  }
}
