import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../bloc/stimulation_bloc.dart';
import 'battery_indicator_item.dart';

class BatterIndicatorWidget extends StatelessWidget {
  const BatterIndicatorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StimulationBloc, StimulationState>(
      buildWhen: (previous, current) => current is BatteryLevelReadSuccess,
      builder: (ctxStim, state) {
        return state is BatteryLevelReadSuccess
            ? BatteryIndicatorItem(batteryPercentage: (state.batteryLevel / 100).toDouble())
            : const SizedBox();
      },
    );
  }
}
