import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../bloc/stimulation_bloc.dart';
import 'battery_indicator_item.dart';

class BatterIndicatorWidget extends StatelessWidget {
  const BatterIndicatorWidget({super.key});

  // Constants for battery level conversion.
  static const _batteryLevelDivisor = 100.0;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StimulationBloc, StimulationState>(
      buildWhen: (previous, current) => current is BatteryLevelReadSuccess,
      builder: (ctxStim, state) {
        return switch (state) {
          BatteryLevelReadSuccess() => BatteryIndicatorItem(
              batteryPercentage: (state.batteryLevel / _batteryLevelDivisor),
            ),
          _ => const SizedBox(),
        };
      },
    );
  }
}
