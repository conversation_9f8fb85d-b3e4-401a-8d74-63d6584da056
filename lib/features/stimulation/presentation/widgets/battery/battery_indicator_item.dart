import 'package:flutter/material.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/battery_indicator.dart';

import '../../../../../core/app_config/app_colors.dart';

class BatteryIndicatorItem extends StatelessWidget {
  const BatteryIndicatorItem({required this.batteryPercentage, super.key});

  // Constants for battery display.
  static const _percentageSymbol = '%';
  static const _percentageMultiplier = 100.0;
  static const _decimalPlaces = 0;
  static const _trackOpacity = 0.3;

  final double batteryPercentage;

  /// Builds the battery percentage text with proper formatting.
  String get _batteryPercentageText {
    final percentage = (batteryPercentage * _percentageMultiplier).toStringAsFixed(_decimalPlaces);

    return percentage + _percentageSymbol;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 0,
      children: [
        BatteryIndicator(
          barColor: AppColors.stimulationControlTitle,
          trackColor: AppColors.stimulationControlTitle.withValues(alpha: _trackOpacity),
          value: batteryPercentage,
        ),
        AppGaps.instance.gapHS4,
        Text(
          _batteryPercentageText,
          style: AppTextStyles.stimulationControlBattery,
        ),
      ],
    );
  }
}
