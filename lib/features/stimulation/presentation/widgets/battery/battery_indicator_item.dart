import 'package:flutter/material.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/battery_indicator.dart';

import '../../../../../core/app_config/app_colors.dart';

class BatteryIndicatorItem extends StatelessWidget {
  const BatteryIndicatorItem({required this.batteryPercentage, super.key});
  final double batteryPercentage;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        BatteryIndicator(
          barColor: AppColors.stimulationControlTitle,
          trackColor: AppColors.stimulationControlTitle,
          value: batteryPercentage,
        ),
        AppGaps.instance.gapHS4,
        Text(
          "${(batteryPercentage * 100).toStringAsFixed(0)}%",
          style: AppTextStyles.stimulationControlBattery,
        ),
      ],
    );
  }
}
