part of 'stimulation_control_widget.dart';

mixin StimulationControlMixin on State<StimulationControlWidget> {
  bool headphoneLSelected = true;
  double leftProgress = 0;
  double rightProgress = 0;
  bool headLConnected = true;
  bool headRConnected = true;
  bool stimOffTime = false;

  bool isShowingDialog = false;

  // Kullanıcının total durationu almak için.
  int? timeLeft;

  void onPressedStart() {
    /*final timeOfUsageEnums = context.read<TimeOfUsageBloc>().state.timeOfUsageEnums;
    // Kullanıcının kalan kullanabilecegi max secondu.
    final userMaxRemainingSecond = context.read<TimeOfUsageBloc>().state.userMaxRemainingSecond;
    final userMaxRemainingMinute =
        ((userMaxRemainingSecond ?? 0) ~/ 60) > 0 ? ((userMaxRemainingSecond ?? 0) ~/ 60) : 0;

    if (timeOfUsageEnums != null) {
      AppDialog(
        cancelButtonOnTap: () => timeOfUsageEnums.timeOfUsageCancelButtonOnTap(context)(),
        cancelButtonText: timeOfUsageEnums.timeOfUsageCancelButtonText,
        context: context,
        description: timeOfUsageEnums.timeOfUsageToDialogDescription(
          value: (userMaxRemainingMinute > 0) ? (userMaxRemainingMinute).toString() : "0",
        ),
        icon: timeOfUsageEnums.timeOfUsageAlertIcon,
        isDynamicButtonResize: false,
        okButtonOnTap: () => timeOfUsageEnums.timeOfUsageOkButtonOnTap(
          context,
          widget.subuser,
          value: userMaxRemainingMinute == 0
              ? timeOfUsageEnums.timeOfUsageToOkButtonUpdateDurationParameters()
              : userMaxRemainingMinute,
        )(),
      ).show;

      bool isRedAlert = (timeOfUsageEnums == TimeOfUsageEnums.redAlert);
      if (isRedAlert) return;
    }
  */

    if (FlavorConstants.isHealthPro(bundleId)) {
      final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();

      final connectedDevices = multipleDeviceBloc.state.devices;

      for (MultipleDeviceEntity element in connectedDevices ?? []) {
        if (element.bleId == widget.deviceId) {
          multipleDeviceBloc.add(
            ChangeConnectedDeviceStatus(
              device: element,
              status: DeviceStatusEnum.ongoing,
              subuser: widget.subuser,
            ),
          );
        }
      }
    }
    context.read<StimulationBloc>().add(
          StimulationStartEvent(
            bleDeviceInteractor: widget.deviceInteractor,
            deviceId: widget.deviceId,
          ),
        );

    context.read<StimulationBloc>().add(
          StimulationCreateEvent(
            commandType: 'start',
            sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
            subuserUid: widget.subuser.uid!,
          ),
        );
  }

  final bundleId =
      GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

  void onPressedStop(DeviceStatusReadSuccess state) {
    if (FlavorConstants.isHealthPro(bundleId)) {
      final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();

      final connectedDevices = multipleDeviceBloc.state.devices;

      for (MultipleDeviceEntity element in connectedDevices ?? []) {
        if (element.bleId == widget.deviceId) {
          multipleDeviceBloc.add(ChangeConnectedDeviceStatus(
            device: element,
            status: state.deviceStatus == "pause"
                ? DeviceStatusEnum.connected
                : DeviceStatusEnum.paused,
            subuser: widget.subuser,
          ));
        }
      }
    }
    // Stop stimulation.
    if (state.deviceStatus == 'pause') {
      context.read<StimulationBloc>().add(
            StimulationCreateEvent(
              commandType: 'stop',
              sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
              subuserUid: widget.subuser.uid!,
            ),
          );
      context.read<StimulationBloc>().add(
            StimulationStopEvent(
              bleDeviceInteractor: widget.deviceInteractor,
              deviceId: widget.deviceId,
              subuserUid: widget.subuser.uid!,
            ),
          );

      setState(() {
        leftProgress = 0.0;
        rightProgress = 0.0;
      });

      _handleOnTapHeartRateDialog();
    } else {
      context.read<StimulationBloc>().add(
            StimulationPauseEvent(
              bleDeviceInteractor: widget.deviceInteractor,
              deviceId: widget.deviceId,
            ),
          );
      context.read<StimulationBloc>().add(
            StimulationCreateEvent(
              commandType: 'pause',
              sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
              subuserUid: widget.subuser.uid!,
            ),
          );
    }
  }

  void onPressedHelp() {
    context.read<StimulationBloc>().add(
          StimulationCreateEvent(
            commandType: 'stop',
            sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
            subuserUid: widget.subuser.uid!,
          ),
        );
    context.read<StimulationBloc>().add(
          StimulationStopEvent(
            bleDeviceInteractor: widget.deviceInteractor,
            deviceId: widget.deviceId,
            subuserUid: widget.subuser.uid!,
          ),
        );

    setState(() {
      leftProgress = 0.0;
      rightProgress = 0.0;
    });
  }

  void onPressedLeftPlus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }

    setState(() {
      if (leftProgress < 150) {
        leftProgress = (leftProgress + 30).clamp(0.0, 1000.0);
      } else if (leftProgress >= 150 && leftProgress < 300) {
        leftProgress = (leftProgress + 10).clamp(0.0, 1000.0);
      } else {
        leftProgress = (leftProgress + 5).clamp(0.0, 1000.0);
      }
    });
    context.read<StimulationBloc>().add(
          ChangeEarIntensityEvent(
            bleDeviceInteractor: widget.deviceInteractor,
            deviceId: widget.deviceId,
            leftElectrodeIntensity: leftProgress.toStringAsFixed(0),
            rightElectrodeIntensity: rightProgress.toStringAsFixed(0),
          ),
        );
  }

  void onPressedLeftMinus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }
    setState(() {
      if (leftProgress <= 150) {
        leftProgress = (leftProgress - 30).clamp(0.0, 1000.0);
      } else if (leftProgress > 150 && leftProgress <= 300) {
        leftProgress = (leftProgress - 10).clamp(0.0, 1000.0);
      } else {
        leftProgress = (leftProgress - 5).clamp(0.0, 1000.0);
      }
    });
    context.read<StimulationBloc>().add(
          ChangeEarIntensityEvent(
            bleDeviceInteractor: widget.deviceInteractor,
            deviceId: widget.deviceId,
            leftElectrodeIntensity: leftProgress.toStringAsFixed(0),
            rightElectrodeIntensity: rightProgress.toStringAsFixed(0),
          ),
        );
  }

  void onPressedRightMinus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }
    setState(() {
      if (rightProgress <= 150) {
        rightProgress = (rightProgress - 30).clamp(0.0, 1000.0);
      } else if (rightProgress > 150 && rightProgress <= 300) {
        rightProgress = (rightProgress - 10).clamp(0.0, 1000.0);
      } else {
        rightProgress = (rightProgress - 5).clamp(0.0, 1000.0);
      }
    });

    context.read<StimulationBloc>().add(
          ChangeEarIntensityEvent(
            bleDeviceInteractor: widget.deviceInteractor,
            deviceId: widget.deviceId,
            leftElectrodeIntensity: leftProgress.toStringAsFixed(0),
            rightElectrodeIntensity: rightProgress.toStringAsFixed(0),
          ),
        );
  }

  void onPressedRightPlus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }

    setState(() {
      if (rightProgress < 150) {
        rightProgress = (rightProgress + 30).clamp(0.0, 1000.0);
      } else if (rightProgress >= 150 && rightProgress < 300) {
        rightProgress = (rightProgress + 10).clamp(0.0, 1000.0);
      } else {
        rightProgress = (rightProgress + 5).clamp(0.0, 1000.0);
      }
    });

    context.read<StimulationBloc>().add(
          ChangeEarIntensityEvent(
            bleDeviceInteractor: widget.deviceInteractor,
            deviceId: widget.deviceId,
            leftElectrodeIntensity: leftProgress.toStringAsFixed(0),
            rightElectrodeIntensity: rightProgress.toStringAsFixed(0),
          ),
        );
  }

  void showParameterGetFailedDialog() {
    if (!context.mounted) return;

    isShowingDialog = true;
    unawaited(AppDialog(
      barrierDismissible: false,
      context: context,
      description:
          "Session type parameter not found. Please choose a different session type to proceed.",
      isDisableCancelButton: true,
      isDisableCloseButton: true,
      okButtonOnTap: () {
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
      },
      title: "Parameter Not Found",
    ).show);
  }

  void _handleOnTapHeartRateDialog() {
    if (!context.mounted) return;
    if (widget.sessionTypeParams.sessionType != SessionType.aiDriven) return;

    final selectSessionTypePageConfig =
        GetIt.instance.get<RemoteConfigService>().selectYourSessionTypePageConfig;

    final SelectYourSessionTypePageConfig(
      :aiDrivenHrvDialogTitle,
      :aiDrivenHrvSkipBtnDesc,
      :laterBtn,
      :startBtn,
    ) = selectSessionTypePageConfig;

    unawaited(AppDialog(
      cancelButtonText: laterBtn,
      context: context,
      description: aiDrivenHrvSkipBtnDesc,
      isCancelBtnNavigatePop: true,
      okButtonOnTap: _handleOnTapHeartRate,
      okButtonText: startBtn,
      title: aiDrivenHrvDialogTitle,
    ).show);
  }

  void _handleOnTapHeartRate() async {
    final subuser = (context.read<AuthBloc>().currentUser?.defaultSubuser);
    final response = await AppRoute.pushNewScreen(
      context,
      StartCameraView(isAiDriven: true, isCanPop: false, subuser: subuser),
      specificIndex: 1,
      withNavBar: false,
    );

    if (response != null) {
      unawaited(showModalBottomSheet(
        builder: (ctx) => BlocProvider<HrvBloc>.value(
          value: GetIt.instance.get(),
          child: AiDrivenResultWidget(isNavigateBleSearch: false),
        ),
        context: context,
        isScrollControlled: true,
        useRootNavigator: true,
        useSafeArea: true,
      ));
    }
  }

  @override
  void initState() {
    super.initState();

    // Sends an analytics event to track user.
    AnalyticHelper.instance.track(AppStrings.stimulationControlPage);
    Future.microtask(() {
      if (!widget.isContinuousStimulation) {
        ((this as StimulationControlTutorialMixin).createAndShowTutorial());
      }

      if (mounted) {
        context.read<OtaBloc>().add(StatusEvent(status: OtaStatus.initial));

        context.read<StimulationBloc>().add(StimulationReadBleDeviceConfigsEvent(
              bleDeviceInteractor: widget.deviceInteractor,
              deviceId: widget.deviceId,
              deviceName: widget.deviceName ?? "",
            ));
      }
    });
    if (!widget.isContinuousStimulation) {
      context.read<NewParameterBloc>().add(ParameterGetRequstedEvent(
            params: widget.sessionTypeParams,
          ));
    }
    final stimulationBloc = context.read<StimulationBloc>();

    stimulationBloc.currentSessionType = widget.sessionTypeParams.sessionType;
    stimulationBloc.presetProgramType = widget.sessionTypeParams.presetProgram;

    if (FlavorConstants.isHealthPro(bundleId)) {
      final multiDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();
      final device = multiDeviceBloc.state.devices
          ?.firstWhereOrNull((element) => element.bleId == widget.deviceId);

      device?.isConnectedStim = true;

      if (device != null) multiDeviceBloc.add(ChangeConnectedDeviceStatus(device: device));
    }
  }

  void _handleOnPopInvoked() async {
    final bloc = context.read<StimulationBloc>();
    await bloc.deviceStreamsMap[widget.deviceId]?.disposeAll();
  }

  @override
  void dispose() {
    final tutorialCoachMark = (this as StimulationControlTutorialMixin).tutorialCoachMark;
    // If tutorial is showing, finish it (if it's not showing, it will be null.
    if (tutorialCoachMark != null) {
      tutorialCoachMark.finish();
    }
    super.dispose();
  }
}
