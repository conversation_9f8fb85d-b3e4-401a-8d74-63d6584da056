// ignore_for_file: prefer-extracting-function-callbacks, avoid-unrelated-type-assertions, avoid-type-casts, avoid-unrelated-type-casts

part of 'stimulation_control_widget.dart';

mixin StimulationControlMixin on State<StimulationControlWidget> {
  // Constants for command types.
  static const _startCommand = 'start';
  static const _stopCommand = 'stop';
  static const _pauseCommand = 'pause';

  // Constants for device status.
  static const _pauseStatus = 'pause';

  // Constants for dialog messages.
  static const _parameterNotFoundTitle = 'Parameter Not Found';
  static const _parameterNotFoundDescription =
      'Session type parameter not found. Please choose a different session type to proceed.';

  // Constants for intensity thresholds and adjustments.
  static const _lowIntensityThreshold = 150.0;
  static const _mediumIntensityThreshold = 300.0;
  static const _lowIntensityIncrement = 30.0;
  static const _mediumIntensityIncrement = 10.0;
  static const _highIntensityIncrement = 5.0;
  static const _minIntensity = 0.0;
  static const _maxIntensity = 1000.0;

  bool isHeadphoneLSelected = true;
  double leftProgress = 0;
  double rightProgress = 0;
  bool isHeadLConnected = true;
  bool isHeadRConnected = true;
  bool isStimOffTime = false;

  bool isShowingDialog = false;

  // Get Total duration for user.
  int? timeLeft;

  void onPressedStart() {
    /*final timeOfUsageEnums = context.read<TimeOfUsageBloc>().state.timeOfUsageEnums;
    final userMaxRemainingSecond = context.read<TimeOfUsageBloc>().state.userMaxRemainingSecond;
    final userMaxRemainingMinute =
        ((userMaxRemainingSecond ?? 0) ~/ 60) > 0 ? ((userMaxRemainingSecond ?? 0) ~/ 60) : 0;

    if (timeOfUsageEnums != null) {
      AppDialog(
        cancelButtonOnTap: () => timeOfUsageEnums.timeOfUsageCancelButtonOnTap(context)(),
        cancelButtonText: timeOfUsageEnums.timeOfUsageCancelButtonText,
        context: context,
        description: timeOfUsageEnums.timeOfUsageToDialogDescription(
          value: (userMaxRemainingMinute > 0) ? (userMaxRemainingMinute).toString() : "0",
        ),
        icon: timeOfUsageEnums.timeOfUsageAlertIcon,
        isDynamicButtonResize: false,
        okButtonOnTap: () => timeOfUsageEnums.timeOfUsageOkButtonOnTap(
          context,
          widget.subuser,
          value: userMaxRemainingMinute == 0
              ? timeOfUsageEnums.timeOfUsageToOkButtonUpdateDurationParameters()
              : userMaxRemainingMinute,
        )(),
      ).show;

      bool isRedAlert = (timeOfUsageEnums == TimeOfUsageEnums.redAlert);
      if (isRedAlert) return;
    }
  */

    if (FlavorConstants.isHealthPro(bundleId)) {
      final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();

      final connectedDevices = multipleDeviceBloc.state.devices;

      for (MultipleDeviceEntity element in connectedDevices ?? []) {
        if (element.bleId == widget.deviceId) {
          multipleDeviceBloc.add(
            ChangeConnectedDeviceStatus(
              device: element,
              status: DeviceStatusEnum.ongoing,
              subuser: widget.subuser,
            ),
          );
        }
      }
    }
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      StimulationStartEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
      ),
    );

    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: _startCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
  }

  final bundleId =
      GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

  void onPressedStop(DeviceStatusReadSuccess state) {
    _updateMultipleDevicesStatus(state);

    if (state.deviceStatus == _pauseStatus) {
      _stopStimulation();
    } else {
      _pauseStimulation();
    }
  }

  void _updateMultipleDevicesStatus(DeviceStatusReadSuccess state) {
    if (!FlavorConstants.isHealthPro(bundleId)) return;

    final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();
    final connectedDevices = multipleDeviceBloc.state.devices;

    for (MultipleDeviceEntity element in connectedDevices ?? []) {
      if (element.bleId == widget.deviceId) {
        multipleDeviceBloc.add(ChangeConnectedDeviceStatus(
          device: element,
          status: state.deviceStatus == _pauseStatus
              ? DeviceStatusEnum.connected
              : DeviceStatusEnum.paused,
          subuser: widget.subuser,
        ));
      }
    }
  }

  void _stopStimulation() {
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: _stopCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
    stimulationBloc.add(
      StimulationStopEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
        subuserUid: _safeSubuserUid,
      ),
    );

    _resetProgress();
    _handleOnTapHeartRateDialog();
  }

  void _pauseStimulation() {
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      StimulationPauseEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
      ),
    );
    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: _pauseCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
  }

  void _resetProgress() {
    setState(() {
      leftProgress = 0.0;
      rightProgress = 0.0;
    });
  }

  void _updateProgressFromParameters({
    required double leftIntensity,
    required double rightIntensity,
  }) {
    setState(() {
      leftProgress = leftIntensity;
      rightProgress = rightIntensity;
    });
  }

  String get _safeSubuserUid => widget.subuser.uid ?? '';

  void onPressedHelp() {
    final stimulationBloc = context.read<StimulationBloc>();

    stimulationBloc.add(
      StimulationCreateEvent(
        commandType: _stopCommand,
        sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
        subuserUid: _safeSubuserUid,
      ),
    );
    stimulationBloc.add(
      StimulationStopEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
        subuserUid: _safeSubuserUid,
      ),
    );

    setState(() {
      leftProgress = 0.0;
      rightProgress = 0.0;
    });
  }

  void onPressedLeftPlus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }

    _incrementLeftProgress();
    _updateEarIntensity();
  }

  void _incrementLeftProgress() {
    setState(() {
      if (leftProgress < _lowIntensityThreshold) {
        leftProgress = (leftProgress + _lowIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      } else if (leftProgress >= _lowIntensityThreshold &&
          leftProgress < _mediumIntensityThreshold) {
        leftProgress =
            (leftProgress + _mediumIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      } else {
        leftProgress = (leftProgress + _highIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      }
    });
  }

  void _updateEarIntensity() {
    final stimulationBloc = context.read<StimulationBloc>();
    stimulationBloc.add(
      ChangeEarIntensityEvent(
        bleDeviceInteractor: widget.deviceInteractor,
        deviceId: widget.deviceId,
        leftElectrodeIntensity: leftProgress.toStringAsFixed(0),
        rightElectrodeIntensity: rightProgress.toStringAsFixed(0),
      ),
    );
  }

  void onPressedLeftMinus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }
    _decrementLeftProgress();
    _updateEarIntensity();
  }

  void _decrementLeftProgress() {
    setState(() {
      if (leftProgress <= _lowIntensityThreshold) {
        leftProgress = (leftProgress - _lowIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      } else if (leftProgress > _lowIntensityThreshold &&
          leftProgress <= _mediumIntensityThreshold) {
        leftProgress =
            (leftProgress - _mediumIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      } else {
        leftProgress = (leftProgress - _highIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      }
    });
  }

  void onPressedRightMinus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }
    _decrementRightProgress();
    _updateEarIntensity();
  }

  void _decrementRightProgress() {
    setState(() {
      if (rightProgress <= _lowIntensityThreshold) {
        rightProgress =
            (rightProgress - _lowIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      } else if (rightProgress > _lowIntensityThreshold &&
          rightProgress <= _mediumIntensityThreshold) {
        rightProgress =
            (rightProgress - _mediumIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      } else {
        rightProgress =
            (rightProgress - _highIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      }
    });
  }

  void onPressedRightPlus() {
    if (widget.deviceId.isEmpty && (!mounted)) {
      return;
    }

    _incrementRightProgress();
    _updateEarIntensity();
  }

  void _incrementRightProgress() {
    setState(() {
      if (rightProgress < _lowIntensityThreshold) {
        rightProgress =
            (rightProgress + _lowIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      } else if (rightProgress >= _lowIntensityThreshold &&
          rightProgress < _mediumIntensityThreshold) {
        rightProgress =
            (rightProgress + _mediumIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      } else {
        rightProgress =
            (rightProgress + _highIntensityIncrement).clamp(_minIntensity, _maxIntensity);
      }
    });
  }

  void showParameterGetFailedDialog() {
    if (!context.mounted) return;

    isShowingDialog = true;
    unawaited(AppDialog(
      barrierDismissible: false,
      context: context,
      description: _parameterNotFoundDescription,
      isDisableCancelButton: true,
      isDisableCloseButton: true,
      okButtonOnTap: () {
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
      },
      title: _parameterNotFoundTitle,
    ).show);
  }

  void _handleOnTapHeartRateDialog() {
    if (!context.mounted) return;
    if (widget.sessionTypeParams.sessionType != SessionType.aiDriven) return;

    final selectSessionTypePageConfig =
        GetIt.instance.get<RemoteConfigService>().selectYourSessionTypePageConfig;

    final SelectYourSessionTypePageConfig(
      :aiDrivenHrvDialogTitle,
      :aiDrivenHrvSkipBtnDesc,
      :laterBtn,
      :startBtn,
    ) = selectSessionTypePageConfig;

    unawaited(AppDialog(
      cancelButtonText: laterBtn,
      context: context,
      description: aiDrivenHrvSkipBtnDesc,
      isCancelBtnNavigatePop: true,
      okButtonOnTap: _handleOnTapHeartRate,
      okButtonText: startBtn,
      title: aiDrivenHrvDialogTitle,
    ).show);
  }

  void _handleOnTapHeartRate() async {
    final subuser = (context.read<AuthBloc>().currentUser?.defaultSubuser);
    final response = await AppRoute.pushNewScreen(
      context,
      StartCameraView(isAiDriven: true, isCanPop: false, subuser: subuser),
      specificIndex: 1,
      withNavBar: false,
    );

    if (response != null) {
      unawaited(showModalBottomSheet(
        builder: (ctx) => BlocProvider<HrvBloc>.value(
          value: GetIt.instance.get(),
          child: AiDrivenResultWidget(isNavigateBleSearch: false),
        ),
        context: context,
        isScrollControlled: true,
        useRootNavigator: true,
        useSafeArea: true,
      ));
    }
  }

  @override
  void initState() {
    super.initState();

    // Sends an analytics event to track user.
    AnalyticHelper.instance.track(AppStrings.stimulationControlPage);
    Future.microtask(() {
      if (!widget.isContinuousStimulation && this is StimulationControlTutorialMixin) {
        (this as StimulationControlTutorialMixin).createAndShowTutorial();
      }

      if (mounted) {
        context.read<OtaBloc>().add(StatusEvent(status: OtaStatus.initial));

        context.read<StimulationBloc>().add(StimulationReadBleDeviceConfigsEvent(
              bleDeviceInteractor: widget.deviceInteractor,
              deviceId: widget.deviceId,
              deviceName: widget.deviceName ?? "",
            ));
      }
    });
    if (!widget.isContinuousStimulation) {
      context.read<NewParameterBloc>().add(ParameterGetRequstedEvent(
            params: widget.sessionTypeParams,
          ));
    }
    final stimulationBloc = context.read<StimulationBloc>();

    stimulationBloc.currentSessionType = widget.sessionTypeParams.sessionType;
    stimulationBloc.presetProgramType = widget.sessionTypeParams.presetProgram;

    if (FlavorConstants.isHealthPro(bundleId)) {
      final multiDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();
      final device = multiDeviceBloc.state.devices
          ?.firstWhereOrNull((element) => element.bleId == widget.deviceId);

      device?.isConnectedStim = true;

      if (device != null) multiDeviceBloc.add(ChangeConnectedDeviceStatus(device: device));
    }
  }

  void _handleOnPopInvoked() async {
    final bloc = context.read<StimulationBloc>();
    await bloc.deviceStreamsMap[widget.deviceId]?.disposeAll();
  }

  @override
  void dispose() {
    if (this is StimulationControlTutorialMixin) {
      final tutorialCoachMark = (this as StimulationControlTutorialMixin).tutorialCoachMark;
      // If tutorial is showing, finish it (if it's not showing, it will be null.
      if (tutorialCoachMark != null) {
        tutorialCoachMark.finish();
      }
    }
    super.dispose();
  }
}
