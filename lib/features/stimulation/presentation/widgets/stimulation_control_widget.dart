// ignore_for_file: prefer-extracting-callbacks, avoid-long-functions, avoid-long-files

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/custom_widgets/minimal_loader.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/extension/iterable_extension.dart';
import 'package:vagustimpro/core/navigator/app_navigator.dart';
import 'package:vagustimpro/core/ble/ble_device_connector.dart';
import 'package:vagustimpro/core/ble/ble_device_interactor.dart';
import 'package:vagustimpro/core/navigator/routes/app_route.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';
import 'package:vagustimpro/core/services/dialog_service.dart';
import 'package:vagustimpro/features/app_permission/presentation/bloc/app_permission_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/multiple_device/domain/entities/device_status_enum.dart';
import 'package:vagustimpro/features/multiple_device/presentation/bloc/multiple_device_event.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_bloc.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_event.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_status.dart';
import 'package:vagustimpro/features/parameter/domain/params/session_type_params.dart';
import 'package:vagustimpro/features/parameter/presentation/new_bloc/new_parameter_bloc.dart';
import 'package:vagustimpro/features/stimulation/presentation/bloc/stimulation_bloc.dart';
import 'package:vagustimpro/features/stimulation/presentation/pages/stimulation_advance_control_page.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/control_panel.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/device_streams.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/headphone_control.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/remaining_time.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/stimulation_control_appbar_trailing.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/tutorial/stimulation_control_target_identify_constants.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/tutorial/target_contents/stimulation_control_tutorial_adjust_parameters.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/home_card.dart';
import '../../../../app/flavor/flavor_config.dart';
import '../../../../core/app_config/app_strings.dart';
import '../../../../core/app_config/app_text_styles.dart';
import '../../../../core/custom_widgets/loader.dart';
import '../../../../core/enum/assets_enums_lottie.dart';
import '../../../../core/helpers/analytic_helper.dart';
import '../../../../core/remote_config/select_your_session_type_page_config.dart';
import '../../../hrv/presentation/bloc/hrv_bloc.dart';
import '../../../hrv/presentation/pages/start_camera_view.dart';
import '../../../multiple_device/domain/entities/multiple_device_entity.dart';
import '../../../multiple_device/presentation/bloc/multiple_device_bloc.dart';
import '../../../ota/presentation/pages/ota_page.dart';
import '../../../parameter/domain/params/session_type.dart';
import '../../../parameter/presentation/new_bloc/new_parameter_event.dart';
import '../../../parameter/presentation/new_bloc/new_parameter_state.dart';
import '../../../parameter/presentation/pages/widgets/select_session_type/ai_driven_result/ai_driven_result_widget.dart';
import '../../../subuser/presentation/pages/subuser_detail_view.dart';
import '../../../time_of_usage/presentation/bloc/time_of_usage_bloc.dart';
import '../../../time_of_usage/presentation/widgets/app_dialog.dart';
import 'battery/batter_indicator_widget.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_head_phone_control.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_left_ear.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_pause_button.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_remaining.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_right_ear.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_skip.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_start_button.dart';
import 'tutorial/walktrough/stimulation_control_walktrough_tutorial.dart';

part 'tutorial/stimulation_control_tutorial_mixin.dart';
part 'stimulation_control_mixin.dart';
part 'tutorial/stimulation_control_tutorial_methods_mixin.dart';

class StimulationControlWidget extends StatefulWidget {
  const StimulationControlWidget({
    required this.deviceConnector,
    required this.deviceId,
    required this.deviceInteractor,
    this.deviceName,
    required this.isContinuousStimulation,
    super.key,
    required this.sessionTypeParams,
    required this.subuser,
  });

  final BleDeviceInteractor deviceInteractor;
  final BleDeviceConnector deviceConnector;
  final String deviceId;
  final SubuserEntity subuser;
  final SessionTypeParams sessionTypeParams;
  final bool isContinuousStimulation;
  final String? deviceName;

  @override
  State<StimulationControlWidget> createState() => _StimulationMainControlWidgetState();
}

class _StimulationMainControlWidgetState extends State<StimulationControlWidget>
    with
        StimulationControlMixin,
        StimulationControlTutorialMixin,
        StimulationControlTutorialMethodsMixin {
  @override
  void initState() {
    super.initState();

    // Sends an analytics event to track user.
    AnalyticHelper.instance.track(AppStrings.stimulationControlPage);
    Future.microtask(() {
      if (!widget.isContinuousStimulation) createAndShowTutorial();

      if (mounted) {
        context.read<OtaBloc>().add(StatusEvent(status: OtaStatus.initial));

        context.read<StimulationBloc>().add(StimulationReadBleDeviceConfigsEvent(
              bleDeviceInteractor: widget.deviceInteractor,
              deviceId: widget.deviceId,
              deviceName: widget.deviceName ?? "",
            ));
      }
    });
    if (!widget.isContinuousStimulation) {
      context.read<NewParameterBloc>().add(ParameterGetRequstedEvent(
            params: widget.sessionTypeParams,
          ));
    }
    final stimulationBloc = context.read<StimulationBloc>();

    stimulationBloc.currentSessionType = widget.sessionTypeParams.sessionType;
    stimulationBloc.presetProgramType = widget.sessionTypeParams.presetProgram;

    if (FlavorConstants.isHealthPro(bundleId)) {
      final multiDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();
      final device = multiDeviceBloc.state.devices
          ?.firstWhereOrNull((element) => element.bleId == widget.deviceId);

      device?.isConnectedStim = true;

      if (device != null) multiDeviceBloc.add(ChangeConnectedDeviceStatus(device: device));
    }
  }

  void _handleOnPopInvoked() async {
    final bloc = context.read<StimulationBloc>();
    await bloc.deviceStreamsMap[widget.deviceId]?.disposeAll();
  }

  @override
  void dispose() {
    if (tutorialCoachMark != null) {
      tutorialCoachMark?.finish();
      tutorialCoachMark = null;
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dialogService = GetIt.instance.get<DialogService>();
    final isIndividual =
        BlocProvider.of<AuthBloc>(context, listen: false).currentUser!.userType == 'individuals';
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final deviceControlPageConfig = remoteConfigService.deviceControlPageConfig;

    final connectionState = context.watch<ConnectionStateUpdate>().connectionState;

    if (connectionState == DeviceConnectionState.disconnected && tutorialCoachMark != null) {
      tutorialCoachMark?.finish();
    }

    return PopScope(
      onPopInvokedWithResult: (didPop, result) => _handleOnPopInvoked(),
      child: Scaffold(
        appBar: CupertinoNavigationBar(
          middle: Text(deviceControlPageConfig.advanceDeviceControlPageNavbarTxt),
          trailing: StimulationControlAppbarTrailing(onTap: (state) {
            onPressedHelp();
            createAndShowTutorial(isHideSkip: false);
          }),
        ),
        body: SingleChildScrollView(
          child: SafeArea(
            child: MultiBlocListener(
              listeners: [
                BlocListener<NewParameterBloc, NewParameterState>(
                  listener: (ctx, state) {
                    if (state.status == ParameterStatus.success) {
                      if (FlavorConstants.isHealthPro(bundleId)) {
                        final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();

                        final device = multipleDeviceBloc.state.devices
                            ?.firstWhereOrNull((element) => element.bleId == widget.deviceId);
                        if (device != null) {
                          multipleDeviceBloc.add(ChangeConnectedDeviceStatus(
                            device: device.copyWith(
                              totalDuration:
                                  ((state.parameterEntity?.totalDurationTime ?? 0).toDouble()),
                            ),
                          ));
                        }
                      }
                      context.read<StimulationBloc>().add(
                            StimulationUpdateBleDeviceConfigsEvent(
                              bleDeviceInteractor: widget.deviceInteractor,
                              deviceId: widget.deviceId,
                              durationForOffTime:
                                  state.parameterEntity?.durationForOffTime?.toString() ?? '0',
                              durationForOnTime:
                                  state.parameterEntity?.durationForOnTime?.toString() ?? '0',
                              frequency: state.parameterEntity?.frequency?.toString() ?? '0',
                              pulseWidth: state.parameterEntity?.pulseWidth?.toString() ?? '0',
                              totalDuration: ((state.parameterEntity?.totalDurationTime ?? 0) / 60)
                                  .toStringAsFixed(0),
                            ),
                          );
                    }
                    /*  if (state.status == ParameterStatus.failure && !isShowingDialog) {
                      showParameterGetFailedDialog();
                    }*/
                  },
                ),
                MultiBlocListener(
                  listeners: [
                    BlocListener<StimulationBloc, StimulationState>(
                      listenWhen: (previous, current) =>
                          current is DeviceInfoReadSuccess && previous != current,
                      listener: (ctx, state) {
                        if ((state is DeviceInfoReadSuccess)) {
                          final otaBloc = context.read<OtaBloc>();
                          otaBloc.add(
                            SaveDeviceEvent(
                              bleId: widget.deviceId,
                              deviceId: state.embeddedMAC,
                              firmwareVersion: state.embeddedVersion,
                            ),
                          );

                          if (((!(otaBloc.state.isShowingDialog ?? false)) &&
                              (otaBloc.state.status != OtaStatus.updateSucces) &&
                              (!widget.isContinuousStimulation ||
                                  otaBloc.state.status != OtaStatus.incorrectDevice))) {
                            unawaited(OtaPage(
                              deviceId: widget.deviceId,
                              deviceMac: state.embeddedMAC,
                              pageContext: ctx,
                            ).show());
                          }
                        }
                      },
                    ),
                  ],
                  child: const SizedBox(),
                ),
                BlocListener<StimulationBloc, StimulationState>(
                  listenWhen: (previous, current) =>
                      current is StimulationParametersReadSuccess && previous != current,
                  listener: (ctx, state) {
                    if (state is StimulationParametersReadSuccess) {
                      setState(() {
                        leftProgress =
                            double.parse(state.stimulation.leftIntensity?.toString() ?? '0');
                        rightProgress =
                            double.parse(state.stimulation.rightIntensity?.toString() ?? '0');
                      });
                    }
                  },
                ),
                BlocListener<StimulationBloc, StimulationState>(
                  listenWhen: (previous, current) {
                    return previous is! DeviceStatusReadSuccess &&
                        current is DeviceStatusReadSuccess &&
                        previous != current &&
                        current.deviceStatus == 'stop';
                  },
                  listener: (ctx, state) {
                    if (state is DeviceStatusReadSuccess &&
                        state.deviceStatus == 'stop' &&
                        (widget.subuser.uid ?? "").isNotEmpty) {
                      context
                          .read<NewParameterBloc>()
                          .add(ParameterGetRequstedEvent(params: widget.sessionTypeParams));
                    }
                  },
                ),
                BlocListener<StimulationBloc, StimulationState>(
                  listenWhen: (previous, current) {
                    return previous is! StimFinishSuccess &&
                        current is StimFinishSuccess &&
                        previous != current &&
                        current.deviceStatus == 'fin';
                  },
                  listener: (ctx, state) {
                    if (state is StimFinishSuccess) {
                      context.read<StimulationBloc>().add(
                            StimulationCreateEvent(
                              commandType: 'finish',
                              sessionType: widget.sessionTypeParams.presetProgram?.getRequestName,
                              subuserUid: widget.subuser.uid!,
                            ),
                          );

                      context.read<StimulationBloc>().add(
                            StimulationStopEvent(
                              bleDeviceInteractor: widget.deviceInteractor,
                              deviceId: widget.deviceId,
                              subuserUid: widget.subuser.uid!,
                            ),
                          );

                      _handleOnTapHeartRateDialog();

                      dialogService.showDialog(
                        confirmButtonText: deviceControlPageConfig.stimulationCompleteAlertButton,
                        message: deviceControlPageConfig.stimulationCompleteAlertMessage,
                        navigatorKey: AppNavigator.navigatorKey,
                        onConfirm: () {
                          if (!mounted) return;

                          if (FlavorConstants.isHealthPro(bundleId)) {
                            final bloc = GetIt.instance.get<MultipleDeviceBloc>();

                            final currentEntity = bloc.state.devices
                                ?.firstWhereOrNull((element) => element.bleId == widget.deviceId);

                            if (currentEntity?.deviceStatus != DeviceStatusEnum.completed) {
                              bloc.add(ChangeConnectedDeviceStatus(
                                device: currentEntity!,
                                status: DeviceStatusEnum.completed,
                              ));
                            }
                          }
                          final isIndividual = context.read<AuthBloc>().currentUser?.userType ==
                              AppStrings.individualType;
                          if (!isIndividual) return;
                          const individuals = 'individuals';

                          unawaited(AppRoute.pushNewScreen(
                            context,
                            SubuserDetailView(
                              isHistory: true,
                              subuser: widget.subuser,
                              userType: individuals,
                            ),
                          ));
                        },
                        title: deviceControlPageConfig.stimulationCompleteAlertTitle,
                      );
                    }
                  },
                ),
              ],
              child: BlocConsumer<StimulationBloc, StimulationState>(
                builder: (ctx, state) {
                  final deviceConnectionState = ctx.watch<ConnectionStateUpdate>().connectionState;

                  return deviceConnectionState == DeviceConnectionState.disconnected
                      ? SizedBox()
                      : Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            BlocBuilder<StimulationBloc, StimulationState>(
                              buildWhen: (previous, current) {
                                return current is DeviceStatusReadSuccess && previous != current;
                              },
                              builder: (ctx, state) {
                                final otaBloc = context.read<OtaBloc>();
                                const incorrectDevice = "Incorrect Product";
                                final lottieHeight = 60.0;

                                return otaBloc.state.status == OtaStatus.incorrectDevice
                                    ? Padding(
                                        padding: ctx.paddingMedium,
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Center(
                                              child: AssetsEnumsLottie.error
                                                  .toLottie(height: lottieHeight),
                                            ),
                                            AppGaps.instance.gapHS16,
                                            Center(
                                              child: Text(
                                                incorrectDevice,
                                                style: AppTextStyles.appDialogTitleStyle,
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    : Padding(
                                        padding: ctx.paddingMedium,
                                        child: HomeCard(
                                          child: Padding(
                                            padding: ctx.paddingDisplay,
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                SizedBox(
                                                  width: double.infinity,
                                                  child: state is DeviceStatusReadSuccess
                                                      ? Column(
                                                          children: [
                                                            BatterIndicatorWidget(),
                                                            ControlPanel(
                                                              adjustParametersButtonKey:
                                                                  adjustParametersButtonKey,
                                                              isDisableAdjustParameters: widget
                                                                          .sessionTypeParams
                                                                          .sessionType ==
                                                                      SessionType.aiDriven ||
                                                                  widget.sessionTypeParams
                                                                          .sessionType ==
                                                                      SessionType.preSetPrograms,
                                                              isStimPaused:
                                                                  state.deviceStatus == 'pause',
                                                              isStimStarted:
                                                                  state.deviceStatus != 'pause' &&
                                                                      state.deviceStatus != 'stop',
                                                              onPressAdjustParameters: () => {
                                                                AppRoute.pushNewScreen(
                                                                  context,
                                                                  StimulationAdvanceControlPage(
                                                                    bleDeviceInteractor:
                                                                        widget.deviceInteractor,
                                                                    deviceId: widget.deviceId,
                                                                    subuserId: widget.subuser.uid!,
                                                                  ),
                                                                ),
                                                              },
                                                              onPressStart: () => onPressedStart(),
                                                              onPressStop: () =>
                                                                  onPressedStop(state),
                                                              pauseButtonKey: pauseButtonKey,
                                                              startButtonKey: startButtonKey,
                                                            ),
                                                            AppGaps.instance.gapVS8,
                                                            Divider(),
                                                            BlocBuilder<StimulationBloc,
                                                                StimulationState>(
                                                              buildWhen: (previous, current) {
                                                                return current
                                                                        is StimulationTimeAndIntensityReadSuccess &&
                                                                    current.deviceId ==
                                                                        widget.deviceId;
                                                              },
                                                              builder: (ctxStim, state) {
                                                                debugPrint(
                                                                  'StimulationTimeAndIntensityReadSuccess $state',
                                                                );
                                                                if (state
                                                                    is! StimulationTimeAndIntensityReadSuccess) {
                                                                  return MinimalLoader();
                                                                }

                                                                if (state is StimulationLoading) {
                                                                  return MinimalLoader();
                                                                }

                                                                if (state.deviceId !=
                                                                    widget.deviceId) {
                                                                  return MinimalLoader();
                                                                }

                                                                if (DeviceStreams(state.deviceId)
                                                                        .stimulationTimeAndIntensityStream ==
                                                                    null) {
                                                                  return MinimalLoader();
                                                                }

                                                                final appPermissionState = context
                                                                    .read<AppPermissionBloc>()
                                                                    .state;

                                                                final hasCurrentIntensityPermission =
                                                                    appPermissionState
                                                                            is AppPermissionSuccess &&
                                                                        appPermissionState
                                                                            .appPermissions
                                                                            .any(
                                                                          (permission) =>
                                                                              permission.name ==
                                                                              "current_intensity",
                                                                        );
                                                                timeLeft = state.timeLeft;

                                                                context.read<TimeOfUsageBloc>().add(
                                                                      GetTimeOfUsageEvent(
                                                                        scheduledDuration: timeLeft,
                                                                      ),
                                                                    );

                                                                return RemainingTime(
                                                                  deviceControlPageConfig:
                                                                      deviceControlPageConfig,
                                                                  deviceId: widget.deviceId,
                                                                  hasCurrentIntensityPermission:
                                                                      hasCurrentIntensityPermission,
                                                                  key: remainingKey,
                                                                  state: state,
                                                                );
                                                              },
                                                            ),
                                                          ],
                                                        )
                                                      : SizedBox(
                                                          height: ctx.height * 0.7,
                                                          child: Center(child: Loader()),
                                                        ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );
                              },
                            ),
                            BlocBuilder<StimulationBloc, StimulationState>(
                              buildWhen: (previous, current) {
                                return current is DeviceStatusReadSuccess && previous != current;
                              },
                              builder: (context, state) {
                                return state is DeviceStatusReadSuccess
                                    ? SizedBox(
                                        height: context.height * 0.44,
                                        child: HeadphoneControl(
                                          isLeftConnected: headLConnected,
                                          isOffTime: stimOffTime,
                                          isRightConnected: headRConnected,
                                          isStimStarted: state.deviceStatus != 'stop' &&
                                              state.deviceStatus != 'pause',
                                          key: headPhoneControlKey,
                                          leftEarButtonKey: leftEarIncrementButtonKey,
                                          leftEarMinusButtonKey: leftEarMinusButtonKey,
                                          leftProgress: leftProgress,
                                          onPressedLeftMinus: () => onPressedLeftMinus(),
                                          onPressedLeftPlus: () => onPressedLeftPlus(),
                                          onPressedRightMinus: () => onPressedRightMinus(),
                                          onPressedRightPlus: () => onPressedRightPlus(),
                                          rightEarButtonKey: rightEarIncrementButtonKey,
                                          rightEarMinusButtonKey: rightEarMinusButtonKey,
                                          rightProgress: rightProgress,
                                        ),
                                      )
                                    : const SizedBox(
                                        height: 300,
                                        width: double.infinity,
                                      );
                              },
                            ),
                          ],
                        );
                },
                listener: (ctx, state) {},
              ),
            ),
          ),
        ),
      ),
    );
  }
}
