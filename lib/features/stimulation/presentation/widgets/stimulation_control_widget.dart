import 'package:flutter/material.dart';
import 'stimulation_control_exports.dart';

part 'tutorial/stimulation_control_tutorial_mixin.dart';
part 'mixin/stimulation_control_mixin.dart';
part 'mixin/stimulation_dialog_mixin.dart';
part 'headphone_control/stimulation_control_headphone.dart';

part 'tutorial/stimulation_control_tutorial_methods_mixin.dart';
part 'listeners/ble_device_config_listener.dart';
part 'listeners/ble_device_read_listener.dart';
part 'listeners/stimulation_parameter_read_listener.dart';
part 'listeners/ble_device_status_listener.dart';
part 'listeners/ble_device_finish_listener.dart';
part 'control_panel/device_control_button/stimulation_control_panel.dart';

class StimulationControlWidget extends StatefulWidget {
  const StimulationControlWidget({
    required this.deviceConnector,
    required this.deviceId,
    required this.deviceInteractor,
    this.deviceName,
    required this.isContinuousStimulation,
    super.key,
    required this.sessionTypeParams,
    required this.subuser,
  });

  final BleDeviceInteractor deviceInteractor;
  final BleDeviceConnector deviceConnector;
  final String deviceId;
  final SubuserEntity subuser;
  final SessionTypeParams sessionTypeParams;
  final bool isContinuousStimulation;
  final String? deviceName;

  @override
  State<StimulationControlWidget> createState() => _StimulationMainControlWidgetState();
}

class _StimulationMainControlWidgetState extends State<StimulationControlWidget>
    with
        StimulationControlMixin,
        StimulationControlTutorialMixin,
        StimulationControlTutorialMethodsMixin,
        StimulationDialogMixin {
  @override
  Widget build(BuildContext context) {
    _handleDisconnect();

    return StimulationControlInheritedWidget(
      dialogMixin: this,
      methodsMixin: this,
      mixin: this,
      tutorialMixin: this,
      child: PopScope(
        onPopInvokedWithResult: (didPop, result) => _handleOnPopInvoked(),
        child: Scaffold(
          appBar: StimulationControlAppBar(onTrailingTap: _handleAppBarTrailingOnTap),
          body: StimulationControlBody(
            deviceId: widget.deviceId,
            deviceInteractor: widget.deviceInteractor,
            isContinuousStimulation: widget.isContinuousStimulation,
            onParametersRead: _handleUpdateProgressFromParameters,
            onStimulationFinish: _handleStimulationFinish,
            sessionTypeParams: widget.sessionTypeParams,
            subuser: widget.subuser,
          ),
        ),
      ),
    );
  }
}
