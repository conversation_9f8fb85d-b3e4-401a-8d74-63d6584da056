// ignore_for_file: prefer-extracting-callbacks, avoid-long-functions, avoid-long-files

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:get_it/get_it.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/custom_widgets/minimal_loader.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/extension/iterable_extension.dart';
import 'package:vagustimpro/core/navigator/app_navigator.dart';
import 'package:vagustimpro/core/ble/ble_device_connector.dart';
import 'package:vagustimpro/core/ble/ble_device_interactor.dart';
import 'package:vagustimpro/core/navigator/routes/app_route.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';
import 'package:vagustimpro/core/services/dialog_service.dart';
import 'package:vagustimpro/features/app_permission/presentation/bloc/app_permission_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/multiple_device/domain/entities/device_status_enum.dart';
import 'package:vagustimpro/features/multiple_device/presentation/bloc/multiple_device_event.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_bloc.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_event.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_status.dart';
import 'package:vagustimpro/features/parameter/domain/entities/parameter_entity.dart';
import 'package:vagustimpro/features/parameter/domain/params/session_type_params.dart';
import 'package:vagustimpro/features/parameter/presentation/new_bloc/new_parameter_bloc.dart';
import 'package:vagustimpro/features/stimulation/presentation/bloc/stimulation_bloc.dart';
import 'package:vagustimpro/features/stimulation/presentation/pages/stimulation_advance_control_page.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/control_panel/control_panel.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/device_streams.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/headphone_control.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/remaining_time/remaining_time.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/stimulation_control_appbar_trailing.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/tutorial/stimulation_control_target_identify_constants.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/tutorial/target_contents/stimulation_control_tutorial_adjust_parameters.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/home_card.dart';
import '../../../../app/flavor/flavor_config.dart';
import '../../../../core/app_config/app_strings.dart';
import '../../../../core/app_config/app_text_styles.dart';
import '../../../../core/ble/stimulation_control_constants.dart';
import '../../../../core/custom_widgets/loader.dart';
import '../../../../core/enum/assets_enums_lottie.dart';
import '../../../../core/helpers/analytic_helper.dart';
import '../../../../core/remote_config/select_your_session_type_page_config.dart';
import '../../../hrv/presentation/bloc/hrv_bloc.dart';
import '../../../hrv/presentation/pages/start_camera_view.dart';
import '../../../multiple_device/domain/entities/multiple_device_entity.dart';
import '../../../multiple_device/presentation/bloc/multiple_device_bloc.dart';
import '../../../ota/presentation/pages/ota_page.dart';
import '../../../parameter/domain/params/session_type.dart';
import '../../../parameter/presentation/new_bloc/new_parameter_event.dart';
import '../../../parameter/presentation/new_bloc/new_parameter_state.dart';
import '../../../parameter/presentation/pages/widgets/select_session_type/ai_driven_result/ai_driven_result_widget.dart';
import '../../../subuser/presentation/pages/subuser_detail_view.dart';
import '../../../time_of_usage/presentation/bloc/time_of_usage_bloc.dart';
import '../../../time_of_usage/presentation/widgets/app_dialog.dart';
import 'incorrect_device_widget.dart';
import 'inherited/stimulation_control_inherited_widget.dart';
import 'preset_program/stimulation_control_preset_item.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_head_phone_control.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_left_ear.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_pause_button.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_remaining.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_right_ear.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_skip.dart';
import 'tutorial/target_contents/stimulation_control_tutorial_start_button.dart';
import 'tutorial/walktrough/stimulation_control_walktrough_tutorial.dart';

part 'tutorial/stimulation_control_tutorial_mixin.dart';
part 'mixin/stimulation_control_mixin.dart';
part 'mixin/stimulation_dialog_mixin.dart';

part 'tutorial/stimulation_control_tutorial_methods_mixin.dart';
part 'listeners/ble_device_config_listener.dart';
part 'listeners/ble_device_read_listener.dart';
part 'listeners/stimulation_parameter_read_listener.dart';
part 'listeners/ble_device_status_listener.dart';
part 'listeners/ble_device_finish_listener.dart';

class StimulationControlWidget extends StatefulWidget {
  const StimulationControlWidget({
    required this.deviceConnector,
    required this.deviceId,
    required this.deviceInteractor,
    this.deviceName,
    required this.isContinuousStimulation,
    super.key,
    required this.sessionTypeParams,
    required this.subuser,
  });

  final BleDeviceInteractor deviceInteractor;
  final BleDeviceConnector deviceConnector;
  final String deviceId;
  final SubuserEntity subuser;
  final SessionTypeParams sessionTypeParams;
  final bool isContinuousStimulation;
  final String? deviceName;

  @override
  State<StimulationControlWidget> createState() => _StimulationMainControlWidgetState();
}

class _StimulationMainControlWidgetState extends State<StimulationControlWidget>
    with
        StimulationControlMixin,
        StimulationControlTutorialMixin,
        StimulationControlTutorialMethodsMixin,
        StimulationDialogMixin {
  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final deviceControlPageConfig = remoteConfigService.deviceControlPageConfig;

    final connectionState = context.watch<ConnectionStateUpdate>().connectionState;

    if (connectionState == DeviceConnectionState.disconnected && tutorialCoachMark != null) {
      tutorialCoachMark?.finish();
    }

    return StimulationControlInheritedWidget(
      dialogMixin: this,
      methodsMixin: this,
      mixin: this,
      tutorialMixin: this,
      child: PopScope(
        onPopInvokedWithResult: (didPop, result) => _handleOnPopInvoked(),
        child: Scaffold(
          appBar: CupertinoNavigationBar(
            middle: Text(deviceControlPageConfig.advanceDeviceControlPageNavbarTxt),
            trailing: StimulationControlAppbarTrailing(onTap: (state) {
              onPressedHelp();
              createAndShowTutorial(isHideSkip: false);
            }),
          ),
          body: SingleChildScrollView(
            child: SafeArea(
              child: MultiBlocListener(
                listeners: [
                  BleDeviceConfigListener.create(
                    deviceId: widget.deviceId,
                    deviceInteractor: widget.deviceInteractor,
                  ),
                  BleDeviceReadListener.create(
                    deviceId: widget.deviceId,
                    isContinuousStimulation: widget.isContinuousStimulation,
                  ),
                  StimulationParameterReadListener.create(
                    deviceId: widget.deviceId,
                    isContinuousStimulation: widget.isContinuousStimulation,
                    onParametersRead: _updateProgressFromParameters,
                  ),
                  BleDeviceStatusListener.create(
                    sessionTypeParams: widget.sessionTypeParams,
                    subuserUid: widget.subuser.uid,
                  ),
                  BleDeviceFinishListener.create(
                    deviceId: widget.deviceId,
                    deviceInteractor: widget.deviceInteractor,
                    onFinish: _handleStimulationFinish,
                    sessionTypeParams: widget.sessionTypeParams,
                    subuserUid: widget.subuser.uid,
                  ),
                ],
                child: BlocConsumer<StimulationBloc, StimulationState>(
                  builder: (ctx, state) {
                    final deviceConnectionState =
                        ctx.watch<ConnectionStateUpdate>().connectionState;

                    return deviceConnectionState == DeviceConnectionState.disconnected
                        ? SizedBox()
                        : Column(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              BlocBuilder<StimulationBloc, StimulationState>(
                                buildWhen: (previous, current) {
                                  return current is DeviceStatusReadSuccess && previous != current;
                                },
                                builder: (ctxStim, state) {
                                  final otaBloc = ctxStim.read<OtaBloc>();

                                  return otaBloc.state.status == OtaStatus.incorrectDevice
                                      ? IncorrectDeviceWidget()
                                      : Padding(
                                          padding: ctxStim.paddingMedium,
                                          child: HomeCard(
                                            child: Padding(
                                              padding: ctxStim.paddingDisplay,
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  SizedBox(
                                                    width: double.infinity,
                                                    child: state is DeviceStatusReadSuccess
                                                        ? Column(
                                                            children: [
                                                              Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment.spaceBetween,
                                                                children: [
                                                                  if (widget.sessionTypeParams
                                                                          .presetProgram !=
                                                                      null)
                                                                    StimulationControlPresetItem(
                                                                      presetProgram: widget
                                                                          .sessionTypeParams
                                                                          .presetProgram!,
                                                                    ),

                                                                  // BatterIndicatorWidget(),.
                                                                ],
                                                              ),
                                                              ControlPanel(
                                                                adjustParametersButtonKey:
                                                                    adjustParametersButtonKey,
                                                                isDisableAdjustParameters: widget
                                                                            .sessionTypeParams
                                                                            .sessionType ==
                                                                        SessionType.aiDriven ||
                                                                    widget.sessionTypeParams
                                                                            .sessionType ==
                                                                        SessionType.preSetPrograms,
                                                                isStimPaused: state.deviceStatus ==
                                                                    StimulationControlConstants
                                                                        .deviceStatusPause,
                                                                isStimStarted: state.deviceStatus !=
                                                                        StimulationControlConstants
                                                                            .deviceStatusPause &&
                                                                    state.deviceStatus !=
                                                                        StimulationControlConstants
                                                                            .deviceStatusStop,
                                                                onPressAdjustParameters: () => {
                                                                  AppRoute.pushNewScreen(
                                                                    context,
                                                                    StimulationAdvanceControlPage(
                                                                      bleDeviceInteractor:
                                                                          widget.deviceInteractor,
                                                                      deviceId: widget.deviceId,
                                                                      subuserId:
                                                                          widget.subuser.uid!,
                                                                    ),
                                                                  ),
                                                                },
                                                                onPressStart: () =>
                                                                    onPressedStart(),
                                                                onPressStop: () =>
                                                                    onPressedStop(state),
                                                                pauseButtonKey: pauseButtonKey,
                                                                startButtonKey: startButtonKey,
                                                              ),
                                                              Divider(),
                                                              BlocBuilder<StimulationBloc,
                                                                  StimulationState>(
                                                                buildWhen: (previous, current) {
                                                                  return current
                                                                          is StimulationTimeAndIntensityReadSuccess &&
                                                                      current.deviceId ==
                                                                          widget.deviceId;
                                                                },
                                                                builder: (ctxStim, state) {
                                                                  debugPrint(
                                                                    'StimulationTimeAndIntensityReadSuccess $state',
                                                                  );
                                                                  if (state
                                                                      is! StimulationTimeAndIntensityReadSuccess) {
                                                                    return MinimalLoader();
                                                                  }

                                                                  if (state is StimulationLoading) {
                                                                    return MinimalLoader();
                                                                  }

                                                                  if (state.deviceId !=
                                                                      widget.deviceId) {
                                                                    return MinimalLoader();
                                                                  }

                                                                  if (DeviceStreams(state.deviceId)
                                                                          .stimulationTimeAndIntensityStream ==
                                                                      null) {
                                                                    return MinimalLoader();
                                                                  }

                                                                  final appPermissionState = context
                                                                      .read<AppPermissionBloc>()
                                                                      .state;

                                                                  final hasCurrentIntensityPermission =
                                                                      appPermissionState
                                                                              is AppPermissionSuccess &&
                                                                          appPermissionState
                                                                              .appPermissions
                                                                              .any(
                                                                            (permission) =>
                                                                                permission.name ==
                                                                                StimulationControlConstants
                                                                                    .currentIntensityPermission,
                                                                          );
                                                                  timeLeft = state.timeLeft;

                                                                  context
                                                                      .read<TimeOfUsageBloc>()
                                                                      .add(
                                                                        GetTimeOfUsageEvent(
                                                                          scheduledDuration:
                                                                              timeLeft,
                                                                        ),
                                                                      );

                                                                  return RemainingTime(
                                                                    deviceControlPageConfig:
                                                                        deviceControlPageConfig,
                                                                    deviceId: widget.deviceId,
                                                                    hasCurrentIntensityPermission:
                                                                        hasCurrentIntensityPermission,
                                                                    key: remainingKey,
                                                                    state: state,
                                                                  );
                                                                },
                                                              ),
                                                            ],
                                                          )
                                                        : SizedBox(
                                                            height: ctx.height *
                                                                StimulationControlConstants
                                                                    .heightMultiplier,
                                                            child: Center(child: Loader()),
                                                          ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                },
                              ),
                              BlocBuilder<StimulationBloc, StimulationState>(
                                buildWhen: (previous, current) {
                                  return current is DeviceStatusReadSuccess && previous != current;
                                },
                                builder: (context, state) {
                                  return state is DeviceStatusReadSuccess
                                      ? SizedBox(
                                          height: context.height *
                                              StimulationControlConstants
                                                  .headphoneControlHeightMultiplier,
                                          child: HeadphoneControl(
                                            isLeftConnected: isHeadLConnected,
                                            isOffTime: isStimOffTime,
                                            isRightConnected: isHeadRConnected,
                                            isStimStarted: state.deviceStatus !=
                                                    StimulationControlConstants.deviceStatusStop &&
                                                state.deviceStatus !=
                                                    StimulationControlConstants.deviceStatusPause,
                                            key: headPhoneControlKey,
                                            leftEarButtonKey: leftEarIncrementButtonKey,
                                            leftEarMinusButtonKey: leftEarMinusButtonKey,
                                            leftProgress: leftProgress,
                                            onPressedLeftMinus: () => onPressedLeftMinus(),
                                            onPressedLeftPlus: () => onPressedLeftPlus(),
                                            onPressedRightMinus: () => onPressedRightMinus(),
                                            onPressedRightPlus: () => onPressedRightPlus(),
                                            rightEarButtonKey: rightEarIncrementButtonKey,
                                            rightEarMinusButtonKey: rightEarMinusButtonKey,
                                            rightProgress: rightProgress,
                                          ),
                                        )
                                      : const SizedBox(
                                          height: 300,
                                          width: double.infinity,
                                        );
                                },
                              ),
                            ],
                          );
                  },
                  listener: (ctx, state) {},
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
