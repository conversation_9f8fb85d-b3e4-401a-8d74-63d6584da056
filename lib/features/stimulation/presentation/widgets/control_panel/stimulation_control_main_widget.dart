import 'package:vagustimpro/features/stimulation/presentation/widgets/stimulation_control_exports.dart';

import '../stimulation_control_widget.dart';

class StimulationControlMainWidget extends StatelessWidget {
  const StimulationControlMainWidget({
    required this.deviceId,
    required this.deviceInteractor,
    super.key,
    required this.sessionTypeParams,
    required this.subuser,
  });

  final String deviceId;
  final BleDeviceInteractor deviceInteractor;
  final SessionTypeParams sessionTypeParams;
  final SubuserEntity subuser;

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();

    final deviceControlPageConfig = remoteConfigService.deviceControlPageConfig;

    final inheritedWidget = StimulationControlInheritedWidget.of(context);
    final controlMixin = inheritedWidget.mixin;
    final tutorialMixin = inheritedWidget.tutorialMixin;

    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        StimulationControlPanel(
          adjustParametersButtonKey: tutorialMixin.adjustParametersButtonKey,
          deviceControlPageConfig: deviceControlPageConfig,
          deviceId: deviceId,
          deviceInteractor: deviceInteractor,
          onPressedStart: controlMixin.handleOnPressedStart,
          onPressedStop: controlMixin.handleOnPressedStop,
          pauseButtonKey: tutorialMixin.pauseButtonKey,
          remainingKey: tutorialMixin.remainingKey,
          sessionTypeParams: sessionTypeParams,
          startButtonKey: tutorialMixin.startButtonKey,
          subuser: subuser,
        ),
        StimulationControlHeadphone(
          headPhoneControlKey: tutorialMixin.headPhoneControlKey,
          isHeadLConnected: controlMixin.isHeadLConnected,
          isHeadRConnected: controlMixin.isHeadRConnected,
          isStimOffTime: controlMixin.isStimOffTime,
          leftEarIncrementButtonKey: tutorialMixin.leftEarIncrementButtonKey,
          leftEarMinusButtonKey: tutorialMixin.leftEarMinusButtonKey,
          leftProgress: controlMixin.leftProgress,
          onPressedLeftMinus: controlMixin.handleOnPressedLeftMinus,
          onPressedLeftPlus: controlMixin.handleOnPressedLeftPlus,
          onPressedRightMinus: controlMixin.handleOnPressedRightMinus,
          onPressedRightPlus: controlMixin.handleOnPressedRightPlus,
          rightEarIncrementButtonKey: tutorialMixin.rightEarIncrementButtonKey,
          rightEarMinusButtonKey: tutorialMixin.rightEarMinusButtonKey,
          rightProgress: controlMixin.rightProgress,
        ),
      ],
    );
  }
}
