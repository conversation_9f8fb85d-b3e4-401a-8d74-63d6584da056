import '../stimulation_control_exports.dart';
import 'stimulation_control_main_widget.dart';

class StimulationControlContent extends StatelessWidget {
  const StimulationControlContent({
    required this.deviceId,
    required this.deviceInteractor,
    super.key,
    required this.sessionTypeParams,
    required this.subuser,
  });

  final String deviceId;
  final BleDeviceInteractor deviceInteractor;
  final SessionTypeParams sessionTypeParams;
  final SubuserEntity subuser;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StimulationBloc, StimulationState>(
      builder: (ctx, state) {
        final deviceConnectionState = ctx.watch<ConnectionStateUpdate>();

        return deviceConnectionState.deviceId == deviceId &&
                deviceConnectionState.connectionState == DeviceConnectionState.disconnected
            ? const SizedBox()
            : StimulationControlMainWidget(
                deviceId: deviceId,
                deviceInteractor: deviceInteractor,
                sessionTypeParams: sessionTypeParams,
                subuser: subuser,
              );
      },
    );
  }
}
