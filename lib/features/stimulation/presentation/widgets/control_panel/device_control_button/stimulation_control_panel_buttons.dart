import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/remote_config/device_control_page_config.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';

class StimulationControlPanelButtons extends StatelessWidget {
  const StimulationControlPanelButtons({
    this.adjustParametersButtonKey,
    this.isDisableAdjustParameters = false,
    required this.isStimPaused,
    required this.isStimStarted,
    super.key,
    required this.onPressAdjustParameters,
    required this.onPressStart,
    required this.onPressStop,
    this.pauseButtonKey,
    this.startButtonKey,
  });

  // Constants for UI dimensions and styling.
  static const _circleDiameter = 56.0;
  static const _cardElevation = 12.0;
  static const _borderWidth = 3.0;
  static const _iconSize = 28.0;
  static const _noElevation = 0.0;
  static const _noBorderWidth = 0.0;

  // Constants for text formatting.
  static const _emptyString = '';
  static const _newlineCharacter = '\n';

  final bool isStimStarted;
  final bool isStimPaused;
  final VoidCallback onPressStart;
  final VoidCallback onPressStop;
  final VoidCallback onPressAdjustParameters;
  final GlobalKey? startButtonKey;
  final GlobalKey? pauseButtonKey;
  final GlobalKey? adjustParametersButtonKey;

  final bool isDisableAdjustParameters;

  // Helper methods to reduce complexity and avoid nested conditionals.
  bool get _shouldShowActiveButtonStyle => isStimStarted && !isStimPaused;

  Color get _stopPauseButtonColor {
    if (isStimStarted && isStimPaused) {
      return AppColors.stimulationControlPauseColor;
    } else if (isStimStarted && !isStimPaused) {
      return AppColors.controlPanelPauseButtonColor;
    } else if (!isStimStarted && isStimPaused) {
      return AppColors.neoStopButtonColor;
    }

    return AppColors.stimulationControlPauseColor;
  }

  Color get _stopPauseCardColor {
    if (_shouldShowActiveButtonStyle) {
      return _stopPauseButtonColor;
    } else if (isStimPaused) {
      return AppColors.neoStopButtonColor;
    }

    return Colors.transparent;
  }

  Color get _stopPauseIconColor {
    if (_shouldShowActiveButtonStyle) {
      return AppColors.controlPanelInContainerBackgroundColor;
    } else if (isStimPaused) {
      return AppColors.controlPanelInContainerBackgroundColor;
    }

    return AppColors.stimulationControlPauseColor;
  }

  String _buildButtonText(String baseText) {
    return baseText + (isDisableAdjustParameters ? _emptyString : _newlineCharacter);
  }

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final deviceControlPageConf = remoteConfigService.deviceControlPageConfig;

    final DeviceControlPageConfig(:continueTxt, :pauseTxt, :startTxt, :stopTxt) =
        deviceControlPageConf;

    return Padding(
      padding: context.paddingLowHorizontal.copyWith(top: context.paddingLow.top),
      child: Row(
        mainAxisAlignment: isDisableAdjustParameters
            ? MainAxisAlignment.spaceAround
            : MainAxisAlignment.spaceBetween,
        children: [
          Column(
            key: pauseButtonKey,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: _noElevation,
            children: [
              GestureDetector(
                onTap: isStimStarted || isStimPaused ? onPressStop : null,
                child: SizedBox.square(
                  dimension: _circleDiameter,
                  child: Card(
                    color: _stopPauseCardColor,
                    elevation: _shouldShowActiveButtonStyle ? _cardElevation : _noElevation,
                    margin: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: AppBorderRadius.circularSize50Radius(),
                      side: BorderSide(color: _stopPauseButtonColor, width: _borderWidth),
                    ),
                    child: Icon(
                      isStimPaused ? Icons.stop : Icons.pause,
                      color: _stopPauseIconColor,
                      size: _iconSize,
                    ),
                  ),
                ),
              ),
              AppGaps.gapH4,
              Text(
                isStimPaused ? _buildButtonText(stopTxt) : _buildButtonText(pauseTxt),
                style: AppTextStyles.stimulationControlPanelPauseText.copyWith(
                  color: isStimPaused ? AppColors.neoStopButtonColor : null,
                  fontWeight: isStimPaused ? FontWeight.bold : null,
                ),
              ),
            ],
          ),
          Column(
            key: startButtonKey,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: _noElevation,
            children: [
              GestureDetector(
                onTap: _shouldShowActiveButtonStyle ? null : onPressStart,
                child: SizedBox.square(
                  dimension: _circleDiameter,
                  child: Card(
                    color: _shouldShowActiveButtonStyle
                        ? Colors.transparent
                        : AppColors.controlPanelStartButton,
                    elevation: _shouldShowActiveButtonStyle ? _noElevation : _cardElevation,
                    margin: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: AppBorderRadius.circularSize50Radius(),
                      side: BorderSide(
                        color: AppColors.stimulationControlPauseColor,
                        width: _shouldShowActiveButtonStyle ? _borderWidth : _noBorderWidth,
                      ),
                    ),
                    child: Icon(
                      Icons.play_arrow,
                      color: _shouldShowActiveButtonStyle
                          ? AppColors.stimulationControlPauseColor
                          : Colors.white,
                      size: _iconSize,
                    ),
                  ),
                ),
              ),
              AppGaps.gapH4,
              Text(
                isStimPaused ? _buildButtonText(continueTxt) : _buildButtonText(startTxt),
                style: AppTextStyles.stimulationControlPanelStartText.copyWith(
                  color:
                      _shouldShowActiveButtonStyle ? AppColors.stimulationControlPauseColor : null,
                ),
              ),
            ],
          ),
          if (!isDisableAdjustParameters)
            Column(
              key: adjustParametersButtonKey,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: 0,
              children: [
                GestureDetector(
                  onTap: onPressAdjustParameters,
                  child: SizedBox.square(
                    dimension: _circleDiameter,
                    child: Card(
                      color: AppColors.controlPanelInContainerBackgroundColor,
                      margin: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: AppBorderRadius.circularSize50Radius(),
                      ),
                      child: Padding(
                        padding: context.paddingMedium,
                        child: AssetsEnums.adjustParameters.toPng(),
                      ),
                    ),
                  ),
                ),
                AppGaps.gapH4,
                Text(
                  remoteConfigService.adjustParametersPageConfig.adjustParametersBtn,
                  style: AppTextStyles.stimulationControlPanelAdjustParameterText,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
        ],
      ),
    );
  }
}
