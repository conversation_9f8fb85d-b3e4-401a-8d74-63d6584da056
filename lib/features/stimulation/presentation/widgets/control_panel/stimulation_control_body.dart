import 'package:vagustimpro/features/stimulation/presentation/widgets/stimulation_control_exports.dart';

import '../listeners/stimulation_control_listeners.dart';
import '../stimulation_control_widget.dart';
import 'stimulation_control_content.dart';

class StimulationControlBody extends StatelessWidget {
  const StimulationControlBody({
    required this.deviceId,
    required this.deviceInteractor,
    required this.isContinuousStimulation,
    super.key,
    required this.onParametersRead,
    required this.onStimulationFinish,
    required this.sessionTypeParams,
    required this.subuser,
  });

  final String deviceId;
  final BleDeviceInteractor deviceInteractor;
  final bool isContinuousStimulation;
  final SessionTypeParams sessionTypeParams;
  final SubuserEntity subuser;
  final ParametersReadCallback onParametersRead;
  final FinishCallback onStimulationFinish;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: SafeArea(
        child: StimulationControlListeners(
          deviceId: deviceId,
          deviceInteractor: deviceInteractor,
          isContinuousStimulation: isContinuousStimulation,
          onParametersRead: onParametersRead,
          onStimulationFinish: onStimulationFinish,
          sessionTypeParams: sessionTypeParams,
          subuser: subuser,
          child: StimulationControlContent(
            deviceId: deviceId,
            deviceInteractor: deviceInteractor,
            sessionTypeParams: sessionTypeParams,
            subuser: subuser,
          ),
        ),
      ),
    );
  }
}
