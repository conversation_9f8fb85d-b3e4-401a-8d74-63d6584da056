// ignore_for_file: prefer-boolean-prefixes

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/custom_widgets/loader.dart';
import 'package:vagustimpro/core/ble/stimulation_control_constants.dart';
import 'package:vagustimpro/core/navigator/routes/app_route.dart';
import 'package:vagustimpro/features/stimulation/presentation/bloc/stimulation_bloc.dart';
import 'package:vagustimpro/features/stimulation/presentation/pages/stimulation_advance_control_page.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/control_panel/control_panel.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/preset_program/stimulation_control_preset_item.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/control_panel/incorrect_device_widget.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/home_card.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';
import 'package:vagustimpro/features/parameter/domain/params/session_type_params.dart';
import 'package:vagustimpro/features/parameter/domain/params/session_type.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_bloc.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_status.dart';
import 'package:vagustimpro/core/ble/ble_device_interactor.dart';
import 'package:vagustimpro/core/remote_config/device_control_page_config.dart';

import '../remaining_time/remaining_time_item.dart';

/// Callback type for handling device status stop events.
typedef OnPressedStopCallback = void Function(DeviceStatusReadSuccess state);

class StimulationControlMainContent extends StatelessWidget {
  const StimulationControlMainContent({
    required this.adjustParametersButtonKey,
    required this.deviceControlPageConfig,
    required this.deviceId,
    required this.deviceInteractor,
    super.key,
    required this.onPressedStart,
    required this.onPressedStop,
    required this.pauseButtonKey,
    required this.remainingKey,
    required this.sessionTypeParams,
    required this.startButtonKey,
    required this.subuser,
  });

  static Widget _buildLoadingContent(BuildContext context) {
    return SizedBox(
      height: context.height * StimulationControlConstants.heightMultiplier,
      child: const Center(child: Loader()),
    );
  }

  static

      /// Checks if device status changes should trigger rebuild.
      bool _handleShouldRebuildForDeviceStatus(
    StimulationState previous,
    StimulationState current,
  ) {
    return current is DeviceStatusReadSuccess && previous != current;
  }

  final BleDeviceInteractor deviceInteractor;
  final String deviceId;
  final SubuserEntity subuser;
  final SessionTypeParams sessionTypeParams;
  final DeviceControlPageConfig deviceControlPageConfig;
  final GlobalKey adjustParametersButtonKey;
  final GlobalKey pauseButtonKey;
  final GlobalKey startButtonKey;
  final GlobalKey remainingKey;
  final VoidCallback onPressedStart;

  final OnPressedStopCallback onPressedStop;

  Widget _buildMainContent(BuildContext context, DeviceStatusReadSuccess state) {
    return Column(
      children: [
        if (sessionTypeParams.presetProgram case final presetProgram?)
          StimulationControlPresetItem(presetProgram: presetProgram),
        ControlPanel(
          adjustParametersButtonKey: adjustParametersButtonKey,
          isDisableAdjustParameters: sessionTypeParams.sessionType == SessionType.aiDriven ||
              sessionTypeParams.sessionType == SessionType.preSetPrograms,
          isStimPaused: state.deviceStatus == StimulationControlConstants.deviceStatusPause,
          isStimStarted: state.deviceStatus != StimulationControlConstants.deviceStatusPause &&
              state.deviceStatus != StimulationControlConstants.deviceStatusStop,
          onPressAdjustParameters: () => _handleAdjustParameters(context),
          onPressStart: onPressedStart,
          onPressStop: () => onPressedStop(state),
          pauseButtonKey: pauseButtonKey,
          startButtonKey: startButtonKey,
        ),
        const Divider(),
        RemainingTimeItem(
          deviceControlPageConfig: deviceControlPageConfig,
          deviceId: deviceId,
          remainingKey: remainingKey,
        ),
      ],
    );
  }

  /// Handles navigation to advance control page.
  void _handleAdjustParameters(BuildContext context) {
    // Navigate to advance control page without awaiting.
    unawaited(AppRoute.pushNewScreen(
      context,
      StimulationAdvanceControlPage(
        bleDeviceInteractor: deviceInteractor,
        deviceId: deviceId,
        subuserId: subuser.uid ?? '',
      ),
    ));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StimulationBloc, StimulationState>(
      buildWhen: _handleShouldRebuildForDeviceStatus,
      builder: (ctxStim, state) {
        final otaBloc = ctxStim.read<OtaBloc>();

        return otaBloc.state.status == OtaStatus.incorrectDevice
            ? const IncorrectDeviceWidget()
            : Padding(
                padding: ctxStim.paddingMedium,
                child: HomeCard(
                  child: Padding(
                    padding: ctxStim.paddingDisplay,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        switch (state) {
                          DeviceStatusReadSuccess() => _buildMainContent(ctxStim, state),
                          _ => _buildLoadingContent(ctxStim),
                        },
                      ],
                    ),
                  ),
                ),
              );
      },
    );
  }
}
