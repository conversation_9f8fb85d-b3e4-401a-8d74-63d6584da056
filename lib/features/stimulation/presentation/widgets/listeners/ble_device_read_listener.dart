part of '../stimulation_control_widget.dart';

abstract final class BleDeviceReadListener {
  static BlocListener<StimulationBloc, StimulationState> create({
    required String deviceId,
    required bool isContinuousStimulation,
  }) {
    return BlocListener(
      listenWhen: (previous, current) => current is DeviceInfoReadSuccess && previous != current,
      listener: (ctx, state) {
        if ((state is DeviceInfoReadSuccess)) {
          final otaBloc = ctx.read<OtaBloc>();
          otaBloc.add(
            SaveDeviceEvent(
              bleId: deviceId,
              deviceId: state.embeddedMAC,
              firmwareVersion: state.embeddedVersion,
            ),
          );

          if (((!(otaBloc.state.isShowingDialog ?? false)) &&
              (otaBloc.state.status != OtaStatus.updateSucces) &&
              (!isContinuousStimulation || otaBloc.state.status != OtaStatus.incorrectDevice))) {
            unawaited(OtaPage(
              deviceId: deviceId,
              deviceMac: state.embeddedMAC,
              pageContext: ctx,
            ).show());
          }
        }
      },
    );
  }
}
