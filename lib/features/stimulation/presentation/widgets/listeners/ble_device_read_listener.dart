part of '../stimulation_control_widget.dart';

abstract final class BleDeviceReadListener {
  static BlocListener<StimulationBloc, StimulationState> create({
    required String deviceId,
    required bool isContinuousStimulation,
  }) {
    return BlocListener(
      listenWhen: (previous, current) => current is DeviceInfoReadSuccess && previous != current,
      listener: (ctx, state) {
        if (state is! DeviceInfoReadSuccess) return;

        final otaBloc = ctx.read<OtaBloc>();
        otaBloc.add(
          SaveDeviceEvent(
            bleId: deviceId,
            deviceId: state.embeddedMAC,
            firmwareVersion: state.embeddedVersion,
          ),
        );

        final blocState = otaBloc.state;
        final isShowingDialog = blocState.isShowingDialog ?? false;
        final updateStatus = blocState.status;

        if (isShowingDialog ||
            updateStatus == OtaStatus.updateSucces ||
            (isContinuousStimulation && updateStatus == OtaStatus.incorrectDevice)) {
          return;
        }

        unawaited(OtaPage(
          deviceId: deviceId,
          deviceMac: state.embeddedMAC,
          pageContext: ctx,
        ).show());
      },
    );
  }
}
