part of '../stimulation_control_widget.dart';

abstract final class BleDeviceConfigListener {
  static const _defaultValue = '0';
  static const _secondsToMinutes = 60;

  static BlocListener<NewParameterBloc, NewParameterState> create({
    required String deviceId,
    required BleDeviceInteractor deviceInteractor,
  }) {
    return BlocListener(
      listener: (ctx, state) {
        if (state.status != ParameterStatus.success) return;

        final bundleId =
            GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

        if (FlavorConstants.isHealthPro(bundleId)) {
          _updateMultipleDeviceStatus(deviceId, state);
        }

        _updateStimulationBloc(ctx, deviceId, deviceInteractor, state);
      },
    );
  }

  static void _updateMultipleDeviceStatus(String deviceId, NewParameterState state) {
    final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();
    final connectedDevice =
        multipleDeviceBloc.state.devices?.firstWhereOrNull((element) => element.bleId == deviceId);

    if (connectedDevice == null) return;

    final parameterEntity = state.parameterEntity;
    if (parameterEntity == null) return;

    multipleDeviceBloc.add(ChangeConnectedDeviceStatus(
      device: connectedDevice.copyWith(
        totalDuration: (parameterEntity.totalDurationTime ?? 0).toDouble(),
      ),
    ));
  }

  static void _updateStimulationBloc(
    BuildContext ctx,
    String deviceId,
    BleDeviceInteractor deviceInteractor,
    NewParameterState state,
  ) {
    final parameterEntity = state.parameterEntity;
    if (parameterEntity == null) return;

    final ParameterEntity(
      durationForOffTime: pauseDuration,
      durationForOnTime: activeDuration,
      :frequency,
      :pulseWidth,
      :totalDurationTime,
    ) = parameterEntity;

    final totalDurationInMinutes =
        ((totalDurationTime ?? 0) / _secondsToMinutes).toStringAsFixed(0);

    ctx.read<StimulationBloc>().add(
          StimulationUpdateBleDeviceConfigsEvent(
            bleDeviceInteractor: deviceInteractor,
            deviceId: deviceId,
            durationForOffTime: pauseDuration?.toString() ?? _defaultValue,
            durationForOnTime: activeDuration?.toString() ?? _defaultValue,
            frequency: frequency?.toString() ?? _defaultValue,
            pulseWidth: pulseWidth?.toString() ?? _defaultValue,
            totalDuration: totalDurationInMinutes,
          ),
        );
  }
}
