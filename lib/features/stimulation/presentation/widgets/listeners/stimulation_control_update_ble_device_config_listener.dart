part of '../stimulation_control_widget.dart';

abstract final class StimulationControlUpdateBleDeviceConfigListener {
  static BlocListener<NewParameterBloc, NewParameterState> create({
    required String deviceId,
    required BleDeviceInteractor deviceInteractor,
  }) {
    return BlocListener(
      listener: (ctx, state) {
        if (state.status == ParameterStatus.success) {
          final bundleId = GetIt.instance
              .get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName)
              .bundleId;

          if (FlavorConstants.isHealthPro(bundleId)) {
            final multipleDeviceBloc = GetIt.instance.get<MultipleDeviceBloc>();

            final device = multipleDeviceBloc.state.devices
                ?.firstWhereOrNull((element) => element.bleId == deviceId);
            if (device != null) {
              multipleDeviceBloc.add(ChangeConnectedDeviceStatus(
                device: device.copyWith(
                  totalDuration: ((state.parameterEntity?.totalDurationTime ?? 0).toDouble()),
                ),
              ));
            }
          }
          ctx.read<StimulationBloc>().add(
                StimulationUpdateBleDeviceConfigsEvent(
                  bleDeviceInteractor: deviceInteractor,
                  deviceId: deviceId,
                  durationForOffTime: state.parameterEntity?.durationForOffTime?.toString() ?? '0',
                  durationForOnTime: state.parameterEntity?.durationForOnTime?.toString() ?? '0',
                  frequency: state.parameterEntity?.frequency?.toString() ?? '0',
                  pulseWidth: state.parameterEntity?.pulseWidth?.toString() ?? '0',
                  totalDuration:
                      ((state.parameterEntity?.totalDurationTime ?? 0) / 60).toStringAsFixed(0),
                ),
              );
        }
      },
    );
  }
}
