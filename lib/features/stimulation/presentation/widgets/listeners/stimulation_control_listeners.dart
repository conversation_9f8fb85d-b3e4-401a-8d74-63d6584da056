import 'package:vagustimpro/features/stimulation/presentation/widgets/stimulation_control_exports.dart';

import '../stimulation_control_widget.dart';

class StimulationControlListeners extends StatelessWidget {
  const StimulationControlListeners({
    required this.child,
    required this.deviceId,
    required this.deviceInteractor,
    required this.isContinuousStimulation,
    super.key,
    required this.onParametersRead,
    required this.onStimulationFinish,
    required this.sessionTypeParams,
    required this.subuser,
  });

  final String deviceId;
  final BleDeviceInteractor deviceInteractor;
  final bool isContinuousStimulation;
  final SessionTypeParams sessionTypeParams;
  final SubuserEntity subuser;
  final ParametersReadCallback onParametersRead;
  final FinishCallback onStimulationFinish;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final subuserUid = subuser.uid;

    return MultiBlocListener(
      listeners: [
        BleDeviceConfigListener.create(
          deviceId: deviceId,
          deviceInteractor: deviceInteractor,
        ),
        BleDeviceReadListener.create(
          deviceId: deviceId,
          isContinuousStimulation: isContinuousStimulation,
        ),
        StimulationParameterReadListener.create(
          deviceId: deviceId,
          isContinuousStimulation: isContinuousStimulation,
          onParametersRead: onParametersRead,
        ),
        BleDeviceStatusListener.create(
          sessionTypeParams: sessionTypeParams,
          subuserUid: subuserUid,
        ),
        BleDeviceFinishListener.create(
          deviceId: deviceId,
          deviceInteractor: deviceInteractor,
          onFinish: onStimulationFinish,
          sessionTypeParams: sessionTypeParams,
          subuserUid: subuserUid,
        ),
      ],
      child: child,
    );
  }
}
