part of '../stimulation_control_widget.dart';

abstract final class BleDeviceFinishListener {
  static const _stopStatus = 'stop';

  static BlocListener<StimulationBloc, StimulationState> create({
    required String deviceId,
    required BleDeviceInteractor deviceInteractor,
    required SessionTypeParams sessionTypeParams,
    required String? subuserUid,
  }) {
    return BlocListener(
      listenWhen: (previous, current) {
        return previous is! StimFinishSuccess &&
            current is StimFinishSuccess &&
            previous != current &&
            current.deviceStatus == 'fin';
      },
      listener: (ctx, state) {
        if (state is StimFinishSuccess) {
          ctx.read<StimulationBloc>().add(
                StimulationCreateEvent(
                  commandType: 'finish',
                  sessionType: sessionTypeParams.presetProgram?.getRequestName,
                  subuserUid: subuserUid,
                ),
              );

          ctx.read<StimulationBloc>().add(
                StimulationStopEvent(
                  bleDeviceInteractor: deviceInteractor,
                  deviceId: deviceId,
                  subuserUid: subuserUid,
                ),
              );

          _handleOnTapHeartRateDialog();

          dialogService.showDialog(
            confirmButtonText: deviceControlPageConfig.stimulationCompleteAlertButton,
            message: deviceControlPageConfig.stimulationCompleteAlertMessage,
            navigatorKey: AppNavigator.navigatorKey,
            onConfirm: () {
              if (!ctx.mounted) return;

              if (FlavorConstants.isHealthPro(bundleId)) {
                final bloc = GetIt.instance.get<MultipleDeviceBloc>();

                final currentEntity = bloc.state.devices
                    ?.firstWhereOrNull((element) => element.bleId == widget.deviceId);

                if (currentEntity?.deviceStatus != DeviceStatusEnum.completed) {
                  bloc.add(ChangeConnectedDeviceStatus(
                    device: currentEntity!,
                    status: DeviceStatusEnum.completed,
                  ));
                }
              }
              final isIndividual =
                  ctx.read<AuthBloc>().currentUser?.userType == AppStrings.individualType;
              if (!isIndividual) return;
              const individuals = 'individuals';

              unawaited(AppRoute.pushNewScreen(
                ctx,
                SubuserDetailView(
                  isHistory: true,
                  subuser: widget.subuser,
                  userType: individuals,
                ),
              ));
            },
            title: deviceControlPageConfig.stimulationCompleteAlertTitle,
          );
        }
      },
    );
  }
}
