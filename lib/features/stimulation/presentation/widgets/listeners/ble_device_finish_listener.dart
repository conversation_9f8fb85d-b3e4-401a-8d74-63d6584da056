part of '../stimulation_control_widget.dart';

typedef FinishCallback = void Function(BuildContext context);

abstract final class BleDeviceFinishListener {
  static const _finishStatus = 'fin';
  static const _finishCommand = 'finish';

  static BlocListener<StimulationBloc, StimulationState> create({
    required String deviceId,
    required BleDeviceInteractor deviceInteractor,
    required FinishCallback onFinish,
    required SessionTypeParams sessionTypeParams,
    required String? subuserUid,
  }) {
    return BlocListener(
      listenWhen: (previous, current) {
        return previous is! StimFinishSuccess &&
            current is StimFinishSuccess &&
            previous != current &&
            current.deviceStatus == _finishStatus;
      },
      listener: (ctx, state) {
        if (state is! StimFinishSuccess) return;

        final stimulationBloc = ctx.read<StimulationBloc>();
        final safeSubuserUid = subuserUid ?? '';

        stimulationBloc.add(
          StimulationCreateEvent(
            commandType: _finishCommand,
            sessionType: sessionTypeParams.presetProgram?.getRequestName,
            subuserUid: safeSubuserUid,
          ),
        );

        stimulationBloc.add(
          StimulationStopEvent(
            bleDeviceInteractor: deviceInteractor,
            deviceId: deviceId,
            subuserUid: safeSubuserUid,
          ),
        );

        onFinish(ctx);
      },
    );
  }
}
