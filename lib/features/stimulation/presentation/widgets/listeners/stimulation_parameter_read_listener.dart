part of '../stimulation_control_widget.dart';

typedef ParametersReadCallback = void Function({
  required double leftIntensity,
  required double rightIntensity,
});

abstract final class StimulationParameterReadListener {
  static const _defaultIntensityValue = '0';

  static BlocListener<StimulationBloc, StimulationState> create({
    required String deviceId,
    required bool isContinuousStimulation,
    required ParametersReadCallback onParametersRead,
  }) {
    return BlocListener(
      listenWhen: (previous, current) =>
          current is StimulationParametersReadSuccess && previous != current,
      listener: (ctx, state) {
        if (state is! StimulationParametersReadSuccess) return;

        final leftIntensity = double.parse(
          state.stimulation.leftIntensity?.toString() ?? _defaultIntensityValue,
        );
        final rightIntensity = double.parse(
          state.stimulation.rightIntensity?.toString() ?? _defaultIntensityValue,
        );

        onParametersRead(
          leftIntensity: leftIntensity,
          rightIntensity: rightIntensity,
        );
      },
    );
  }
}
