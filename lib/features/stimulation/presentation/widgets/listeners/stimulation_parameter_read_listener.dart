part of '../stimulation_control_widget.dart';

abstract final class StimulationParameterReadListener {
  static BlocListener<StimulationBloc, StimulationState> create({
    required String deviceId,
    required bool isContinuousStimulation,
  }) {
    return BlocListener(
      listenWhen: (previous, current) =>
          current is StimulationParametersReadSuccess && previous != current,
      listener: (ctx, state) {
        if (state is StimulationParametersReadSuccess) {
          setState(() {
            leftProgress = double.parse(state.stimulation.leftIntensity?.toString() ?? '0');
            rightProgress = double.parse(state.stimulation.rightIntensity?.toString() ?? '0');
          });
        }
      },
    );
  }
}
