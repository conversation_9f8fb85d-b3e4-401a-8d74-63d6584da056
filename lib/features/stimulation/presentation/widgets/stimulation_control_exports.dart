// ignore_for_file: prefer-define-hero-tag

// DART CORE.
export 'dart:async';

// FLUTTER PACKAGES.
export 'package:flutter/cupertino.dart';

// THIRD PARTY PACKAGES.
export 'package:flutter_bloc/flutter_bloc.dart';
export 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
export 'package:get_it/get_it.dart';
export 'package:shared_preferences/shared_preferences.dart';
export 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

// CORE - CONSTANTS & EXTENSIONS.
export 'package:vagustimpro/core/constants/flavor_constants.dart';
export 'package:vagustimpro/core/extension/context_extension.dart';
export 'package:vagustimpro/core/extension/iterable_extension.dart';

// CORE - NAVIGATION & ROUTING.
export 'package:vagustimpro/core/navigator/app_navigator.dart';
export 'package:vagustimpro/core/navigator/routes/app_route.dart';

// CORE - BLE & DEVICE MANAGEMENT.
export 'package:vagustimpro/core/ble/ble_device_connector.dart';
export 'package:vagustimpro/core/ble/ble_device_interactor.dart';

// CORE - SERVICES & CONFIG.
export 'package:vagustimpro/core/remote_config/remote_config_service.dart';
export 'package:vagustimpro/core/services/dialog_service.dart';

// FEATURES - AUTH.
export 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';

// FEATURES - MULTIPLE DEVICE.
export 'package:vagustimpro/features/multiple_device/domain/entities/device_status_enum.dart';
export 'package:vagustimpro/features/multiple_device/presentation/bloc/multiple_device_event.dart';

// FEATURES - OTA (Over The Air Updates).
export 'package:vagustimpro/features/ota/presentation/bloc/ota_bloc.dart';
export 'package:vagustimpro/features/ota/presentation/bloc/ota_event.dart';
export 'package:vagustimpro/features/ota/presentation/bloc/ota_status.dart';

// FEATURES - PARAMETER MANAGEMENT.
export 'package:vagustimpro/features/parameter/domain/entities/parameter_entity.dart';
export 'package:vagustimpro/features/parameter/domain/params/session_type_params.dart';
export 'package:vagustimpro/features/parameter/presentation/new_bloc/new_parameter_bloc.dart';

// FEATURES - STIMULATION.
export 'package:vagustimpro/features/stimulation/presentation/bloc/stimulation_bloc.dart';
export 'package:vagustimpro/features/stimulation/presentation/widgets/headphone_control/headphone_control.dart';
export 'package:vagustimpro/features/stimulation/presentation/widgets/appbar/stimulation_control_appbar_trailing.dart';
export 'package:vagustimpro/features/stimulation/presentation/widgets/tutorial/stimulation_control_target_identify_constants.dart';
export 'package:vagustimpro/features/stimulation/presentation/widgets/tutorial/target_contents/stimulation_control_tutorial_adjust_parameters.dart';

// FEATURES - SUBUSER.
export 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';

// APP & FLAVOR CONFIG (RELATIVE PATHS).
export '../../../../app/flavor/flavor_config.dart';

// CORE - APP CONFIG & HELPERS (RELATIVE PATHS).
export '../../../../core/app_config/app_strings.dart';
export '../../../../core/ble/stimulation_control_constants.dart';
export '../../../../core/helpers/analytic_helper.dart';
export '../../../../core/remote_config/select_your_session_type_page_config.dart';

// FEATURES - HRV (RELATIVE PATHS).
export '../../../hrv/presentation/bloc/hrv_bloc.dart';
export '../../../hrv/presentation/pages/start_camera_view.dart';

// FEATURES - MULTIPLE DEVICE (RELATIVE PATHS).
export '../../../multiple_device/domain/entities/multiple_device_entity.dart';
export '../../../multiple_device/presentation/bloc/multiple_device_bloc.dart';

// FEATURES - OTA (RELATIVE PATHS).
export '../../../ota/presentation/pages/ota_page.dart';

// FEATURES - PARAMETER (RELATIVE PATHS).
export '../../../parameter/domain/params/session_type.dart';
export '../../../parameter/presentation/new_bloc/new_parameter_event.dart';
export '../../../parameter/presentation/new_bloc/new_parameter_state.dart';
export '../../../parameter/presentation/pages/widgets/select_session_type/ai_driven_result/ai_driven_result_widget.dart';

// FEATURES - SUBUSER (RELATIVE PATHS).
export '../../../subuser/presentation/pages/subuser_detail_view.dart';

// FEATURES - TIME OF USAGE (RELATIVE PATHS).
export '../../../time_of_usage/presentation/widgets/app_dialog.dart';

// LOCAL WIDGETS - INHERITED & CONTROL PANEL.
export 'inherited/stimulation_control_inherited_widget.dart';
export '../../../../core/custom_widgets/loader.dart';
export '../../../../core/remote_config/device_control_page_config.dart';
export '../../../subuser/presentation/widgets/individual/home_card.dart';
export '../pages/stimulation_advance_control_page.dart';
export 'appbar/stimulation_control_appbar.dart';
export 'control_panel/device_control_button/incorrect_device_widget.dart';
export 'control_panel/device_control_button/stimulation_control_panel_buttons.dart';
export 'control_panel/stimulation_control_body.dart';
export 'preset_program/stimulation_control_preset_item.dart';
export 'remaining_time/remaining_time_item.dart';
export 'stimulation_control_exports.dart';


// LOCAL WIDGETS - TUTORIAL COMPONENTS.
export 'tutorial/target_contents/stimulation_control_tutorial_head_phone_control.dart';
export 'tutorial/target_contents/stimulation_control_tutorial_left_ear.dart';
export 'tutorial/target_contents/stimulation_control_tutorial_pause_button.dart';
export 'tutorial/target_contents/stimulation_control_tutorial_remaining.dart';
export 'tutorial/target_contents/stimulation_control_tutorial_right_ear.dart';
export 'tutorial/target_contents/stimulation_control_tutorial_skip.dart';
export 'tutorial/target_contents/stimulation_control_tutorial_start_button.dart';
export 'tutorial/walktrough/stimulation_control_walktrough_tutorial.dart';
