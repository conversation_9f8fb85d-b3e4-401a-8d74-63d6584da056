import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../core/app_config/app_colors.dart';
import '../../../../core/app_config/app_text_styles.dart';
import '../../../../core/remote_config/device_control_page_config.dart';
import '../bloc/stimulation_bloc.dart';

class RemainingTime extends StatelessWidget {
  const RemainingTime({
    required this.deviceControlPageConfig,
    required this.deviceId,
    required this.hasCurrentIntensityPermission,
    super.key,
    required this.state,
  });

  // Constants for text strings.
  static const _loadingText = 'Loading...';
  static const _leftCurrentPrefix = 'left current: ';
  static const _rightCurrentPrefix = ' mA - right current: ';
  static const _currentSuffix = ' mA';
  static const _spacePrefix = ' ';
  static const _timeFormatSeparator = ':';

  // Constants for numeric values.
  static const _currentDivisor = 1000.0;
  static const _currentDecimalPlaces = 2;
  static const _secondsPerMinute = 60;
  static const _fontSizeSmall = 12.0;

  final DeviceControlPageConfig deviceControlPageConfig;
  final bool hasCurrentIntensityPermission;
  final StimulationTimeAndIntensityReadSuccess state;
  final String deviceId;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                '$_spacePrefix${deviceControlPageConfig.remainingTimeTxt}',
                style: AppTextStyles.stimulationControlRemainingText
                    .copyWith(fontSize: _fontSizeSmall.sp, fontWeight: FontWeight.w400),
              ),
              Text(
                state.deviceId == deviceId
                    ? '$_spacePrefix${(state.timeLeft ~/ _secondsPerMinute)}$_timeFormatSeparator${state.timeLeft % _secondsPerMinute}'
                    : _loadingText,
                style: AppTextStyles.stimulationControlRemainingText,
                textAlign: TextAlign.center,
              ),
            ],
          ),
          // Current_intensity izni varsa bu container gösterilecek.
          Align(
            alignment: Alignment.center,
            child: Container(
              decoration: BoxDecoration(
                border: Border.fromBorderSide(
                  BorderSide(color: AppColors.stimulationControlTutorialSkipButtonBackgroundColor),
                ),
                borderRadius: AppBorderRadius.circularSize8Radius(),
                color: AppColors.pureWhite,
              ),
              padding: context.paddingLow,
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text:
                          'left current: ${(double.parse(state.currentLeft) / 1000).toStringAsFixed(2)} mA - right current: ${(double.parse(state.currentRight) / 1000).toStringAsFixed(2)} mA',
                    ),
                  ],
                ),
                style: AppTextStyles.historyCalendarWeek.copyWith(fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
