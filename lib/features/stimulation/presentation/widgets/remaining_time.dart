import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../core/app_config/app_colors.dart';
import '../../../../core/app_config/app_text_styles.dart';
import '../../../../core/remote_config/device_control_page_config.dart';
import '../bloc/stimulation_bloc.dart';

class RemainingTime extends StatelessWidget {
  const RemainingTime({
    required this.deviceControlPageConfig,
    required this.deviceId,
    required this.hasCurrentIntensityPermission,
    super.key,
    required this.state,
  });

  final DeviceControlPageConfig deviceControlPageConfig;
  final bool hasCurrentIntensityPermission;
  final StimulationTimeAndIntensityReadSuccess state;
  final String deviceId;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                " ${deviceControlPageConfig.remainingTimeTxt}",
                style: AppTextStyles.stimulationControlRemainingText
                    .copyWith(fontSize: 12.sp, fontWeight: FontWeight.w400),
              ),
              Text(
                state.deviceId == deviceId
                    ? ' ${(state.timeLeft ~/ 60)}:${state.timeLeft % 60}'
                    : "Loading...",
                style: AppTextStyles.stimulationControlRemainingText,
                textAlign: TextAlign.center,
              ),
            ],
          ),
          // Current_intensity izni varsa bu container gösterilecek.
          Align(
            alignment: Alignment.center,
            child: Container(
              decoration: BoxDecoration(
                border: Border.fromBorderSide(
                  BorderSide(color: AppColors.stimulationControlTutorialSkipButtonBackgroundColor),
                ),
                borderRadius: AppBorderRadius.circularSize8Radius(),
                color: AppColors.pureWhite,
              ),
              padding: context.paddingLow,
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text:
                          'left current: ${(double.parse(state.currentLeft) / 1000).toStringAsFixed(2)} mA - right current: ${(double.parse(state.currentRight) / 1000).toStringAsFixed(2)} mA',
                    ),
                  ],
                ),
                style: AppTextStyles.historyCalendarWeek.copyWith(fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
