// ignore_for_file: no-magic-number, avoid-long-files

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/headphone_control/headphone_control_level_bar.dart';
import 'package:vagustimpro/core/custom_widgets/long_press_icon_button.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/home_card.dart';

import '../../../../../core/custom_widgets/clipper/bottom_reveal_clipper.dart';
import 'headphone_control_left_level_bar.dart';

class HeadphoneControl extends StatelessWidget {
  const HeadphoneControl({
    required this.isLeftConnected,
    required this.isOffTime,
    required this.isRightConnected,
    required this.isStimStarted,
    super.key,
    this.leftEarButtonKey,
    this.leftEarMinusButtonKey,
    required this.leftProgress,
    required this.onPressedLeftMinus,
    required this.onPressedLeftPlus,
    required this.onPressedRightMinus,
    required this.onPressedRightPlus,
    this.rightEarButtonKey,
    this.rightEarMinusButtonKey,
    required this.rightProgress,
  });
  final bool isStimStarted;
  final VoidCallback onPressedLeftMinus;
  final VoidCallback onPressedLeftPlus;
  final VoidCallback onPressedRightMinus;
  final VoidCallback onPressedRightPlus;
  final bool isLeftConnected;
  final bool isRightConnected;
  final bool isOffTime;
  final double leftProgress;
  final double rightProgress;
  final GlobalKey? leftEarButtonKey;
  final GlobalKey? rightEarButtonKey;
  final GlobalKey? leftEarMinusButtonKey;
  final GlobalKey? rightEarMinusButtonKey;

  final totalSteps = 1000;
  final size = 10;

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final deviceControlPageConf = remoteConfigService.deviceControlPageConfig;

    final progressBarImageBottomVal = 4;
    final progressBarImageHeight = context.height * 0.4;

    return Padding(
      padding: context.paddingMediumHorizontal,
      child: HomeCard(
        child: Padding(
          padding: context.paddingDisplay,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Intensity Control",
                style: AppTextStyles.stimulationControlTitle,
              ),
              AppGaps.instance.gapVS8,
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Color(0xFFE3EDF7)),
                          borderRadius: AppBorderRadius.circularSize8Radius(),
                          color: AppColors.controlPanelBackgroundColor,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const SizedBox(height: 10),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                AssetsEnums.leftEar.toSvg(),
                                AppGaps.gapW4,
                                Text(
                                  ' ${deviceControlPageConf.leftEarTxt}',
                                  style: AppTextStyles.stimulationControlEarText,
                                ),
                              ],
                            ),
                            Padding(
                              padding: context.paddingDisplayhorizontal,
                              child:
                                  const Divider(color: AppColors.headPhoneControlCardBorderColor),
                            ),
                            /*Transform.rotate(
                              angle: pi * 90 / -180,
                              child: LinearProgressIndicator(
                                backgroundColor: HexColor.fromHex('FFFFFF'),
                                borderRadius: const BorderRadius.all(Radius.circular(10)),
                                minHeight: 30,
                                value: leftProgress / 1000,
                                valueColor: const AlwaysStoppedAnimation<Color>(Colors.red),
                              ),
                            ),*/
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  FittedBox(
                                    child: HeadphoneControlLeftLevelBar(leftProgress: leftProgress),
                                  ),
                                  AppGaps.gapW8,
                                  Stack(
                                    alignment: Alignment.bottomCenter,
                                    children: [
                                      SizedBox(
                                        height: progressBarImageHeight,
                                        width: 16,
                                        child: Container(
                                          decoration: _barDecoration,
                                        ),
                                      ),
                                      Positioned(
                                        bottom: progressBarImageBottomVal.sp,
                                        top: progressBarImageBottomVal.sp,
                                        child: ClipRRect(
                                          borderRadius: AppBorderRadius.circularSize12Radius(),
                                          child: ClipPath(
                                            clipper: BottomRevealClipper(
                                              (leftProgress / 1000).clamp(0.0, 1.0),
                                            ),
                                            child: AssetsEnums.leftEarProgress.toPng(
                                              fit: BoxFit.fill,
                                              height: progressBarImageHeight,
                                              width: 10.sp,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  /* 
                                  ),*/
                                  AppGaps.gapW32,
                                ],
                              ),
                            ),
                            AppGaps.instance.gapVS16,
                            Container(
                              padding: context.paddingLow,
                              width: double.infinity,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: AppBorderRadius.circularSize8Radius(),
                                        color: isStimStarted
                                            ? AppColors.headPhoneControlIncrementButtonColor
                                            : AppColors.disableButtonBackgroundColor,
                                      ),
                                      height: 50,
                                      key: leftEarMinusButtonKey,
                                      child: LongPressIconButton(
                                        color: isStimStarted
                                            ? Color(0xFFFFFFFF)
                                            : AppColors.disableButtonIconColor,
                                        icon: Icons.remove,
                                        onPressed:
                                            isStimStarted && !isOffTime ? onPressedLeftMinus : null,
                                        size: 32,
                                      ),
                                    ),
                                  ),
                                  AppGaps.instance.gapHS16,
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: AppBorderRadius.circularSize8Radius(),
                                        color: isStimStarted
                                            ? AppColors.headPhoneControlIncrementButtonColor
                                            : AppColors.disableButtonBackgroundColor,
                                      ),
                                      height: 50,
                                      key: leftEarButtonKey,
                                      child: IconButton(
                                        color: isStimStarted
                                            ? Color(0xFFFFFFFF)
                                            : AppColors.disableButtonIconColor,
                                        icon: Icon(Icons.add, size: 32),
                                        onPressed:
                                            isStimStarted && !isOffTime ? onPressedLeftPlus : null,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      flex: 1,
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Color(0xFFE3EDF7)),
                          borderRadius: AppBorderRadius.circularSize8Radius(),
                          color: AppColors.controlPanelBackgroundColor,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            const SizedBox(height: 10),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  ' ${deviceControlPageConf.rightEarTxt}',
                                  style: AppTextStyles.stimulationControlEarText,
                                ),
                                AppGaps.gapW8,
                                AssetsEnums.rightEar.toSvg(),
                              ],
                            ),
                            Padding(
                              padding: context.paddingDisplayhorizontal,
                              child:
                                  const Divider(color: AppColors.headPhoneControlCardBorderColor),
                            ),
                            Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  AppGaps.gapW32,
                                  Stack(
                                    alignment: Alignment.bottomCenter,
                                    children: [
                                      SizedBox(
                                        height: progressBarImageHeight,
                                        width: 16,
                                        child: Container(
                                          decoration: _barDecoration,
                                        ),
                                      ),
                                      Positioned(
                                        bottom: progressBarImageBottomVal.sp,
                                        top: progressBarImageBottomVal.sp,
                                        child: ClipRRect(
                                          borderRadius: AppBorderRadius.circularSize12Radius(),
                                          child: ClipPath(
                                            clipper: BottomRevealClipper(
                                              (rightProgress / 1000).clamp(0.0, 1.0),
                                            ),
                                            child: AssetsEnums.leftEarProgress.toPng(
                                              fit: BoxFit.fill,
                                              height: context.height * 0.4,
                                              width: 10.sp,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  /* RotatedBox(
                                    quarterTurns: -1,
                                    child: Card(
                                      color: AppColors.headPhoneControlCardBackgroundColor,
                                      elevation: 8,
                                      shadowColor: Colors.black,
                                      child: Padding(
                                        padding: const EdgeInsets.all(2.0),
                                        child: StepProgressIndicator(
                                          currentStep: (rightProgress).toInt(),
                                          padding: 0,
                                          roundedEdges: Radius.circular(size),
                                          size: size,
                                          totalSteps: totalSteps,
                                          unselectedGradientColor: unSelectedGradient,
                                        ),
                                      ),
                                    ),
                                  ),*/
                                  AppGaps.gapW8,
                                  FittedBox(
                                    child: HeadphoneControlLevelBar(leftProgress: rightProgress),
                                  ),
                                ],
                              ),
                            ),
                            AppGaps.instance.gapVS16,
                            Padding(
                              padding: context.paddingLow,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: AppBorderRadius.circularSize8Radius(),
                                        color: isStimStarted
                                            ? AppColors.headPhoneControlIncrementButtonColor
                                            : AppColors.disableButtonBackgroundColor,
                                      ),
                                      height: 50,
                                      key: rightEarMinusButtonKey,
                                      child: LongPressIconButton(
                                        color: isStimStarted
                                            ? Color(0xFFFFFFFF)
                                            : AppColors.disableButtonIconColor,
                                        icon: Icons.remove,
                                        onPressed: isStimStarted && !isOffTime
                                            ? onPressedRightMinus
                                            : null,
                                        size: 32,
                                      ),
                                    ),
                                  ),
                                  AppGaps.instance.gapHS16,
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: AppBorderRadius.circularSize8Radius(),
                                        color: isStimStarted
                                            ? AppColors.headPhoneControlIncrementButtonColor
                                            : AppColors.disableButtonBackgroundColor,
                                      ),
                                      height: 50,
                                      key: rightEarButtonKey,
                                      child: IconButton(
                                        color: isStimStarted
                                            ? Color(0xFFFFFFFF)
                                            : AppColors.disableButtonIconColor,
                                        icon: Icon(Icons.add, size: 32),
                                        onPressed:
                                            isStimStarted && !isOffTime ? onPressedRightPlus : null,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  BoxDecoration get _barDecoration {
    return BoxDecoration(
      border: Border.all(color: Color(0xFFE3EDF7)),
      borderRadius: AppBorderRadius.circularSize12Radius(),
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        colors: [Color(0xFFE8F0F8), Colors.white],
        end: Alignment.bottomRight,
        stops: [0.0, 1.0],
      ),
    );
  }

  LinearGradient get unSelectedGradient => const LinearGradient(
        begin: Alignment.topLeft,
        colors: [Colors.white12, Colors.white12],
        end: Alignment.bottomRight,
      );
}
