import 'package:flutter/material.dart';

import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/ble/stimulation_control_constants.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/individual/home_card.dart';

import 'headphone_left_ear.dart';
import 'headphone_right_ear.dart';

class HeadphoneControl extends StatelessWidget {
  const HeadphoneControl({
    required this.isLeftConnected,
    required this.isOffTime,
    required this.isRightConnected,
    required this.isStimStarted,
    super.key,
    this.leftEarButtonKey,
    this.leftEarMinusButtonKey,
    required this.leftProgress,
    required this.onPressedLeftMinus,
    required this.onPressedLeftPlus,
    required this.onPressedRightMinus,
    required this.onPressedRightPlus,
    this.rightEarButtonKey,
    this.rightEarMinusButtonKey,
    required this.rightProgress,
  });
  final bool isStimStarted;
  final VoidCallback onPressedLeftMinus;
  final VoidCallback onPressedLeftPlus;
  final VoidCallback onPressedRightMinus;
  final VoidCallback onPressedRightPlus;
  final bool isLeftConnected;
  final bool isRightConnected;
  final bool isOffTime;
  final double leftProgress;
  final double rightProgress;
  final GlobalKey? leftEarButtonKey;
  final GlobalKey? rightEarButtonKey;
  final GlobalKey? leftEarMinusButtonKey;
  final GlobalKey? rightEarMinusButtonKey;

  @override
  Widget build(BuildContext context) {
    final progressBarImageBottomVal = StimulationControlConstants.progressBarImageBottomValue;
    final progressBarImageHeight =
        context.height * StimulationControlConstants.progressBarImageHeight;

    return Padding(
      padding: context.paddingMediumHorizontal,
      child: HomeCard(
        child: Padding(
          padding: context.paddingDisplay,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 0,
            children: [
              Text(
                StimulationControlConstants.intensityControlTitle,
                style: AppTextStyles.stimulationControlTitle,
              ),
              AppGaps.instance.gapVS8,
              Expanded(
                child: Row(
                  spacing: 0,
                  children: [
                    HeadPhoneLeftEar(
                      barDecoration: _barDecoration,
                      isOffTime: isOffTime,
                      isStimStarted: isStimStarted,
                      leftEarButtonKey: leftEarButtonKey,
                      leftEarMinusButtonKey: leftEarMinusButtonKey,
                      leftProgress: leftProgress,
                      onPressedLeftMinus: onPressedLeftMinus,
                      onPressedLeftPlus: onPressedLeftPlus,
                      progressBarImageBottomVal: progressBarImageBottomVal,
                      progressBarImageHeight: progressBarImageHeight,
                    ),
                    AppGaps.gapW8,
                    HeadPhoneRightEar(
                      barDecoration: _barDecoration,
                      isOffTime: isOffTime,
                      isStimStarted: isStimStarted,
                      onPressedRightMinus: onPressedRightMinus,
                      onPressedRightPlus: onPressedRightPlus,
                      progressBarImageBottomVal: progressBarImageBottomVal,
                      progressBarImageHeight: progressBarImageHeight,
                      rightEarButtonKey: rightEarButtonKey,
                      rightEarMinusButtonKey: rightEarMinusButtonKey,
                      rightProgress: rightProgress,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  BoxDecoration get _barDecoration {
    return BoxDecoration(
      border: const Border.fromBorderSide(BorderSide(color: AppColors.headPhoneControlBorderColor)),
      borderRadius: AppBorderRadius.circularSize12Radius(),
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        colors: [AppColors.headPhoneControlGradientStart, Colors.white],
        end: Alignment.bottomRight,
        stops: [0.0, 1.0],
      ),
    );
  }

  LinearGradient get unSelectedGradient => const LinearGradient(
        begin: Alignment.topLeft,
        colors: [Colors.white12, Colors.white10],
        end: Alignment.bottomRight,
      );
}
