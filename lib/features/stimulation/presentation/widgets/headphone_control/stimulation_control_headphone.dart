part of '../stimulation_control_widget.dart';

class StimulationControlHeadphone extends StatelessWidget {
  const StimulationControlHeadphone({
    this.headPhoneControlKey,
    required this.isHeadLConnected,
    required this.isHeadRConnected,
    required this.isStimOffTime,
    super.key,
    this.leftEarIncrementButtonKey,
    this.leftEarMinusButtonKey,
    required this.leftProgress,
    required this.onPressedLeftMinus,
    required this.onPressedLeftPlus,
    required this.onPressedRightMinus,
    required this.onPressedRightPlus,
    this.rightEarIncrementButtonKey,
    this.rightEarMinusButtonKey,
    required this.rightProgress,
  });
  final bool isHeadLConnected;
  final bool isHeadRConnected;
  final bool isStimOffTime;
  final double leftProgress;
  final VoidCallback onPressedLeftMinus;
  final VoidCallback onPressedLeftPlus;
  final VoidCallback onPressedRightMinus;
  final VoidCallback onPressedRightPlus;
  final double rightProgress;
  final GlobalKey? headPhoneControlKey;
  final GlobalKey? leftEarMinusButtonKey;
  final GlobalKey? rightEarMinusButtonKey;
  final GlobalKey? leftEarIncrementButtonKey;
  final GlobalKey? rightEarIncrementButtonKey;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StimulationBloc, StimulationState>(
      buildWhen: (previous, current) => current is DeviceStatusReadSuccess && previous != current,
      builder: (ctxStim, stateStim) {
        return stateStim is DeviceStatusReadSuccess
            ? SizedBox(
                height:
                    ctxStim.height * StimulationControlConstants.headphoneControlHeightMultiplier,
                child: HeadphoneControl(
                  isLeftConnected: isHeadLConnected,
                  isOffTime: isStimOffTime,
                  isRightConnected: isHeadRConnected,
                  isStimStarted:
                      stateStim.deviceStatus != StimulationControlConstants.deviceStatusStop &&
                          stateStim.deviceStatus != StimulationControlConstants.deviceStatusPause,
                  key: headPhoneControlKey,
                  leftEarButtonKey: leftEarIncrementButtonKey,
                  leftEarMinusButtonKey: leftEarMinusButtonKey,
                  leftProgress: leftProgress,
                  onPressedLeftMinus: () => onPressedLeftMinus(),
                  onPressedLeftPlus: () => onPressedLeftPlus(),
                  onPressedRightMinus: () => onPressedRightMinus(),
                  onPressedRightPlus: () => onPressedRightPlus(),
                  rightEarButtonKey: rightEarIncrementButtonKey,
                  rightEarMinusButtonKey: rightEarMinusButtonKey,
                  rightProgress: rightProgress,
                ),
              )
            : const SizedBox();
      },
    );
  }
}
