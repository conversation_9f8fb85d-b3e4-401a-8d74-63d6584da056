// ignore_for_file: avoid-unnecessary-collections
import 'package:flutter/material.dart';
import 'package:vagustimpro/core/ble/stimulation_control_constants.dart';

import '../../../../../core/app_config/app_border_radius.dart';
import '../../../../../core/app_config/app_colors.dart';
import '../../../../../core/app_config/app_text_styles.dart';

class HeadphoneControlLeftLevelBar extends StatefulWidget {
  const HeadphoneControlLeftLevelBar({super.key, required this.leftProgress});
  final double leftProgress;

  @override
  State<HeadphoneControlLeftLevelBar> createState() => _HeadphoneControlLevelBarState();
}

class _HeadphoneControlLevelBarState extends State<HeadphoneControlLeftLevelBar> {
  static bool _isCalculateIndex(int index) {
    int middle = 5;
    int high = 10;

    return index == middle || index == 0 || index == high;
  }

  final _height = 3.0;
  final _width = 24.0;
  final _textWidth = 20.0;
  final _quarterTurns = 2;
  final _length = 11;
  final _stepPerIndicator =
      StimulationControlConstants.totalSteps ~/ StimulationControlConstants.stepPerIndicator;
  final _withOpacity = 0.1;

  final _withOpacity2 = 0.2;
  final _offset = const Offset(0, -2);

  final _opacity = 1.0;

  @override
  Widget build(BuildContext context) {
    return RotatedBox(
      quarterTurns: _quarterTurns,
      child: Column(
        children: [
          for (int index = 0; index < _length; index += 1) ...[
            Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: AppBorderRadius.circularSize8Radius(),
                    boxShadow: ((index <= (widget.leftProgress ~/ _stepPerIndicator)) &&
                            widget.leftProgress > 0)
                        ? [
                            BoxShadow(
                              color: AppColors.timePickerTextColor.withValues(alpha: _withOpacity),
                              offset: _offset,
                            ),
                          ]
                        : [],
                    color: ((index <= (widget.leftProgress ~/ _stepPerIndicator)) &&
                            widget.leftProgress > 0)
                        ? Colors.white
                        : AppColors.timePickerTextColor.withValues(alpha: _withOpacity2),
                  ),
                  height: _height,
                  width: _width,
                ),
                SizedBox(
                  width: _textWidth,
                  child: RotatedBox(
                    quarterTurns: _quarterTurns,
                    child: Opacity(
                      opacity: _isCalculateIndex(index) ? _opacity : 0,
                      child: Text(
                        (index).toString(),
                        style: AppTextStyles.headPhoneLevel,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
