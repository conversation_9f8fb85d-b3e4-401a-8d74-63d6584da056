import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/ble/stimulation_control_constants.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/headphone_control/headphone_control_level_bar.dart';
import 'package:vagustimpro/core/custom_widgets/long_press_icon_button.dart';
import 'package:vagustimpro/core/custom_widgets/clipper/bottom_reveal_clipper.dart';

class HeadPhoneRightEar extends StatelessWidget {
  const HeadPhoneRightEar({
    required this.barDecoration,
    required this.isOffTime,
    required this.isStimStarted,
    super.key,
    required this.onPressedRightMinus,
    required this.onPressedRightPlus,
    required this.progressBarImageBottomVal,
    required this.progressBarImageHeight,
    this.rightEarButtonKey,
    this.rightEarMinusButtonKey,
    required this.rightProgress,
  });

  final double rightProgress;
  final bool isStimStarted;
  final bool isOffTime;
  final VoidCallback onPressedRightMinus;
  final VoidCallback onPressedRightPlus;
  final GlobalKey? rightEarMinusButtonKey;
  final GlobalKey? rightEarButtonKey;
  final double progressBarImageHeight;
  final int progressBarImageBottomVal;
  final BoxDecoration barDecoration;

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final deviceControlPageConf = remoteConfigService.deviceControlPageConfig;

    return Expanded(
      flex: 1,
      child: Container(
        decoration: _decoration,
        child: AnimatedOpacity(
          curve: Curves.easeInOut,
          duration: context.lowDuration,
          opacity: isStimStarted ? 1.0 : 0.8,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              AppGaps.gapH8,
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: 0,
                children: [
                  Text(
                    ' ${deviceControlPageConf.rightEarTxt}',
                    style: AppTextStyles.stimulationControlEarText,
                  ),
                  AppGaps.gapW8,
                  AssetsEnums.rightEar.toSvg(),
                ],
              ),
              Padding(
                padding: context.paddingDisplayhorizontal,
                child: const Divider(color: AppColors.headPhoneControlCardBorderColor),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppGaps.gapW32,
                    Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        SizedBox(
                          height: progressBarImageHeight,
                          width: StimulationControlConstants.progressBarImageWidth.toDouble(),
                          child: Container(decoration: barDecoration),
                        ),
                        Positioned(
                          bottom: progressBarImageBottomVal.sp,
                          top: progressBarImageBottomVal.sp,
                          child: ClipRRect(
                            borderRadius: AppBorderRadius.circularSize12Radius(),
                            child: ClipPath(
                              clipper: BottomRevealClipper(
                                (rightProgress / StimulationControlConstants.totalSteps)
                                    .clamp(0.0, 1.0),
                              ),
                              child: AssetsEnums.leftEarProgress.toPng(
                                fit: BoxFit.fill,
                                height: progressBarImageHeight,
                                width: StimulationControlConstants.progressBarClipperWidth.sp,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    AppGaps.gapW8,
                    FittedBox(
                      child: HeadphoneControlLevelBar(leftProgress: rightProgress),
                    ),
                  ],
                ),
              ),
              AppGaps.instance.gapVS16,
              AnimatedOpacity(
                curve: Curves.easeInOut,
                duration: context.lowDuration,
                opacity: isStimStarted ? 1.0 : 0.0,
                child: ClipRRect(
                  child: AnimatedAlign(
                    alignment: Alignment.topCenter,
                    curve: Curves.easeInOut,
                    duration: context.lowDuration,
                    heightFactor: isStimStarted ? 1.0 : 0.0,
                    child: Padding(
                      padding: context.paddingLow,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        spacing: 0,
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: AppBorderRadius.circularSize8Radius(),
                                color: isStimStarted
                                    ? AppColors.headPhoneControlIncrementButtonColor
                                    : AppColors.disableButtonBackgroundColor,
                              ),
                              height: StimulationControlConstants.buttonHeight,
                              key: rightEarMinusButtonKey,
                              child: LongPressIconButton(
                                color: isStimStarted
                                    ? AppColors.pureWhite
                                    : AppColors.disableButtonIconColor,
                                icon: Icons.remove,
                                onPressed: isStimStarted && !isOffTime ? onPressedRightMinus : null,
                                size: StimulationControlConstants.iconSize,
                              ),
                            ),
                          ),
                          AppGaps.instance.gapHS16,
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: AppBorderRadius.circularSize8Radius(),
                                color: isStimStarted
                                    ? AppColors.headPhoneControlIncrementButtonColor
                                    : AppColors.disableButtonBackgroundColor,
                              ),
                              height: StimulationControlConstants.buttonHeight,
                              key: rightEarButtonKey,
                              child: IconButton(
                                color: isStimStarted
                                    ? AppColors.pureWhite
                                    : AppColors.disableButtonIconColor,
                                icon: Icon(
                                  Icons.add,
                                  size: StimulationControlConstants.iconSize,
                                ),
                                onPressed: isStimStarted && !isOffTime ? onPressedRightPlus : null,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  BoxDecoration get _decoration {
    return BoxDecoration(
      border: const Border.fromBorderSide(BorderSide(color: AppColors.headPhoneControlBorderColor)),
      borderRadius: AppBorderRadius.circularSize8Radius(),
      color: AppColors.controlPanelBackgroundColor,
    );
  }
}
