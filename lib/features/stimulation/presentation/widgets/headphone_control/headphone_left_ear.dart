import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/ble/stimulation_control_constants.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/headphone_control/headphone_control_left_level_bar.dart';
import 'package:vagustimpro/core/custom_widgets/long_press_icon_button.dart';
import 'package:vagustimpro/core/custom_widgets/clipper/bottom_reveal_clipper.dart';

class HeadPhoneLeftEar extends StatelessWidget {
  const HeadPhoneLeftEar({
    required this.barDecoration,
    required this.isOffTime,
    required this.isStimStarted,
    super.key,
    this.leftEarButtonKey,
    this.leftEarMinusButtonKey,
    required this.leftProgress,
    required this.onPressedLeftMinus,
    required this.onPressedLeftPlus,
    required this.progressBarImageBottomVal,
    required this.progressBarImageHeight,
  });

  final double leftProgress;
  final bool isStimStarted;
  final bool isOffTime;
  final VoidCallback onPressedLeftMinus;
  final VoidCallback onPressedLeftPlus;
  final GlobalKey? leftEarMinusButtonKey;
  final GlobalKey? leftEarButtonKey;
  final double progressBarImageHeight;
  final int progressBarImageBottomVal;
  final BoxDecoration barDecoration;

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final deviceControlPageConf = remoteConfigService.deviceControlPageConfig;

    return Expanded(
      flex: StimulationControlConstants.flexValue,
      child: Container(
        decoration: _decoration,
        child: AnimatedOpacity(
          curve: Curves.easeInOut,
          duration: context.lowDuration,
          opacity: isStimStarted
              ? StimulationControlConstants.fullOpacity
              : StimulationControlConstants.reducedOpacity,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppGaps.gapH8,
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: StimulationControlConstants.spacingValue,
                children: [
                  AssetsEnums.leftEar.toSvg(),
                  AppGaps.gapW4,
                  Text(
                    '${StimulationControlConstants.earTextPrefix}${deviceControlPageConf.leftEarTxt}',
                    style: AppTextStyles.stimulationControlEarText,
                  ),
                ],
              ),
              Padding(
                padding: context.paddingDisplayhorizontal,
                child: const Divider(color: AppColors.headPhoneControlCardBorderColor),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  spacing: StimulationControlConstants.spacingValue.toDouble(),
                  children: [
                    FittedBox(
                      child: HeadphoneControlLeftLevelBar(leftProgress: leftProgress),
                    ),
                    AppGaps.gapW8,
                    Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        SizedBox(
                          height: progressBarImageHeight,
                          width: StimulationControlConstants.progressBarImageWidth.toDouble(),
                          child: Container(decoration: barDecoration),
                        ),
                        Positioned(
                          bottom: progressBarImageBottomVal.sp,
                          left: StimulationControlConstants.progressBarImageLeftRightValue.sp,
                          right: StimulationControlConstants.progressBarImageLeftRightValue.sp,
                          child: ClipRRect(
                            borderRadius: AppBorderRadius.circularSize12Radius(),
                            child: ClipPath(
                              clipper: BottomRevealClipper(
                                (leftProgress / StimulationControlConstants.totalSteps).clamp(
                                  StimulationControlConstants.hiddenOpacity,
                                  StimulationControlConstants.fullOpacity,
                                ),
                              ),
                              child: AssetsEnums.leftEarProgress.toPng(
                                fit: BoxFit.fill,
                                height: progressBarImageHeight,
                                width: StimulationControlConstants.progressBarClipperWidth.sp,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    AppGaps.gapW32,
                  ],
                ),
              ),
              AppGaps.instance.gapVS16,
              AnimatedOpacity(
                curve: Curves.easeInOut,
                duration: context.lowDuration,
                opacity: isStimStarted
                    ? StimulationControlConstants.fullOpacity
                    : StimulationControlConstants.hiddenOpacity,
                child: ClipRect(
                  child: AnimatedAlign(
                    alignment: Alignment.topCenter,
                    curve: Curves.easeInOut,
                    duration: context.lowDuration,
                    heightFactor: isStimStarted
                        ? StimulationControlConstants.fullHeightFactor
                        : StimulationControlConstants.hiddenHeightFactor,
                    child: Padding(
                      padding: context.paddingLow,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        spacing: StimulationControlConstants.spacingValue.toDouble(),
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: AppBorderRadius.circularSize8Radius(),
                                color: isStimStarted
                                    ? AppColors.headPhoneControlIncrementButtonColor
                                    : AppColors.disableButtonBackgroundColor,
                              ),
                              height: StimulationControlConstants.buttonHeight,
                              key: leftEarMinusButtonKey,
                              child: LongPressIconButton(
                                color: isStimStarted
                                    ? AppColors.pureWhite
                                    : AppColors.disableButtonIconColor,
                                icon: Icons.remove,
                                onPressed: isStimStarted && !isOffTime ? onPressedLeftMinus : null,
                                size: StimulationControlConstants.iconSize,
                              ),
                            ),
                          ),
                          AppGaps.instance.gapHS16,
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: AppBorderRadius.circularSize8Radius(),
                                color: isStimStarted
                                    ? AppColors.headPhoneControlIncrementButtonColor
                                    : AppColors.disableButtonBackgroundColor,
                              ),
                              height: StimulationControlConstants.buttonHeight,
                              key: leftEarButtonKey,
                              child: IconButton(
                                color: isStimStarted
                                    ? AppColors.pureWhite
                                    : AppColors.disableButtonIconColor,
                                icon: Icon(
                                  Icons.add,
                                  size: StimulationControlConstants.iconSize,
                                ),
                                onPressed: isStimStarted && !isOffTime ? onPressedLeftPlus : null,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  BoxDecoration get _decoration {
    return BoxDecoration(
      border: const Border.fromBorderSide(BorderSide(color: AppColors.headPhoneControlBorderColor)),
      borderRadius: AppBorderRadius.circularSize8Radius(),
      color: AppColors.controlPanelBackgroundColor,
    );
  }
}
