import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:fpdart/fpdart.dart';
import 'package:vagustimpro/core/ble/ble_constants.dart';
import 'package:vagustimpro/core/ble/ble_device_interactor.dart';
import 'package:vagustimpro/core/exception/failure.dart';
import 'package:vagustimpro/core/usecase/usecase.dart';
import 'package:vagustimpro/features/stimulation/presentation/widgets/utils/device_streams.dart';

class StimulationListenStreamsUseCase
    implements UseCase<List<Stream<List<int>>>, StimulationReadBleDeviceConfigsParams> {
  final BleDeviceInteractor bleDeviceInteractor;

  const StimulationListenStreamsUseCase(this.bleDeviceInteractor);

  @override
  Future<Either<Failure, List<Stream<List<int>>>>> calculate(
    StimulationReadBleDeviceConfigsParams params,
  ) async {
    try {
      final bleStreams = <Stream<List<int>>>[];

      // Control Service Char0 - Device Configuration.
      final mainBleStream = bleDeviceInteractor.subscribeToCharacteristic(
        QualifiedCharacteristic(
          characteristicId: BleConstants.controlChar0UUID,
          deviceId: params.deviceId,
          serviceId: BleConstants.controlServiceUUID,
        ),
      );

      bleStreams.add(mainBleStream);

      // Control Service Char1 - Device Status.
      final stimulationStatusStream = bleDeviceInteractor.subscribeToCharacteristic(
        QualifiedCharacteristic(
          characteristicId: BleConstants.controlChar1UUID,
          deviceId: params.deviceId,
          serviceId: BleConstants.controlServiceUUID,
        ),
      );

      bleStreams.add(stimulationStatusStream);

      // Status Service Char 1 - Battery Level.
      final batteryLevelStream = bleDeviceInteractor.subscribeToCharacteristic(
        QualifiedCharacteristic(
          characteristicId: BleConstants.statusChar1UUID,
          deviceId: params.deviceId,
          serviceId: BleConstants.statusServiceUUID,
        ),
      );

      bleStreams.add(batteryLevelStream);

      // Status Service Char 2 - Stimulation time and intensity stream.
      final stimulationTimeAndIntensityStream = bleDeviceInteractor.subscribeToCharacteristic(
        QualifiedCharacteristic(
          characteristicId: BleConstants.statusChar2UUID,
          deviceId: params.deviceId,
          serviceId: BleConstants.statusServiceUUID,
        ),
      );

      bleStreams.add(stimulationTimeAndIntensityStream);

      return Right(bleStreams);
    } catch (e) {
      return Left(Failure(e.toString()));
    }
  }
}

class StimulationReadBleDeviceConfigsParams {
  final String deviceId;
  const StimulationReadBleDeviceConfigsParams({required this.deviceId});
}
