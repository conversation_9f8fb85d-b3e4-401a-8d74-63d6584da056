class AiDrivenEntity {
  final String? subuserUid;
  final double? localTime;
  final String? reminderType;
  final String? reminderDate;
  final bool? isActive;
  final String? uid;
  final bool? isDeleted;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const AiDrivenEntity({
    this.createdAt,
    this.isActive,
    this.isDeleted,
    this.localTime,
    this.reminderDate,
    this.reminderType,
    this.subuserUid,
    this.uid,
    this.updatedAt,
  });
}
