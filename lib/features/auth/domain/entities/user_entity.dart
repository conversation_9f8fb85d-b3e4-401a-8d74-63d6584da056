// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';

class UserEntity {
  final String? uid;
  final String? name;
  final String? surname;
  final String? email;
  String? userType;
  final String? token;
  final bool? isNewUser;
  final SubuserEntity? defaultSubuser;
  final String? institution;
  final String? country;
  final String? title;
  final String? specialization;
  final Map<String, bool>? purposeOfUsage;
  final List<String>? otherPou;
  final Map<String, bool>? hereAboutUs;
  final String? vagustimCode;

  @override
  int get hashCode => uid.hashCode ^ (defaultSubuser?.uid.hashCode ?? 0);

  UserEntity({
    this.country,
    this.defaultSubuser,
    this.email,
    this.hereAboutUs,
    this.institution,
    this.isNewUser,
    this.name,
    this.otherPou,
    this.purposeOfUsage,
    this.specialization,
    this.surname,
    this.title,
    this.token,
    this.uid,
    this.userType,
    this.vagustimCode,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is UserEntity &&
        other.uid == uid &&
        other.defaultSubuser?.uid == defaultSubuser?.uid;
  }

  UserEntity copyWith({
    String? country,
    SubuserEntity? defaultSubuser,
    String? email,
    Map<String, bool>? hereAboutUs,
    String? institution,
    bool? isNewUser,
    String? name,
    List<String>? otherPou,
    Map<String, bool>? purposeOfUsage,
    String? specialization,
    String? surname,
    String? title,
    String? token,
    String? uid,
    String? userType,
    String? vagustimCode,
  }) {
    return UserEntity(
      country: country ?? this.country,
      defaultSubuser: defaultSubuser ?? this.defaultSubuser,
      email: email ?? this.email,
      hereAboutUs: hereAboutUs ?? this.hereAboutUs,
      institution: institution ?? this.institution,
      isNewUser: isNewUser ?? this.isNewUser,
      name: name ?? this.name,
      otherPou: otherPou ?? this.otherPou,
      purposeOfUsage: purposeOfUsage ?? this.purposeOfUsage,
      specialization: specialization ?? this.specialization,
      surname: surname ?? this.surname,
      title: title ?? this.title,
      token: token ?? this.token,
      uid: uid ?? this.uid,
      userType: userType ?? this.userType,
      vagustimCode: vagustimCode ?? this.vagustimCode,
    );
  }
}
