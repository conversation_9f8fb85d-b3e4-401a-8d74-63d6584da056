// ignore_for_file: prefer-extracting-callbacks, prefer-early-return, prefer-class-destructuring, prefer-moving-to-variable

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_strings.dart';
import 'package:vagustimpro/core/custom_widgets/loader.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/pages/auth_strategies/select_role_strategy.dart';
import 'package:vagustimpro/features/auth/presentation/pages/auth_strategies/individuals_home_strategy.dart';
import 'package:vagustimpro/features/auth/presentation/pages/auth_strategies/healt_prof_home_strategy.dart';
import 'package:vagustimpro/features/auth/presentation/pages/auth_strategies/user_state_context.dart';
import 'package:vagustimpro/features/auth/presentation/pages/login/login_page.dart';
import 'package:vagustimpro/features/auth/presentation/pages/profile/hrv/other/hrv_profile_view.dart';
import 'package:vagustimpro/features/landing/presentation/bloc/landing_bloc.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';
import '../../../../../app/flavor/flavor_config.dart';
import '../../../../../core/constants/flavor_constants.dart';
import '../../../../subuser/presentation/widgets/subuser_form/individual/individual_profile_view.dart';
import '../profile/health_professional/professional_profile_view.dart';
import 'redirecting_to_another_app_dialog.dart';

class CustomAuthWidget extends StatelessWidget {
  const CustomAuthWidget({super.key});

  static CupertinoPageRoute<CustomAuthWidget> get route =>
      CupertinoPageRoute(builder: (context) => const CustomAuthWidget());

  static UserStateContext _getUserStateContext(GetUserSuccess state) {
    if ((state.user.userType == AppStrings.healthProfessionalType ||
        state.user.userType == AppStrings.healthPractition)) {
      return const UserStateContext(HealthProfUsersStrategy());
    } else if (state.user.userType == AppStrings.individualType &&
        state.user.defaultSubuser != null) {
      return const UserStateContext(IndividualsHomeStrategy());
    }

    return const UserStateContext(SelectRoleStrategy());
  }

  static void _redirectingDialogThen(BuildContext ctx) {
    if (ctx.mounted) {
      ctx.read<AuthBloc>().add(SignOutEvent());
    }
  }

  static Widget _handleFirstAppLaunch(String? name, SubuserEntity? subuser) {
    final bundleId =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

    if (FlavorConstants.isHealthPro(bundleId) && (name ?? "").isEmpty) {
      return const ProfessionalProfileView(isEditMode: false);
    } else if (FlavorConstants.isIndividual(bundleId) && subuser == null) {
      return const IndividualProfileView(isEditMode: false);
    } else if (FlavorConstants.isHrv(bundleId) && subuser == null) {
      return HrvProfileView();
    }

    return const IndividualProfileView(isEditMode: false);
  }

  void _showRedirectingAnotherAppDialog(BuildContext ctx, RedirectingToAnotherAppState state) {
    WidgetsBinding.instance.addPostFrameCallback((_) => _handlePostFrameCallback(ctx, state));
  }

  void _handlePostFrameCallback(BuildContext ctx, RedirectingToAnotherAppState state) async {
    final response =
        await RedirectingToAnotherAppDialog(context: ctx, userType: state.user.userType).show;

    if (response == null && ctx.mounted) _redirectingDialogThen(ctx);
  }

  @override
  Widget build(BuildContext context) {
    bool isShowAnotherAppDialog = false;

    return BlocConsumer<AuthBloc, AuthState>(
      buildWhen: (previous, current) => previous.runtimeType != current.runtimeType,
      builder: (ctx, state) {
        debugPrint("Building with state: $state");

        switch (state) {
          case GetUserSuccess getUserSuccessState:
            if ((getUserSuccessState.user.userType ?? "").isEmpty ||

                // User type is health professional and name is empty.
                (getUserSuccessState.user.userType == AppStrings.healthProfessionalType &&
                    (getUserSuccessState.user.name ?? "").isEmpty) ||

                // User type is individual and default subuser is null.
                (getUserSuccessState.user.userType == AppStrings.individualType &&
                    getUserSuccessState.user.defaultSubuser == null)) {
              return _handleFirstAppLaunch(
                getUserSuccessState.user.name,
                state.user.defaultSubuser,
              );
            }

            final contextt = _getUserStateContext(getUserSuccessState);

            return contextt.build(getUserSuccessState);

          case RedirectingToAnotherAppState anotherAppState:
            if ((anotherAppState.user.userType ?? "").isEmpty) {
              return _handleFirstAppLaunch(anotherAppState.user.name, state.user.defaultSubuser);
            }
            if (!isShowAnotherAppDialog) {
              isShowAnotherAppDialog = true;
              _showRedirectingAnotherAppDialog(ctx, anotherAppState);
            }

            return const LoginPage();

          case AuthFailure authFailure:
            return LoginPage(authFailure: authFailure);

          default:
            return const Scaffold(body: SafeArea(child: Loader()));
        }
      },
      listenWhen: (previous, current) => previous is AuthLoading && current is GetUserSuccess,
      listener: (ctx, state) {
        if (state is GetUserSuccess) {
          // If user type is individual and default subuser is not null, show bottom bar.
          if (state.user.userType == AppStrings.individualType &&
              state.user.defaultSubuser != null) {
            ctx.read<LandingBloc>().add(ChangeBottomBarVisibility(isShowBottomBar: true));
          }

          // If user type is health professional and name is not empty, show bottom bar.
          if ((state.user.userType == AppStrings.healthProfessionalType ||
                  state.user.userType == AppStrings.healthPractition) &&
              (state.user.name ?? "").isNotEmpty) {
            ctx.read<LandingBloc>().add(ChangeBottomBarVisibility(isShowBottomBar: true));
          }
        }
      },
    );
  }
}
