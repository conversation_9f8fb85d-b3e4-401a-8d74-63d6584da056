// ignore_for_file: avoid-passing-async-when-sync-expected, prefer-extracting-callbacks, avoid-non-null-assertion, use-existing-variable, prefer-switch-with-sealed-classes

import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get_it/get_it.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shake_flutter/shake_flutter.dart';
import 'package:vagustimpro/core/app_config/app_snackbar.dart';
import 'package:vagustimpro/core/navigator/app_navigator.dart';

import 'package:vagustimpro/core/custom_widgets/loader.dart';
import 'package:vagustimpro/core/remote_config/login_page_config.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';
import 'package:vagustimpro/core/services/dialog_service.dart';
import 'package:vagustimpro/features/app_permission/presentation/bloc/app_permission_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/pages/login/login_content.dart';
import 'package:vagustimpro/features/auth/presentation/pages/profile/health_professional/professional_profile_view.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';

import '../../../../../app/flavor/flavor_config.dart';
import '../../../../../core/app_config/app_strings.dart';
import '../../../../../core/constants/flavor_constants.dart';
import '../../../../../core/navigator/routes/constants/route_constants.dart';
import '../../../../landing/presentation/bloc/landing_bloc.dart';
import '../../../../subuser/presentation/widgets/subuser_form/individual/individual_profile_view.dart';
import '../profile/hrv/other/hrv_profile_view.dart';
import 'redirecting_to_another_app_dialog.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({this.authFailure, this.isEnableBackButton = false, super.key});
  final bool isEnableBackButton;
  final AuthFailure? authFailure;

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  bool _isGetUserSucces = false;

  static void _redirectingDialogThen(BuildContext ctx) {
    if (ctx.mounted) {
      ctx.read<AuthBloc>().add(SignOutEvent());
    }
  }

  final _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    FlutterNativeSplash.remove();

    Future.microtask(() {
      if (!mounted) return;
      final authFailure = widget.authFailure;
      if (authFailure != null) {
        AppSnackbar.showErrorSnackbar(context, message: authFailure.message);
      }
    });
  }

  void _handleFirstAppLaunch(BuildContext context, String? name, SubuserEntity? subuser) {
    final bundleId =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

    if (FlavorConstants.isHealthPro(bundleId) && ((name ?? "").isEmpty)) {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        CupertinoPageRoute(builder: (ctx) => ProfessionalProfileView(isEditMode: false)),
        (route) => false,
      ));
    } else if (FlavorConstants.isIndividual(bundleId) && subuser == null) {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        CupertinoPageRoute(
          builder: (ctx) => IndividualProfileView(isEditMode: false),
        ),
        (route) => false,
      ));
    } else if (FlavorConstants.isHrv(bundleId) && subuser == null) {
      unawaited(
        Navigator.of(context).pushAndRemoveUntil(
          CupertinoPageRoute(builder: (ctx) => HrvProfileView()),
          (route) => false,
        ),
      );
    } else {
      context.read<LandingBloc>().add(ChangeBottomBarVisibility(isShowBottomBar: true));

      unawaited(
        Navigator.of(context).pushNamedAndRemoveUntil(RouteConstants.landingPage, (route) => false),
      );
    }

    _isGetUserSucces = true;
  }

  void _showRedirectingAnotherAppDialog(BuildContext ctx, RedirectingToAnotherAppState state) {
    WidgetsBinding.instance.addPostFrameCallback((_) => _handlePostFrameCallback(ctx, state));
  }

  void _handlePostFrameCallback(BuildContext ctx, RedirectingToAnotherAppState state) async {
    final response =
        await RedirectingToAnotherAppDialog(context: ctx, userType: state.user.userType).show;

    if (response == null && ctx.mounted) _redirectingDialogThen(ctx);
  }

  Future<void> _handleEmailSignIn() async {
    if (_emailController.text.isNotEmpty) {
      context.read<AuthBloc>().add(SignInWithEmailEvent(email: _emailController.text));
    } else {
      final dialogService = GetIt.instance.get<DialogService>();

      final remoteConfigService = GetIt.instance.get<RemoteConfigService>();
      final loginPageConfig = remoteConfigService.loginPageConfig;
      final LoginPageConfig(
        :enterYourEmailAlertConfirmBtnTxt,
        :enterYourEmailAlertMessage,
        :enterYourEmailAlertTitle,
      ) = loginPageConfig;

      await dialogService.showDialog(
        confirmButtonText: enterYourEmailAlertConfirmBtnTxt,
        message: enterYourEmailAlertMessage,
        navigatorKey: AppNavigator.navigatorKey,
        title: enterYourEmailAlertTitle,
      );
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final width = size.width;
    final height = size.height;
    final dialogService = GetIt.instance.get<DialogService>();

    final remoteConfigService = GetIt.instance.get<RemoteConfigService>();
    final loginPageConfig = remoteConfigService.loginPageConfig;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: widget.isEnableBackButton ? CupertinoNavigationBar() : null,
        body: SafeArea(
          child: BlocConsumer<AuthBloc, AuthState>(
            builder: (_, state) {
              debugPrint("Building with state login page : $state");

              return state is AuthLoading || state is AuthSuccess
                  ? const Loader()
                  : LoginContent(
                      emailController: _emailController,
                      height: height,
                      onHandleEmailSignIn: _handleEmailSignIn,
                      width: width,
                    );
            },
            listener: (ctx, state) async {
              final currentState = AppNavigator.navigatorKey.currentState;
              if (currentState == null) return;

              final overlay = currentState.overlay;

              if (overlay?.context != null) {
                final currentRoute = ModalRoute.of(currentState.overlay!.context)?.settings.name;

                if (currentRoute == RouteConstants.otpValidatePage) {
                  return;
                }
              }
              if (state is AuthFailure) {
                await dialogService.showDialog(
                  confirmButtonText: loginPageConfig.authFailureAlertConfirmBtnTxt,
                  message: state.message,
                  navigatorKey: AppNavigator.navigatorKey,
                  title: loginPageConfig.authFailureAlertTitle,
                );
              } else if (state is EmailSentSuccess) {
                unawaited(Navigator.of(ctx).pushNamed(RouteConstants.otpValidatePage));
              } else if (state is AuthSuccess) {
                ctx.read<AuthBloc>().add(GetUserEvent());
                ctx.read<AppPermissionBloc>().add(GetAppPermissionsEvent());

                // Context.read<SettingsBloc>().add(GetRatingEvent());.

                final user = FirebaseAuth.instance.currentUser;

                if (user != null) {
                  unawaited(Shake.registerUser(user.uid));
                  unawaited(Purchases.logIn(user.uid));
                  unawaited(OneSignal.login(user.uid));
                }
              } else if (state is GetUserSuccess) {
                final bundleId = GetIt.instance
                    .get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName)
                    .bundleId;

                if (FlavorConstants.isHrv(bundleId)) {
                  ctx.read<LandingBloc>().add(ChangeBottomBarVisibility(isShowBottomBar: true));
                }

                _handleFirstAppLaunch(ctx, state.user.name, state.user.defaultSubuser);
              } else if (state is RedirectingToAnotherAppState) {
                _showRedirectingAnotherAppDialog(ctx, state);
              } else if (state is AuthUserCredentialsSuccess) {
                _emailController.text = state.email;
              }
            },
          ),
        ),
        resizeToAvoidBottomInset: false,
      ),
    );
  }
}
