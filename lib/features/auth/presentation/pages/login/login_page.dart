import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get_it/get_it.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shake_flutter/shake_flutter.dart';
import 'package:vagustimpro/core/app_config/app_snackbar.dart';
import 'package:vagustimpro/core/navigator/app_navigator.dart';

import 'package:vagustimpro/core/custom_widgets/loader.dart';
import 'package:vagustimpro/core/remote_config/login_page_config.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';
import 'package:vagustimpro/core/services/dialog_service.dart';
import 'package:vagustimpro/features/app_permission/presentation/bloc/app_permission_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/pages/login/login_content.dart';
import 'package:vagustimpro/features/auth/domain/entities/user_entity.dart';
import 'package:vagustimpro/features/auth/presentation/pages/profile/health_professional/professional_profile_view.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';

import '../../../../../app/flavor/flavor_config.dart';
import '../../../../../core/app_config/app_strings.dart';
import '../../../../../core/constants/flavor_constants.dart';
import '../../../../../core/navigator/routes/constants/route_constants.dart';
import '../../../../landing/presentation/bloc/landing_bloc.dart';
import '../../../../subuser/presentation/widgets/subuser_form/individual/individual_profile_view.dart';
import '../profile/hrv/other/hrv_profile_view.dart';
import 'redirecting_to_another_app_dialog.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({this.authFailure, this.isEnableBackButton = false, super.key});
  final bool isEnableBackButton;
  final AuthFailure? authFailure;

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  static void _redirectingDialogThen(BuildContext ctx) {
    if (ctx.mounted) {
      ctx.read<AuthBloc>().add(SignOutEvent());
    }
  }

  static void _handleFirstAppLaunch(BuildContext context, String? name, SubuserEntity? subuser) {
    final bundleId =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

    if (FlavorConstants.isHealthPro(bundleId) && ((name ?? "").isEmpty)) {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        CupertinoPageRoute(builder: (ctx) => ProfessionalProfileView(isEditMode: false)),
        (route) => false,
      ));
    } else if (FlavorConstants.isIndividual(bundleId) && subuser == null) {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        CupertinoPageRoute(
          builder: (ctx) => IndividualProfileView(isEditMode: false),
        ),
        (route) => false,
      ));
    } else if (FlavorConstants.isHrv(bundleId) && subuser == null) {
      unawaited(
        Navigator.of(context).pushAndRemoveUntil(
          CupertinoPageRoute(builder: (ctx) => HrvProfileView()),
          (route) => false,
        ),
      );
    } else {
      context.read<LandingBloc>().add(ChangeBottomBarVisibility(isShowBottomBar: true));

      unawaited(
        Navigator.of(context).pushNamedAndRemoveUntil(RouteConstants.landingPage, (route) => false),
      );
    }
  }

  final _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    FlutterNativeSplash.remove();

    Future.microtask(() => _handleShowErrorSnackBar());
  }

  void _handleShowErrorSnackBar() {
    if (!mounted) return;

    final authFailure = widget.authFailure;
    if (authFailure != null) {
      AppSnackbar.showErrorSnackbar(context, message: authFailure.message);
    }
  }

  void _showRedirectingAnotherAppDialog(BuildContext ctx, RedirectingToAnotherAppState state) {
    WidgetsBinding.instance.addPostFrameCallback((_) => _handlePostFrameCallback(ctx, state));
  }

  void _handlePostFrameCallback(BuildContext ctx, RedirectingToAnotherAppState state) async {
    final response =
        await RedirectingToAnotherAppDialog(context: ctx, userType: state.user.userType).show;

    if (response == null && ctx.mounted) _redirectingDialogThen(ctx);
  }

  /// Handles different auth states in the listener.
  Future<void> _handleAuthState(BuildContext ctx, AuthState state) async {
    switch (state) {
      case AuthFailure(:final message):
        final dialogService = GetIt.instance.get<DialogService>();
        final remoteConfigService = GetIt.instance.get<RemoteConfigService>();
        final loginPageConfig = remoteConfigService.loginPageConfig;

        // Destructure loginPageConfig to avoid repeated property access.
        final LoginPageConfig(
          :authFailureAlertConfirmBtnTxt,
          :authFailureAlertTitle,
        ) = loginPageConfig;

        await dialogService.showDialog(
          confirmButtonText: authFailureAlertConfirmBtnTxt,
          message: message,
          navigatorKey: AppNavigator.navigatorKey,
          title: authFailureAlertTitle,
        );

      case EmailSentSuccess():
        unawaited(Navigator.of(ctx).pushNamed(RouteConstants.otpValidatePage));

      case AuthSuccess():
        ctx.read<AuthBloc>().add(GetUserEvent());
        ctx.read<AppPermissionBloc>().add(GetAppPermissionsEvent());

        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          unawaited(Shake.registerUser(user.uid));
          unawaited(Purchases.logIn(user.uid));
          unawaited(OneSignal.login(user.uid));
        }

      case GetUserSuccess():
        final bundleId =
            GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

        if (FlavorConstants.isHrv(bundleId)) {
          ctx.read<LandingBloc>().add(ChangeBottomBarVisibility(isShowBottomBar: true));
        }

        // Destructure user properties to avoid repeated access.
        final UserEntity(:defaultSubuser, :name) = state.user;

        _handleFirstAppLaunch(ctx, name, defaultSubuser);

      case RedirectingToAnotherAppState():
        _showRedirectingAnotherAppDialog(ctx, state);

      case AuthUserCredentialsSuccess(:final email):
        _emailController.text = email;

      case AuthInitial() ||
            AuthLoading() ||
            AuthUserCredentialsFailure() ||
            UpdateUserSuccess() ||
            ValidateOtpSuccess() ||
            ValidateOtpFailure() ||
            ProfileButtonState():
        // No action needed for these states.
        break;
    }
  }

  void _handleEmailSignIn() async {
    if (_emailController.text.isNotEmpty) {
      context.read<AuthBloc>().add(SignInWithEmailEvent(email: _emailController.text));
    } else {
      final dialogService = GetIt.instance.get<DialogService>();

      final remoteConfigService = GetIt.instance.get<RemoteConfigService>();
      final loginPageConfig = remoteConfigService.loginPageConfig;
      final LoginPageConfig(
        :enterYourEmailAlertConfirmBtnTxt,
        :enterYourEmailAlertMessage,
        :enterYourEmailAlertTitle,
      ) = loginPageConfig;

      await dialogService.showDialog(
        confirmButtonText: enterYourEmailAlertConfirmBtnTxt,
        message: enterYourEmailAlertMessage,
        navigatorKey: AppNavigator.navigatorKey,
        title: enterYourEmailAlertTitle,
      );
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final width = size.width;
    final height = size.height;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: widget.isEnableBackButton ? CupertinoNavigationBar() : null,
        body: SafeArea(
          child: BlocConsumer<AuthBloc, AuthState>(
            builder: (_, state) {
              debugPrint("Building with state login page : $state");

              return switch (state) {
                AuthLoading() || AuthSuccess() => const Loader(),
                _ => LoginContent(
                    emailController: _emailController,
                    height: height,
                    onHandleEmailSignIn: _handleEmailSignIn,
                    width: width,
                  ),
              };
            },
            listener: (ctx, state) async {
              final currentState = AppNavigator.navigatorKey.currentState;
              if (currentState == null) return;

              final overlay = currentState.overlay;

              if (overlay?.context != null) {
                final currentRoute = ModalRoute.of(currentState.overlay!.context)?.settings.name;

                if (currentRoute == RouteConstants.otpValidatePage) {
                  return;
                }
              }
              await _handleAuthState(ctx, state);
            },
          ),
        ),
        resizeToAvoidBottomInset: false,
      ),
    );
  }
}
