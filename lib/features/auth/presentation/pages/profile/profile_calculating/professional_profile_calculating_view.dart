// ignore_for_file: prefer-extracting-function-callbacks, avoid-returning-widgets, no-empty-block

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_snackbar.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/navigator/routes/app_route.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/pages/login/login_page.dart';
import 'package:vagustimpro/features/auth/presentation/pages/profile/profile_calculating/profile_calculating_widget.dart';

class ProfessionalProfileCalculatingView extends StatefulWidget {
  const ProfessionalProfileCalculatingView({required this.isEditMode, super.key});
  final bool isEditMode;

  @override
  State<ProfessionalProfileCalculatingView> createState() =>
      _ProfessionalProfileCalculatingViewState();
}

class _ProfessionalProfileCalculatingViewState extends State<ProfessionalProfileCalculatingView> {
  static const _kIconSize = 24;

  static const _kMinimumLoadingDuration = Duration(seconds: 15);

  bool _isLoading = false;
  bool _isSuccessReceived = false;

  Timer? _loadingTimer;
  Widget get _handleBuildContent {
    final defaultPaddingValue = 16;

    return Padding(
      padding: EdgeInsets.only(
        bottom: widget.isEditMode
            ? 0
            : MediaQuery.maybePaddingOf(context)?.bottom ?? defaultPaddingValue.sp,
        left: 0,
        top: kMinInteractiveDimension.sp,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.isEditMode
              ? SizedBox.shrink()
              : GestureDetector(
                  onTap: _handleBackNavigation,
                  child: Icon(Icons.arrow_back_ios_new, size: _kIconSize.sp),
                ),
          Expanded(child: ProfileCalculatingWidget(isEditMode: widget.isEditMode)),
        ],
      ),
    );
  }

  static void _navigateAfterSuccess() {
    /* if (widget.subuser == null) {
      unawaited(
        Navigator.of(context).pushNamedAndRemoveUntil(RouteConstants.landingPage, (route) => false),
      );
    }
    AppRoute.popUntilFirstScreen(context);*/
  }

  final _pageController = PageController();

  void _handleLoadingState() {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _isSuccessReceived = false;
    });

    _loadingTimer = Timer(_kMinimumLoadingDuration, () {
      if (!mounted) return;

      setState(() => _isLoading = false);
      if (_isSuccessReceived) _navigateAfterSuccess();
    });
  }

  void _handleFailureState(String errorMessage) {
    if (widget.isEditMode) {
      AppRoute.popUntilFirstScreen(context);
    } else {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false,
      ));
    }
    AppSnackbar.showErrorSnackbar(context, errorMessage);
  }

  void _handleUpdateSuccessState() {
    if (!mounted) return;

    setState(() {
      _isSuccessReceived = true;
    });

    if (!_isLoading && mounted) {
      _navigateAfterSuccess();
    }
  }

  void _handleBlocListener(BuildContext context, AuthState state) {
    _handleLoadingState();

    switch (state) {
      case AuthFailure():
        _handleFailureState(state.message);
        break;

      case UpdateUserSuccess():
        _handleUpdateSuccessState();
        break;

      default:
        break;
    }
  }

  // Extracted callback to a separate method.
  void _handleBackNavigation() {
    if (_selectedIndex == 0) {
      if (!widget.isEditMode) {
        unawaited(Navigator.of(context).pushAndRemoveUntil(
          CupertinoPageRoute(builder: (context) => const LoginPage()),
          (route) => false,
        ));

        return;
      }

      AppRoute.pop(context);
    } else {
      if (!mounted) return;

      setState(() => _selectedIndex -= 1);
      unawaited(_pageController.animateToPage(
        _selectedIndex,
        curve: Curves.easeIn,
        duration: context.lowDuration,
      ));
    }
  }

  int _selectedIndex = 0;

  @override
  void dispose() {
    _pageController.dispose();
    _loadingTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: context.paddingMedium,
        child: BlocConsumer<AuthBloc, AuthState>(
          builder: (ctx, state) => _handleBuildContent,
          listener: _handleBlocListener,
        ),
      ),
    );
  }
}
