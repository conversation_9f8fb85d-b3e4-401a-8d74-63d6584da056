// ignore_for_file: avoid-non-ascii-symbols, no-magic-string

import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:provider/provider.dart';
import 'package:vagustimpro/core/enum/assets_enums_lottie.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/landing/presentation/bloc/landing_bloc.dart';

import '../../../../../../core/app_config/app_colors.dart';
import '../../../../../../core/app_config/app_gaps.dart';
import '../../../../../../core/app_config/app_text_styles.dart';
import '../../../../../../core/custom_widgets/common_button.dart';
import '../../../../../../core/navigator/routes/app_route.dart';
import '../../../../../../core/navigator/routes/constants/route_constants.dart';
import '../../../../../../core/remote_config/remote_config_service.dart';
import 'profile_calculating_content.dart';

class ProfileCalculatingDoneView extends StatefulWidget {
  const ProfileCalculatingDoneView({required this.isEditMode, super.key});
  final bool isEditMode;

  @override
  State<ProfileCalculatingDoneView> createState() => _ProfileCalculatingDoneViewState();
}

class _ProfileCalculatingDoneViewState extends State<ProfileCalculatingDoneView> {
  static final _profilePageConfig = GetIt.instance.get<RemoteConfigService>().profilePageConfig;
  final _currentContent = ProfileCalculatingContent(
    description:
        "Your personalized session plan is ready. Start now and experience the benefits—our team is here to support you every step of the way.",
    image: _profilePageConfig.profileCalculatingImage4,
    title: "Begin Your Journey",
  );

  final _imageHeight = 0.3;
  final _height = 0.9;
  final _checkHeight = 0.1;

  void _handleOnTap() {
    if (widget.isEditMode) {
      AppRoute.popUntilFirstScreen(context);
      Navigator.of(context).popUntil((route) => route.isFirst);
    } else {
      final landingBloc = context.read<LandingBloc>();

      final isShowBottomBar = landingBloc.state.isShowBottomBar ?? false;

      if (!isShowBottomBar) {
        landingBloc.add(ChangeBottomBarVisibility(isShowBottomBar: true));
      }
      unawaited(
        Navigator.of(context).pushNamedAndRemoveUntil(RouteConstants.landingPage, (route) => false),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final image = (_currentContent.image ?? "");

    return SizedBox(
      height: context.height * (widget.isEditMode ? _height : 1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        spacing: 0,
        children: [
          Column(
            children: [
              if (image.isNotEmpty)
                SizedBox(
                  height: context.height * _imageHeight,
                  child: Image.network(
                    image,
                    loadingBuilder: (
                      BuildContext ctx,
                      Widget child,
                      ImageChunkEvent? loadingProgress,
                    ) {
                      if (loadingProgress == null) return child;

                      return Center(
                        child: CircularProgressIndicator(
                          color: AppColors.neoBlue,
                          value: loadingProgress.expectedTotalBytes == null
                              ? null
                              : loadingProgress.cumulativeBytesLoaded /
                                  (loadingProgress.expectedTotalBytes ?? 0),
                        ),
                      );
                    },
                    semanticLabel: UniqueKey().toString(),
                  ),
                ),
              AppGaps.instance.gapVS8,
              Text(
                _currentContent.title,
                key: ValueKey(_currentContent.title),
                style: AppTextStyles.comprehensiveLoadingDescription
                    .copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              Text(
                _currentContent.description,
                key: ValueKey(_currentContent.description),
                style: AppTextStyles.comprehensiveLoadingDescription,
                textAlign: TextAlign.center,
              ),
            ],
          ),
          AssetsEnumsLottie.checkAnimation.toLottie(
            height: context.height * _checkHeight,
            isRepeat: false,
          ),
          Padding(
            padding: context.paddingLow.copyWith(bottom: context.paddingMedium.bottom),
            child: CommonButton(
              buttonColor: AppColors.neoBlue,
              onPress: _handleOnTap,
              text: "Start Your Journey",
            ),
          ),
        ],
      ),
    );
  }
}
