part of 'professional_profile_form.dart';

mixin ProfessionalProfileFormMixin on State<ProfessionalProfileForm> {
  final _nameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _ageController = TextEditingController();

  final _institutionNameController = TextEditingController();
  final _specializationController = TextEditingController();
  final _countryController = TextEditingController();

  Map<String, bool> _purposeOfUsageValues = <String, bool>{};
  Map<String, bool> _professionalTitleValues = <String, bool>{};
  List<String> _otherTags = [];
  Map<String, bool> _hearAboutUsValues = <String, bool>{};

  ProfileUpdateEvent? _profileUpdateEvent;

  @override
  void initState() {
    super.initState();
    final user = widget.user;

    _nameController.text = user?.name ?? "";
    _lastNameController.text = user?.surname ?? "";
    _purposeOfUsageValues = user?.purposeOfUsage ?? {};
    _otherTags = user?.otherPou ?? [];
    _institutionNameController.text = user?.institution ?? "";
    _specializationController.text = user?.specialization ?? "";
    _countryController.text = user?.country ?? "";
    _purposeOfUsageValues = user?.purposeOfUsage ?? {};
    _professionalTitleValues = {user?.title ?? "": true};
    _hearAboutUsValues = user?.hereAboutUs ?? {};

    if (user == null) {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser?.displayName != null) {
        final displayName = (currentUser?.displayName ?? "");
        final split = displayName.split(' ');
        _nameController.text = split.firstOrNull ?? "";
        if (split.length > 1) {
          _lastNameController.text = split.lastOrNull ?? "";
        }
      }
    }
    _handleOnPageChanged();
  }

  void _handleOnPageChanged() {
    _profileUpdateEvent = ProfileUpdateEvent(
      country: _countryController.text,
      hereAboutUs: _hearAboutUsValues,
      institutionName: _institutionNameController.text,
      name: _nameController.text,
      otherPou: _otherTags,
      purposeOfUsage: _purposeOfUsageValues,
      specialization: _specializationController.text,
      surname: _lastNameController.text,
      title: _professionalTitleValues.keys.firstOrNull,
      userType: AppStrings.healthProfessionalType,
    );
    final profileUpdateEvent = _profileUpdateEvent;
    final onPageChanged = widget.onPageChanged;

    if (profileUpdateEvent != null && onPageChanged != null) onPageChanged(profileUpdateEvent);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _lastNameController.dispose();
    _ageController.dispose();
    _institutionNameController.dispose();
    _specializationController.dispose();
    _countryController.dispose();
    super.dispose();
  }
}
