// ignore_for_file: prefer-extracting-callbacks

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import '../../../../../../../core/app_config/app_strings.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/heart_about_us/hear_about_us_picker.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/name/profile_name_and_surname.dart';
import '../../../../../../auth/presentation/pages/profile/hrv/purpose_of_usage/purpose_of_usage_picker.dart';
import '../../../../../domain/entities/user_entity.dart';
import '../../../../bloc/auth_bloc.dart';
import '../../hrv/country/country_picker.dart';
import '../../hrv/institution/what_your_institution.dart';
import '../../hrv/specialization/what_your_specialization.dart';

part 'professional_profile_form_mixin.dart';

typedef OnEditChanged = void Function(ProfileUpdateEvent value);

class ProfessionalProfileForm extends StatefulWidget {
  const ProfessionalProfileForm({
    this.isEditMode,
    this.isReadOnly,
    super.key,
    this.onPageChanged,
    this.pageController,
    required this.selectedIndex,
    this.user,
  });
  final UserEntity? user;
  final PageController? pageController;
  final OnEditChanged? onPageChanged;
  final bool? isEditMode;
  final bool? isReadOnly;
  final int? selectedIndex;

  @override
  State<ProfessionalProfileForm> createState() => _ProfessionalProfileFormState();
}

class _ProfessionalProfileFormState extends State<ProfessionalProfileForm>
    with ProfessionalProfileFormMixin {
  @override
  Widget build(BuildContext context) {
    return PageView(
      controller: widget.pageController,
      onPageChanged: (value) => _handleOnPageChanged(),
      physics: NeverScrollableScrollPhysics(),
      children: [
        ProfileNameAndSurname(
          controller: _nameController,
          handleNameOnChanged: (value) {
            _nameController.text = value;
            _handleOnPageChanged();
          },
          handleSurnameOnChanged: (value) {
            _lastNameController.text = value;
            _handleOnPageChanged();
          },
          surnameController: _lastNameController,
        ),
        WhatYourInstitution(
          controller: _institutionNameController,
          handleOnChanged: (value) {
            _institutionNameController.text = value;
            _handleOnPageChanged();
          },
        ),
        WhatYourSpecialization(
          controller: _specializationController,
          handleOnChanged: (value) {
            _specializationController.text = value;
            _handleOnPageChanged();
          },
        ),
        CountryPicker(
          controller: _countryController,
          handleOnChanged: (value) {
            _countryController.text = value;
            _handleOnPageChanged();
          },
        ),
        if (!(widget.isEditMode ?? false))
          HearAboutUsPicker(
            onValueChanged: (value) {
              _hearAboutUsValues = value;
              _handleOnPageChanged();
            },
            selectedValues: _hearAboutUsValues,
          ),
        PurposeOfUsagePicker(
          onOtherValueChanged: (otherTags) {
            _otherTags = otherTags;
            _handleOnPageChanged();
          },
          onValueChanged: (value) {
            _purposeOfUsageValues = value;
            _handleOnPageChanged();
          },
          otherTags: _otherTags,
          selectedValues: _purposeOfUsageValues,
        ),
      ],
    );
  }
}
