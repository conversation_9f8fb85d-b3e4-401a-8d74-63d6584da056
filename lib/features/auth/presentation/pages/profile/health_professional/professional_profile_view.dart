import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/navigator/app_navigator.dart';
import 'package:vagustimpro/features/auth/presentation/pages/profile/health_professional/form/professional_profile_form.dart';
import '../../../../../../app/flavor/flavor_config.dart';
import '../../../../../../core/app_config/app_colors.dart';
import '../../../../../../core/app_config/app_gaps.dart';
import '../../../../../../core/app_config/app_snackbar.dart';
import '../../../../../../core/app_config/app_strings.dart';
import '../../../../../../core/custom_widgets/common_button.dart';
import '../../../../../../core/navigator/routes/app_route.dart';
import '../../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../auth/presentation/bloc/auth_bloc.dart';
import '../../../../../auth/presentation/pages/login/login_page.dart';
import '../../../../../auth/presentation/pages/profile/hrv/other/hrv_profile_bar.dart';
import '../../../../../auth/presentation/pages/profile/profile_calculating/profile_calculating_view.dart';
import '../../../../../landing/presentation/bloc/landing_bloc.dart';
import '../../../../domain/entities/user_entity.dart';

part 'professional_profile_view_mixin.dart';

class ProfessionalProfileView extends StatefulWidget {
  const ProfessionalProfileView({this.isEditMode, this.isReadOnly, super.key, this.user});

  final bool? isEditMode;
  final bool? isReadOnly;
  final UserEntity? user;

  @override
  State<ProfessionalProfileView> createState() => _ProfessionalProfileViewState();
}

class _ProfessionalProfileViewState extends State<ProfessionalProfileView>
    with ProfessionalProfileViewMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CupertinoNavigationBar(
        heroTag: UniqueKey(),
        leading: GestureDetector(
          onTap: _handleBackNavigation,
          child: Icon(Icons.arrow_back_ios_new, size: _kIconSize.sp),
        ),
        padding: EdgeInsetsDirectional.all(_kAppbarPadding),
        transitionBetweenRoutes: false,
      ),
      body: SafeArea(
        child: Padding(
          padding: context.paddingMedium,
          child: BlocConsumer<AuthBloc, AuthState>(
            builder: (ctx, state) => _handleBuildContent(state),
            listener: (ctx, state) => _handleBlocListener(state),
          ),
        ),
      ),
      resizeToAvoidBottomInset: true,
    );
  }
}
