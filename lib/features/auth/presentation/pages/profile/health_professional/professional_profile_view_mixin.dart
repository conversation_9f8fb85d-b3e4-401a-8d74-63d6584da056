// ignore_for_file: avoid-high-cyclomatic-complexity

part of 'professional_profile_view.dart';

mixin ProfessionalProfileViewMixin on State<ProfessionalProfileView> {
  final _kAppbarPadding = 8.0;
  final _kIconSize = 24;
  double _kTotalFormPages = 7;

  static const _kTotalMaxFormPages = 6.0;
  final _kTotalFormPagesEditMode = 5.0;

  static const _kMinimumLoadingDuration = Duration(seconds: 15);

  bool _isLoading = false;
  bool _isSuccessReceived = false;

  Timer? _loadingTimer;

  final _remoteConfigService = GetIt.instance.get<RemoteConfigService>();

  final _pageController = PageController();

  // Page indices as named constants.
  static const _kNameSurnamePageIndex = 0;
  static const _kInstitutionPageIndex = 1;
  static const _kSpecializationPageIndex = 2;
  static const _kCountryPageIndex = 3;
  static const _kHearAboutUsPageIndex = 4;
  static const _kPurposeOfUsagePageIndex = 5;

  @override
  void initState() {
    super.initState();
    _kTotalFormPages =
        (widget.isEditMode ?? false) ? _kTotalFormPagesEditMode : _kTotalMaxFormPages;
  }

  void _handleLoadingState() {
    _isLoading = true;
    _isSuccessReceived = false;
    final viewHeight = 0.9;

    unawaited(showModalBottomSheet(
      backgroundColor: AppColors.appTextFormFieldBackgroundColor,
      builder: (context) => SizedBox(
        height: (widget.isEditMode ?? false) ? (context.height * viewHeight) : context.height,
        child: ProfileCalculatingView(isEditMode: widget.isEditMode ?? false),
      ),
      context: context,
      enableDrag: false,
      isDismissible: false,
      isScrollControlled: true,
      useRootNavigator: true,
    ));

    _loadingTimer = Timer(_kMinimumLoadingDuration, () {
      _isLoading = false;
      if (_isSuccessReceived) _navigateAfterSuccess();
    });
  }

  void _navigateAfterSuccess() {
    if (widget.user != null) return;

    if (widget.user == null) {
      final bundleId =
          GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

      if (FlavorConstants.isHrv(bundleId)) GetIt.instance<AuthBloc>().add(GetUserEvent());
    }
  }

  void _handleFailureState(String errorMessage) {
    final currentContext = AppNavigator.navigatorKey.currentContext;

    if (currentContext != null) Navigator.of(currentContext).pop();
    context.read<LandingBloc>().add(ChangeBottomBarVisibility(isShowBottomBar: false));

    unawaited(Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginPage()),
      (route) => false,
    ));
    AppSnackbar.showErrorSnackbar(context, errorMessage);
  }

  void _handleUpdateSuccessState() {
    _isSuccessReceived = true;

    if (!_isLoading) {
      _navigateAfterSuccess();
    }
  }

  void _handleBlocListener(AuthState state) {
    switch (state) {
      case AuthLoading():
        _handleLoadingState();
        break;

      case AuthFailure():
        _handleFailureState(state.message);
        break;

      case UpdateUserSuccess():
        _handleUpdateSuccessState();
        break;

      default:
        break;
    }
  }

  // Extracted callback to a separate method.
  void _handleOnTap() {
    final profileUpdateEvent = _profileUpdateEvent;

    if (profileUpdateEvent != null) {
      context.read<AuthBloc>().add(profileUpdateEvent);
    }
  }

  // Extracted callback to a separate method.
  void _handleBackNavigation() {
    if (_selectedIndex == 0) {
      if (widget.user == null) {
        unawaited(Navigator.of(context).pushAndRemoveUntil(
          CupertinoPageRoute(builder: (context) => const LoginPage()),
          (route) => false,
        ));

        return;
      }

      AppRoute.pop(context);
    } else {
      setState(() => _selectedIndex -= 1);
      unawaited(_pageController.animateToPage(
        _selectedIndex,
        curve: Curves.easeIn,
        duration: context.lowDuration,
      ));
    }
  }

  // Extracted callback to a separate method.
  void _handleContinueButton() {
    FocusScope.of(context).unfocus();

    if (!_isCurrentPageValid) {
      final errorText = "Please fill in all required fields";

      AppSnackbar.showErrorSnackbar(context, errorText);

      return;
    }

    setState(() => _selectedIndex += 1);
    unawaited(_pageController.animateToPage(
      _selectedIndex,
      curve: Curves.easeIn,
      duration: context.lowDuration,
    ));

    if (_selectedIndex == _kTotalFormPages) {
      _handleOnTap();
    }
  }

  bool get _isCurrentPageValid {
    if (_profileUpdateEvent == null) return false;

    final purposeOfUsageIndex =
        ((widget.isEditMode ?? false)) ? _kPurposeOfUsagePageIndex - 1 : _kPurposeOfUsagePageIndex;

    if ((_profileUpdateEvent?.purposeOfUsage ?? {}).isEmpty &&
        (purposeOfUsageIndex == _selectedIndex)) return false;

    if ((_profileUpdateEvent?.purposeOfUsage ?? {})["Other"] == true &&
        (_profileUpdateEvent?.otherPou ?? []).isEmpty &&
        (purposeOfUsageIndex == _selectedIndex)) {
      return false;
    }

    switch (_selectedIndex) {
      case _kNameSurnamePageIndex: // Name and Surname page.
        return _profileUpdateEvent?.name != null &&
            (_profileUpdateEvent?.name ?? "").isNotEmpty &&
            _profileUpdateEvent?.surname != null &&
            (_profileUpdateEvent?.surname ?? "").isNotEmpty;

      case _kInstitutionPageIndex: // Institution page.
        return _profileUpdateEvent?.institutionName != null &&
            (_profileUpdateEvent?.institutionName ?? "").isNotEmpty;

      case _kSpecializationPageIndex: // Specialization page.
        return _profileUpdateEvent?.specialization != null &&
            (_profileUpdateEvent?.specialization ?? "").isNotEmpty;

      case _kCountryPageIndex: // Country page.
        return _profileUpdateEvent?.country != null &&
            (_profileUpdateEvent?.country ?? "").isNotEmpty;

      case _kHearAboutUsPageIndex: // Hear About Us (if not edit mode).
        if (!(widget.isEditMode ?? false)) {
          return _profileUpdateEvent?.hereAboutUs != null &&
              (_profileUpdateEvent?.hereAboutUs ?? {}).isNotEmpty;
        }

        return true;

      default:
        return true;
    }
  }

  // Extracted callback to a separate method.
  void _handleOnPageChanged(ProfileUpdateEvent event) {
    _profileUpdateEvent = event;
  }

  double? _initialMaxHeight;

  Widget _handleBuildContent(AuthState state) {
    final isLoading = state is AuthLoading;
    final keyboardValue = 50;

    return LayoutBuilder(
      builder: (context, constraints) {
        _initialMaxHeight ??= constraints.maxHeight;
        final heightDifference = (_initialMaxHeight ?? 0) - constraints.maxHeight;
        final isKeyboardOpen = heightDifference > keyboardValue;

        return Column(
          spacing: 0,
          children: [
            HrvProfileBar(
              index: _selectedIndex,
              profileBarLength:
                  ((widget.isEditMode ?? false) ? _kTotalFormPagesEditMode : _kTotalMaxFormPages)
                      .toInt(),
            ),
            AppGaps.instance.gapVS16,
            Expanded(
              child: ProfessionalProfileForm(
                isEditMode: widget.isEditMode,
                isReadOnly: widget.isReadOnly,
                onPageChanged: _handleOnPageChanged,
                pageController: _pageController,
                selectedIndex: _selectedIndex,
                user: widget.user,
              ),
            ),
            if (!isLoading)
              AnimatedOpacity(
                duration: context.veryLowDuration,
                opacity: isKeyboardOpen ? 0 : 1,
                child: Visibility(
                  visible: !isKeyboardOpen,
                  child: CommonButton(
                    buttonColor: AppColors.neoBlue,
                    icon: isLoading ? const CupertinoActivityIndicator() : null,
                    isDisabled: isLoading,
                    onPress: _handleContinueButton,
                    text: _remoteConfigService.deviceControlPageConfig.continueTxt,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _loadingTimer?.cancel();
    super.dispose();
  }

  int _selectedIndex = 0;

  ProfileUpdateEvent? _profileUpdateEvent;
}
