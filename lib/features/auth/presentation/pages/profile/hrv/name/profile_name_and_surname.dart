// ignore_for_file: prefer-correct-callback-field-name

import 'package:flutter/cupertino.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import '../../../../../../../core/app_config/app_text_styles.dart';
import '../../../../../../subuser/presentation/widgets/form_fields/new_app_text_form_field.dart';

class ProfileNameAndSurname extends StatelessWidget {
  const ProfileNameAndSurname({
    this.controller,
    this.handleNameOnChanged,
    this.handleSurnameOnChanged,
    this.isReadOnly,
    this.isSubuser,
    super.key,
    this.surnameController,
  });

  final Function(String value)? handleNameOnChanged;
  final Function(String value)? handleSurnameOnChanged;
  final TextEditingController? controller;
  final TextEditingController? surnameController;
  final bool? isReadOnly;
  final bool? isSubuser;

  @override
  Widget build(BuildContext context) {
    final kNameTitle = "What's your name?";
    final kUserTitle = "What's user's name?";

    final titleText = (isSubuser ?? false) ? kUserTitle : kNameTitle;

    return Column(spacing: 0, children: [
      Text(titleText, style: AppTextStyles.hrvProfileText),
      AppGaps.instance.gapVS16,
      NewAppTextFormField(
        controller: controller,
        handleOnChanged: handleNameOnChanged,
        isReadOnly: isReadOnly,
        label: "Name",
      ),
      AppGaps.instance.gapVS8,
      NewAppTextFormField(
        controller: surnameController,
        handleOnChanged: handleSurnameOnChanged,
        isReadOnly: isReadOnly,
        label: "Surname",
      ),
    ]);
  }
}
