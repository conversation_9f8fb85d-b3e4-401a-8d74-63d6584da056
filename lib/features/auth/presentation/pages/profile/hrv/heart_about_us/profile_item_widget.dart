import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';

class ProfileItemWidget extends StatelessWidget {
  const ProfileItemWidget({
    this.isReadOnly,
    required this.isSelected,
    super.key,
    required this.onTap,
    required this.text,
    required this.value,
  });
  final String value;
  final bool isSelected;
  final VoidCallback onTap;
  final String text;
  final bool? isReadOnly;

  @override
  Widget build(BuildContext context) {
    final baseFontSize = 14.0;
    final horizontal = 16.0;
    final vertical = 8.0;

    final height = 40;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: AppBorderRadius.circularSize12Radius(),
          color: isSelected
              ? (isReadOnly ?? false)
                  ? AppColors.appTextFormFieldDisabledBackgroundColor
                  : AppColors.neoBlue
              : AppColors.profileFormNonItemSelect,
        ),
        height: height.sp,
        padding: EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical),
        child: Center(
          child: FittedBox(
            child: Text(
              text,
              style: TextStyle(
                color: (isReadOnly ?? false)
                    ? AppColors.appTextFormFieldDisabledTextColor
                    : isSelected
                        ? AppColors.profileFormNonItemSelect
                        : AppColors.profileFormItemText,
                fontSize: baseFontSize.sp,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
