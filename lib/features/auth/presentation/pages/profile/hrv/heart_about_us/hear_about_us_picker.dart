// ignore_for_file: avoid-missing-interpolation, avoid-collection-mutating-methods

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/remote_config/profile_page_config.dart';

import '../../../../../../../core/remote_config/remote_config_service.dart';
import '../../profile_item.dart';
import 'profile_item_view.dart';

typedef OnValueChanged = void Function(Map<String, bool> value);

class HearAboutUsPicker extends StatefulWidget {
  const HearAboutUsPicker({
    this.isReadOnly = false,
    super.key,
    required this.onValueChanged,
    this.selectedValues,
  });

  final bool? isReadOnly;
  final OnValueChanged onValueChanged;
  final Map<String, bool>? selectedValues;

  @override
  State<HearAboutUsPicker> createState() => _HearAboutUsPickerState();
}

class _HearAboutUsPickerState extends State<HearAboutUsPicker> {
  final _selectedValues = <String, bool>{};

  @override
  void initState() {
    super.initState();
    if (widget.selectedValues != null) {
      _selectedValues.addAll(widget.selectedValues ?? {});
    }
  }

  void _handleTap(String value) {
    final isSelected = _selectedValues[value] ?? false;

    if (isSelected) {
      _selectedValues.remove(value);
    } else {
      _selectedValues[value] = true;
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final profilePageConfig = remoteConfigService.profilePageConfig;

    final ProfilePageConfig(
      :email,
      :events,
      :other,
      :searchEngine,
      :socialMedia,
      :wordOfMouth,
    ) = profilePageConfig;

    final values = [
      {socialMedia: 'social_media'},
      {searchEngine: 'search_engine'},
      {wordOfMouth: 'word_of_mouth'},
      {events: 'events'},
      {email: 'email'},
      {other: 'other'},
    ];

    final items = values
        .map((value) =>
            ProfileItem(text: value.keys.firstOrNull ?? "", value: value.values.firstOrNull ?? ""))
        .toList();

    return ProfileItemView(
      isReadOnly: widget.isReadOnly,
      items: items,
      onItemTap: (value) {
        if ((widget.isReadOnly ?? false)) return;

        _handleTap(value);
        widget.onValueChanged(_selectedValues);
      },
      selectedValues: _selectedValues,
      title: "Where did you hear about us?",
    );
  }
}
