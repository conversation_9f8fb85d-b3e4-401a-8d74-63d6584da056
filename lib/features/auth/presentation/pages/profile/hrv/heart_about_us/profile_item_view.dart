// ignore_for_file: no-equal-arguments

import 'package:flutter/cupertino.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/auth/presentation/pages/profile/hrv/heart_about_us/profile_item_widget.dart';
import 'package:vagustimpro/features/auth/presentation/pages/profile/profile_item.dart';

import '../../../../../../../core/app_config/app_text_styles.dart';

typedef OnItemTapCallback = void Function(String value);
typedef OtherFieldBuilder = Widget Function(BuildContext context);

class ProfileItemView extends StatelessWidget {
  const ProfileItemView({
    this.isReadOnly,
    required this.items,
    super.key,
    required this.onItemTap,
    this.otherFieldBuilder,
    required this.selectedValues,
    this.title,
  });

  static // Extract border radius logic.
      BorderRadius _getBorderRadius(int index, int itemCount, double radius) {
    if (itemCount == 1) {
      // Single item in a row - radius on all corners.
      return BorderRadius.circular(radius);
    } else if (index == 0) {
      // First item in the row - radius only on left corners.
      return BorderRadius.only(
        bottomLeft: Radius.circular(radius),
        topLeft: Radius.circular(radius),
      );
    } else if (index == itemCount - 1) {
      // Last item in the row - radius only on right corners.
      return BorderRadius.only(
        bottomRight: Radius.circular(radius),
        topRight: Radius.circular(radius),
      );
    } // Middle items - no radius.

    return BorderRadius.zero;
  }

  final List<ProfileItem> items;
  final Map<String, bool> selectedValues;
  final OnItemTapCallback onItemTap;
  final OtherFieldBuilder? otherFieldBuilder;
  final String? title;
  final bool? isReadOnly;

  // Extract item rows building logic to a separate method.
  Widget _handleBuildItemRows(BuildContext context) {
    // Extract magic numbers to named constants.
    const kRunSpacing = 16.0;
    const kBorderRadius = 12.0;

    // Divide items into rows of 2.
    final rows = _createItemRows();

    // Build column of rows.
    final rowWidgets = <Widget>[];
    for (int rowIndex = 0; rowIndex < rows.length; rowIndex += 1) {
      final rowItems = rows[rowIndex];
      rowWidgets.add(
        Padding(
          padding: const EdgeInsets.only(bottom: kRunSpacing),
          child: _buildRowItems(kBorderRadius, context, rowItems),
        ),
      );
    }

    return Column(children: rowWidgets);
  }

  // Create rows of items, 2 items per row.
  List<List<ProfileItem>> _createItemRows() {
    List<List<ProfileItem>> rows = [];
    List<ProfileItem> currentRow = [];
    final maxLength = 2;

    for (int index = 0; index < items.length; index += 1) {
      currentRow.add(items[index]);

      if (currentRow.length == maxLength || index == items.length - 1) {
        rows.add(List.of(currentRow));
        currentRow = [];
      }
    }

    return rows;
  }

  // Build a row of items.
  Widget _buildRowItems(double borderRadius, BuildContext context, List<ProfileItem> rowItems) {
    final itemWidgets = <Widget>[];

    for (int index = 0; index < rowItems.length; index += 1) {
      final item = rowItems[index];
      itemWidgets.add(
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: _getBorderRadius(index, rowItems.length, borderRadius),
            ),
            padding: context.paddingLow,
            child: _buildProfileItemWidget(item),
          ),
        ),
      );
    }

    return Row(children: itemWidgets);
  }

  // Extract profile item widget creation.
  Widget _buildProfileItemWidget(ProfileItem item) {
    return ProfileItemWidget(
      isReadOnly: isReadOnly,
      isSelected: selectedValues[item.value] ?? false,
      onTap: () => _handleItemTap(item.value),
      text: item.text,
      value: item.value,
    );
  }

  // Extract callback to a separate method.
  void _handleItemTap(String value) {
    onItemTap(value);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        spacing: 0,
        children: [
          Text(title ?? "", style: AppTextStyles.hrvProfileText),
          AppGaps.instance.gapVS16,
          _handleBuildItemRows(context),
          if (otherFieldBuilder != null) otherFieldBuilder!(context),
        ],
      ),
    );
  }
}
