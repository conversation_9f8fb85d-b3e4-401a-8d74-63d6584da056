import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/enum/assets_enums_lottie.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../../insight/presentation/pages/widgets/info_card/info_card_score/insight_comprehensive_loading/random_percentage_animation.dart';

class HrvLoadingWidget extends StatelessWidget {
  const HrvLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final square = 100;
    final totalDuration = 3000;
    final defaultPaddingValue = 16;
    final minPause = 0.3;
    final shortDelayMs = 200;

    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.maybePaddingOf(context)?.bottom ?? defaultPaddingValue.sp,
        left: context.paddingMedium.left,
        right: context.paddingMedium.right,
        top: kMinInteractiveDimension.sp,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AssetsEnums.hrvProfileLoadingHeader.toPng(),
          RandomPercentageAnimation(
            incrementSecond: 1,
            maxPause: 1,
            minPause: minPause,
            shortDelayMs: shortDelayMs,
            totalDuration: Duration(milliseconds: totalDuration),
          ),
          Column(
            spacing: 0,
            children: [
              Text(
                "Creating your profile...",
                style: AppTextStyles.comprehensiveLoadingTitle,
              ),
              AppGaps.instance.gapVS16,
              Center(
                child: Text(
                  "Your data is being analyzed and your personal health profile is being generated. This sets the foundation for smarter, more personalized recommendations.",
                  style: AppTextStyles.comprehensiveLoadingDescription,
                ),
              ),
            ],
          ),
          AppGaps.instance.gapVS32,
          SizedBox.square(
            dimension: square.sp,
            child: AssetsEnumsLottie.hrvLoading.toLottie(),
          ),
        ],
      ),
    );
  }
}
