// ignore_for_file: prefer-extracting-function-callbacks

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_snackbar.dart';
import 'package:vagustimpro/core/custom_widgets/common_button.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/navigator/app_navigator.dart';
import 'package:vagustimpro/core/navigator/routes/app_route.dart';
import 'package:vagustimpro/features/auth/presentation/pages/login/login_page.dart';
import 'package:vagustimpro/features/auth/presentation/pages/profile/hrv/form/hrv_profile_form.dart';
import '../../../../../../../core/app_config/app_colors.dart';
import '../../../../../../../core/navigator/routes/constants/route_constants.dart';
import '../../../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../../subuser/domain/entities/subuser_entity.dart';
import '../../../../../../subuser/presentation/bloc/subuser_bloc.dart';
import '../../../../bloc/auth_bloc.dart';
import 'hrv_loading_widget.dart';
import 'hrv_profile_bar.dart';

class HrvProfileView extends StatefulWidget {
  const HrvProfileView({super.key, this.subuser});
  final SubuserEntity? subuser;

  @override
  State<HrvProfileView> createState() => _HrvProfileViewState();
}

class _HrvProfileViewState extends State<HrvProfileView> {
  static const _kIconSize = 24;
  static const _kTotalFormPages = 5;

  static const _kMinimumLoadingDuration = Duration(seconds: 3);

  bool _isLoading = false;
  bool _isSuccessReceived = false;

  Timer? _loadingTimer;

  final _remoteConfigService = GetIt.instance.get<RemoteConfigService>();

  final _pageController = PageController();

  void _handleLoadingState() {
    setState(() {
      _isLoading = true;
      _isSuccessReceived = false;
    });

    unawaited(showModalBottomSheet(
      backgroundColor: AppColors.appTextNewFormFieldBackgroundColor,
      builder: (ctx) => PopScope(canPop: false, child: HrvLoadingWidget()),
      context: context,
      enableDrag: false,
      isDismissible: false,
      isScrollControlled: true,
      useRootNavigator: true,
      useSafeArea: true,
    ));

    _loadingTimer = Timer(_kMinimumLoadingDuration, () {
      setState(() => _isLoading = false);
      if (_isSuccessReceived) _navigateAfterSuccess();
    });
  }

  void _navigateAfterSuccess() {
    if (widget.subuser == null) {
      GetIt.instance<AuthBloc>().add(GetUserEvent());
      unawaited(
        Navigator.of(context).pushNamedAndRemoveUntil(RouteConstants.landingPage, (route) => false),
      );
    } else {
      final currentContext = AppNavigator.navigatorKey.currentContext;

      if (currentContext != null) Navigator.of(currentContext).pop();
      AppRoute.pop(context);
    }
  }

  void _handleFailureState(String errorMessage) {
    if (widget.subuser == null) {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false,
      ));
    } else {
      final currentContext = AppNavigator.navigatorKey.currentContext;

      if (currentContext != null) Navigator.of(currentContext).pop();
      AppRoute.pop(context);
    }
    AppSnackbar.showErrorSnackbar(context, errorMessage);
  }

  void _handleUpdateSuccessState() {
    setState(() {
      _isSuccessReceived = true;
    });

    if (!_isLoading) {
      _navigateAfterSuccess();
    }
  }

  void _handleBlocListener(BuildContext context, SubuserState state) {
    switch (state) {
      case SubuserLoading():
        _handleLoadingState();
        break;

      case SubuserFailure():
        _handleFailureState(state.message);
        break;

      case SubuserCreatedSuccess():
      case SubuserUpdatedSuccess():
        _handleUpdateSuccessState();
        break;

      default:
        break;
    }
  }

  // Extracted callback to a separate method.
  void _handleOnTap() {
    if (_subuserEditEvent != null) {
      if (widget.subuser == null) {
        context.read<SubuserBloc>().add(SubuserCreateEvent(
              age: _subuserEditEvent?.age ?? " ",
              gender: _subuserEditEvent?.gender ?? " ",
              hearAboutUs: _subuserEditEvent?.hearAboutUs ?? {},
              height: _subuserEditEvent?.height ?? "",
              name: _subuserEditEvent?.name ?? "",
              otherPou: _subuserEditEvent?.otherPou ?? [],
              purposeOfUsage: _subuserEditEvent?.purposeOfUsage ?? {},
              surname: _subuserEditEvent?.surname ?? "",
              weight: _subuserEditEvent?.weight ?? "",
            ));
      } else {
        context.read<SubuserBloc>().add(_subuserEditEvent!);
      }
    }
  }

  // Extracted callback to a separate method.
  void _handleBackNavigation() {
    if (_selectedIndex == 0) {
      if (widget.subuser == null) {
        unawaited(Navigator.of(context).pushAndRemoveUntil(
          CupertinoPageRoute(builder: (context) => const LoginPage()),
          (route) => false,
        ));

        return;
      }

      AppRoute.pop(context);
    } else {
      setState(() => _selectedIndex -= 1);
      unawaited(_pageController.animateToPage(
        _selectedIndex,
        curve: Curves.easeIn,
        duration: context.lowDuration,
      ));
    }
  }

  // Extracted callback to a separate method.
  void _handleContinueButton() {
    FocusScope.of(context).unfocus();
    setState(() => _selectedIndex += 1);
    unawaited(_pageController.animateToPage(
      _selectedIndex,
      curve: Curves.easeIn,
      duration: context.lowDuration,
    ));

    if (_selectedIndex == _kTotalFormPages) {
      _handleOnTap();
    }
  }

  // Extracted callback to a separate method.
  void _handleOnPageChanged(SubuserEditEvent event) {
    _subuserEditEvent = event;
  }

  // Extracted bloc builder to a separate method.
  Widget _handleBuildContent(SubuserState state) {
    final isLoading = state is SubuserLoading;

    return Column(spacing: 0, children: [
      if (!isLoading) HrvProfileBar(index: _selectedIndex, profileBarLength: _kTotalFormPages),
      AppGaps.instance.gapVS16,
      Expanded(
        child: HrvProfileForm(
          onPageChanged: _handleOnPageChanged,
          pageController: _pageController,
          subuser: widget.subuser,
        ),
      ),
      if (!isLoading)
        CommonButton(
          buttonColor: AppColors.neoBlue,
          icon: isLoading ? const CupertinoActivityIndicator() : null,
          isDisabled: isLoading,
          onPress: _handleContinueButton,
          text: _remoteConfigService.deviceControlPageConfig.continueTxt,
        ),
    ]);
  }

  int _selectedIndex = 0;

  SubuserEditEvent? _subuserEditEvent;

  @override
  void dispose() {
    _pageController.dispose();
    _loadingTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CupertinoNavigationBar(
        heroTag: UniqueKey(),
        leading: GestureDetector(
          onTap: _handleBackNavigation,
          child: Icon(Icons.arrow_back_ios_new, size: _kIconSize.sp),
        ),
        transitionBetweenRoutes: false,
      ),
      body: SafeArea(
        child: Padding(
          padding: context.paddingMedium,
          child: BlocConsumer<SubuserBloc, SubuserState>(
            builder: (ctx, state) => _handleBuildContent(state),
            listener: _handleBlocListener,
          ),
        ),
      ),
    );
  }
}
