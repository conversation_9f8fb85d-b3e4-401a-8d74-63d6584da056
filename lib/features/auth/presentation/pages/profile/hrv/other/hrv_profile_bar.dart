// ignore_for_file: prefer-for-loop-in-children

import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';

import '../../../../../../../core/app_config/app_colors.dart';

class HrvProfileBar extends StatelessWidget {
  const HrvProfileBar({this.index, super.key, this.profileBarLength});
  final int? index;
  final int? profileBarLength;

  @override
  Widget build(BuildContext context) {
    final marginValue = 3.0;
    final maxLength = 6;
    final length = profileBarLength ?? maxLength;
    final height = 4;

    return Row(
      children: List.generate(
        length,
        (barIndex) => Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: AppBorderRadius.circularSize8Radius(),
              color: index == barIndex ? AppColors.neoBlue : AppColors.profileBarUnSelected,
            ),
            height: height.h,
            margin: context.paddingLowHorizontal / marginValue,
          ),
        ),
      ),
    );
  }
}
