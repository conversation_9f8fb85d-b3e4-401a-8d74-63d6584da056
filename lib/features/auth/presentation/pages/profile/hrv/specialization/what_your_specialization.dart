// ignore_for_file: prefer-correct-callback-field-name

import 'package:flutter/cupertino.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import '../../../../../../../core/app_config/app_text_styles.dart';
import '../../../../../../subuser/presentation/widgets/form_fields/new_app_text_form_field.dart';

class WhatYourSpecialization extends StatelessWidget {
  const WhatYourSpecialization({
    this.controller,
    this.handleOnChanged,
    super.key,
  });

  final Function(String value)? handleOnChanged;
  final TextEditingController? controller;

  @override
  Widget build(BuildContext context) {
    return Column(spacing: 0, children: [
      Text("What’s your specialization?", style: AppTextStyles.hrvProfileText),
      AppGaps.instance.gapVS16,
      NewAppTextFormField(
        controller: controller,
        handleOnChanged: handleOnChanged,
        label: "Specialization/Field",
      ),
    ]);
  }
}
