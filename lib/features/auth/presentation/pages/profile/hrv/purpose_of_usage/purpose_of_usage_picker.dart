// ignore_for_file: avoid-ignoring-return-values, avoid-collection-mutating-methods

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/remote_config/subuser_page_config.dart';

import '../../../../../../../core/remote_config/remote_config_service.dart';
import '../../../../../../../core/app_config/app_colors.dart';
import '../../../../../../../core/app_config/app_gaps.dart';
import '../../../../../../subuser/presentation/widgets/form_fields/new_app_text_form_field.dart';
import '../../profile_item.dart';
import '../heart_about_us/profile_item_view.dart';

typedef OnValueChanged = void Function(Map<String, bool> value);
typedef OnOtherValueChanged = void Function(List<String> otherTags);

class PurposeOfUsagePicker extends StatefulWidget {
  const PurposeOfUsagePicker({
    this.isReadOnly = false,
    this.isSubuser,
    super.key,
    this.onOtherValueChanged,
    required this.onValueChanged,
    this.otherTags,
    this.selectedValues,
  });

  final bool? isReadOnly;
  final OnValueChanged onValueChanged;
  final OnOtherValueChanged? onOtherValueChanged;
  final Map<String, bool>? selectedValues;
  final List<String>? otherTags;
  final bool? isSubuser;

  @override
  State<PurposeOfUsagePicker> createState() => _PurposeOfUsagePickerState();
}

class _PurposeOfUsagePickerState extends State<PurposeOfUsagePicker> {
  final _selectedValues = <String, bool>{};
  final _otherTextController = TextEditingController();

  final _otherTags = <String>[];

  final _kPurposeOfUsageTitle = "What's your purpose of usage?";
  final _kUserPurposeOfUsageTitle = "What's user's purpose of usage?";

  bool _isOtherSelected = false;

  @override
  void initState() {
    super.initState();

    if (widget.selectedValues != null) {
      _selectedValues.addAll(widget.selectedValues ?? {});
    }

    if (_otherTags.isEmpty) {
      _isOtherSelected = false;
    }
    if ((widget.otherTags ?? []).isNotEmpty) {
      _otherTags.addAll(widget.otherTags ?? {});
      _isOtherSelected = true;
    }

    if ((widget.selectedValues ?? {})["Other"] == true) {
      _isOtherSelected = true;
    }
  }

  void _handleTap(String value) {
    final otherValue = 'Other';

    final isSelected = _selectedValues[value] ?? false;

    if (isSelected) {
      _selectedValues.remove(value);
      if (value == otherValue) {
        setState(() => _isOtherSelected = false);
      }
    } else {
      _selectedValues[value] = true;
      if (value == otherValue) {
        setState(() => _isOtherSelected = true);
      }
    }

    setState(() => widget.onValueChanged(_selectedValues));
  }

  void _handleAddOtherTag() {
    final text = _otherTextController.text.trim();
    if (text.isEmpty) return;

    if (_otherTags.contains(text)) {
      _otherTags.remove(text);
    }

    setState(() {
      _otherTags.add(text);

      _otherTextController.clear();
    });

    if (widget.onOtherValueChanged != null) widget.onOtherValueChanged!(_otherTags);
  }

  void _handleRemoveOtherTag(String tag) {
    setState(() => _otherTags.remove(tag));
    widget.onOtherValueChanged!(_otherTags);
  }

  Widget _handleBuildOtherField(BuildContext context) {
    final size = 28.0;
    final width = size;

    final spacing = 8.0;
    final runSpacing = spacing;

    final addMore = "Add more";

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        NewAppTextFormField(
          controller: _otherTextController,
          isLabelText: false,
          isReadOnly: widget.isReadOnly,
          label: addMore,
          onSuffixTap: _handleAddOtherTag,
          suffixWidget: Container(
            decoration: BoxDecoration(
              borderRadius: AppBorderRadius.circularSize8Radius(),
              color: AppColors.profileFormOtherBackground,
            ),
            height: size,
            width: width,
            child: Icon(Icons.add, color: AppColors.neoBlue, size: size.sp),
          ),
        ),
        if (_otherTags.isNotEmpty) ...[
          AppGaps.instance.gapVS8,
          Wrap(
            runSpacing: runSpacing,
            spacing: spacing,
            children: _otherTags.asMap().entries.map((entry) => _buildTag(entry.value)).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildTag(String tag) {
    final iconSize = 20.0;
    final rowSpacing = 8.0;
    final height = 40;
    final left = 32.0;
    final right = 16.0;

    return TweenAnimationBuilder(
      builder: (context, double value, child) {
        return Opacity(opacity: value, child: child);
      },
      duration: context.lowDuration,
      tween: Tween(begin: 0.0, end: 1.0),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: AppBorderRadius.circularSize8Radius(),
          color: AppColors.neoBlue,
        ),
        height: height.sp,
        padding: context.paddingMediumHorizontalDisplayVertical.copyWith(left: left, right: right),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.min,
          spacing: rowSpacing,
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: context.width * 0.7),
              child: Text(tag, style: AppTextStyles.profilePurposeOfUsageOtherItem),
            ),
            AppGaps.instance.gapVS4,
            GestureDetector(
              onTap: () => (widget.isReadOnly ?? false) ? null : _handleRemoveOtherTag(tag),
              child: Icon(Icons.close, color: AppColors.profileFormOtherIcon, size: iconSize),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _otherTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance.get<RemoteConfigService>();
    final subuserPageConfig = remoteConfigService.subuserPageConfig;

    final purposeOfUsageTitle =
        (widget.isSubuser ?? false) ? _kUserPurposeOfUsageTitle : _kPurposeOfUsageTitle;

    final SubuserPageConfig(
      :cognitivePerformance,
      :digestiveHealth,
      :other,
      :overallWellbeing,
      :recovery,
      :sleep,
      :stress,
    ) = subuserPageConfig;

    final values = [
      {digestiveHealth: 'Digestive Health'},
      {overallWellbeing: 'Overall Wellbeing'},
      {stress: 'Stress'},
      {sleep: 'Sleep'},
      {recovery: 'Recovery'},
      {cognitivePerformance: 'Cognitive Performance'},
      {other: 'Other'},
    ];

    final items = values
        .map((value) =>
            ProfileItem(text: value.keys.firstOrNull ?? "", value: value.values.firstOrNull ?? ""))
        .toList();

    return ProfileItemView(
      isReadOnly: widget.isReadOnly,
      items: items,
      onItemTap: (value) {
        if ((widget.isReadOnly ?? false)) return;

        _handleTap(value);
      },
      otherFieldBuilder:
          _isOtherSelected && !(widget.isReadOnly ?? false) ? _handleBuildOtherField : null,
      selectedValues: _selectedValues,
      title: purposeOfUsageTitle,
    );
  }
}
