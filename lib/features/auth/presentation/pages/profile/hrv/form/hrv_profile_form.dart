// ignore_for_file: prefer-extracting-callbacks

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:vagustimpro/features/auth/presentation/pages/profile/hrv/weight/weight_unit.dart';

import '../../../../../../subuser/domain/entities/subuser_entity.dart';
import '../../../../../../subuser/presentation/bloc/subuser_bloc.dart';
import '../age/age_picker.dart';
import '../gender/gender.dart';
import '../gender/hexagon_gender_selection.dart';
import '../height/height_picker.dart';
import '../height/height_unit.dart';
import '../name/profile_name_and_surname.dart';
import '../weight/weight_picker.dart';

part 'hrv_profile_form_mixin.dart';

class HrvProfileForm extends StatefulWidget {
  const HrvProfileForm({super.key, this.onPageChanged, this.pageController, this.subuser});
  final SubuserEntity? subuser;
  final PageController? pageController;

  final Function(SubuserEditEvent event)? onPageChanged;

  @override
  State<HrvProfileForm> createState() => _HrvProfileFormState();
}

class _HrvProfileFormState extends State<HrvProfileForm> with HrvProfileFormMixin {
  @override
  Widget build(BuildContext context) {
    final isContainsCm = _heightController.text.contains("cm");

    final heightControllerSplit =
        isContainsCm ? null : _heightController.text.split("'").firstOrNull;

    final weightControllerSplit = _weightController.text.split(" ").firstOrNull;

    return PageView(
      controller: widget.pageController,
      onPageChanged: (value) => _handleOnPageChanged(),
      physics: NeverScrollableScrollPhysics(),
      children: [
        ProfileNameAndSurname(
          controller: _nameController,
          handleNameOnChanged: (value) => _nameController.text = value,
          handleSurnameOnChanged: (value) => _lastNameController.text = value,
          surnameController: _lastNameController,
        ),
        HexagonGenderSelector(
          initialGender: _genderController.text == Gender.male.name ? Gender.male : Gender.female,
          onGenderSelected: (gender) => _genderController.text = gender.name.toLowerCase(),
        ),
        AgePicker(
          initialYear: int.tryParse(_ageController.text),
          onYearChanged: (age) => _ageController.text = age.toString(),
        ),
        WeightPicker(
          initialUnit:
              (widget.subuser?.weight ?? "").contains('kg') ? WeightUnit.kg : WeightUnit.lbs,
          initialWeight: weightControllerSplit == null ? null : int.tryParse(weightControllerSplit),
          onWeightChanged: (unit, weight) => _weightController.text = ("$weight ${unit.name}"),
        ),
        HeightPicker(
          initialCm: isContainsCm ? int.tryParse(_heightController.text.split(" ").first) : null,
          initialFeet: heightControllerSplit == null ? null : int.tryParse(heightControllerSplit),
          initialInches: heightControllerSplit == null
              ? null
              : int.tryParse(_heightController.text.split("'").last.replaceAll('"', '')),
          initialUnit:
              (widget.subuser?.height ?? "").contains('cm') ? HeightUnit.cm : HeightUnit.feetInch,
          onCmChanged: (cmValue) {
            _heightController.text = "$cmValue cm";

            _handleOnPageChanged();
          },
          onHeightChanged: (feet, inches) {
            _heightController.text = "$feet' $inches\"";

            _handleOnPageChanged();
          },
        ),
      ],
    );
  }
}
