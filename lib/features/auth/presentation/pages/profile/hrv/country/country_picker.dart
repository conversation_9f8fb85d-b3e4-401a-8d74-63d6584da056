// ignore_for_file: prefer-extracting-function-callbacks, prefer-extracting-callbacks, avoid-non-null-assertion, prefer-moving-to-variable

import 'package:country_picker/country_picker.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/features/subuser/presentation/widgets/form_fields/new_app_text_form_field.dart';
import '../../../../../../../core/app_config/app_colors.dart';
import '../../../../../../../core/app_config/app_text_styles.dart';
import '../../../../../../../core/remote_config/remote_config_service.dart';

class CountryPicker extends StatefulWidget {
  const CountryPicker({this.controller, this.handleOnChanged, super.key});

  final Function(String value)? handleOnChanged;
  final TextEditingController? controller;

  @override
  State<CountryPicker> createState() => _CountryPickerState();
}

class _CountryPickerState extends State<CountryPicker> {
  bool _isExpanded = false;
  List<Country> _allCountries = [];
  List<Country> _filteredCountries = [];
  List<Country> _favoriteCountries = [];
  Country? _selectedCountry;
  final _searchController = TextEditingController();

  // Excluded and favorite country codes.
  final _excludedCountryCodes = ['AF', 'AX'];
  final _favoriteCountryCodes = ['US', 'CA', 'AU'];

  @override
  void initState() {
    super.initState();
    // Get all countries and filter out excluded ones.
    _allCountries = CountryService()
        .getAll()
        .where((country) => !_excludedCountryCodes.contains(country.countryCode))
        .toList();

    // Create favorite countries list.
    _favoriteCountries = _allCountries
        .where((country) => _favoriteCountryCodes.contains(country.countryCode))
        .toList();

    _filteredCountries = _allCountries;
    _searchController.addListener(_filterCountries);

    // Set initial selected country if controller has text.
    if (widget.controller != null && (widget.controller?.text ?? "").isNotEmpty) {
      _selectedCountry = _allCountries.firstWhere(
        (country) => country.name == widget.controller?.text,
        orElse: () => _allCountries.first,
      );
    }
  }

  void _filterCountries() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      // Show favorites at the top when no search query.
      _filteredCountries = query.isEmpty
          ? [
              ..._favoriteCountries,
              ..._allCountries.where(
                (country) => !_favoriteCountryCodes.contains(country.countryCode),
              ),
            ]
          : _allCountries.where((country) => country.name.toLowerCase().contains(query)).toList();
    });
  }

  void _handleSelectCountry(Country country) {
    setState(() {
      _selectedCountry = country;
      _isExpanded = false;
      widget.controller?.text = country.name;
    });

    if (widget.handleOnChanged != null) {
      widget.handleOnChanged!(country.name);
    }
  }

  Widget _buildCountryItem(Country country, bool isFavorite, bool isSelected) {
    return InkWell(
      onTap: () => _handleSelectCountry(country),
      child: Container(
        color: isSelected ? AppColors.neoBlue.withOpacity(0.1) : Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            spacing: 0,
            children: [
              Text(country.flagEmoji, style: const TextStyle(fontSize: 24)),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  country.name,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
              if (isFavorite && !isSelected) const Icon(Icons.star, color: Colors.amber, size: 18),
              if (isSelected) const Icon(Icons.check, color: Colors.blue),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final fontSize = 16.0;
    final height = 0.5;

    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final profilePageConfig = remoteConfigService.profilePageConfig;

    // Initialize filtered countries if empty (first build).
    if (_filteredCountries.isEmpty) {
      _filteredCountries = [
        ..._favoriteCountries,
        ..._allCountries.where(
          (country) => !_favoriteCountryCodes.contains(country.countryCode),
        ),
      ];
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        spacing: 0,
        children: [
          Text("What's your country?", style: AppTextStyles.hrvProfileText),
          AppGaps.instance.gapVS16,
          // Country Selector as ExpansionTile-like widget.
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.countryPickerBorder),
              borderRadius: AppBorderRadius.circularSize8Radius(),
            ),
            child: Column(
              children: [
                // Header (always visible).
                InkWell(
                  onTap: () {
                    setState(() {
                      _isExpanded = !_isExpanded;

                      // Reset search and list when opening.
                      if (_isExpanded) {
                        _searchController.clear();
                        _filteredCountries = [
                          ..._favoriteCountries,
                          ..._allCountries.where(
                            (country) => !_favoriteCountryCodes.contains(country.countryCode),
                          ),
                        ];
                      }
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    child: Row(
                      children: [
                        if (_selectedCountry != null) ...[
                          Text(
                            _selectedCountry!.flagEmoji,
                            style: const TextStyle(fontSize: 24),
                          ),
                          const SizedBox(width: 12),
                        ],
                        Expanded(
                          child: Text(
                            _selectedCountry?.name ?? "Select a country",
                            style: TextStyle(
                              color: _selectedCountry == null
                                  ? AppColors.appTextFormFieldHintColor
                                  : Colors.black,
                              fontSize: fontSize,
                            ),
                          ),
                        ),
                        Icon(
                          _isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                          color: AppColors.appTextFormFieldHintColor,
                        ),
                      ],
                    ),
                  ),
                ),
                // Expandable section.
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: _isExpanded ? context.height * height : 0,
                  child: SingleChildScrollView(
                    physics: const NeverScrollableScrollPhysics(),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Colors.grey.shade300),
                        ),
                        color: Colors.white,
                      ),
                      height: context.height * height,
                      child: Column(
                        children: [
                          // Search field.
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: NewAppTextFormField(
                              controller: _searchController,
                              isLabelText: false,
                              label: profilePageConfig.search,
                              prefixWidget: const Icon(Icons.search),
                            ),
                          ),
                          // Country list.
                          Expanded(
                            child: ListView.builder(
                              itemBuilder: (ctx, index) {
                                final country = _filteredCountries[index];
                                final isSelected =
                                    _selectedCountry?.countryCode == country.countryCode;
                                final isFavorite =
                                    _favoriteCountryCodes.contains(country.countryCode);

                                return _searchController.text.isEmpty &&
                                        index == _favoriteCountries.length - 1
                                    ? Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          // Favorite country item.
                                          _buildCountryItem(country, isFavorite, isSelected),

                                          // Divider after favorites.
                                          Padding(
                                            padding: const EdgeInsets.symmetric(horizontal: 16),
                                            child: Divider(
                                              color: AppColors.countryPickerBorder,
                                              height: 24,
                                              thickness: 1,
                                            ),
                                          ),
                                        ],
                                      )
                                    : _buildCountryItem(country, isFavorite, isSelected);
                              },
                              itemCount: _filteredCountries.length,
                              padding: EdgeInsets.zero,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
