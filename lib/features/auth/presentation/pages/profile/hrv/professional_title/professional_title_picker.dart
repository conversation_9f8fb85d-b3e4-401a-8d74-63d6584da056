// ignore_for_file: avoid-missing-interpolation, avoid-collection-mutating-methods, prefer-extracting-callbacks

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/remote_config/profile_page_config.dart';

import '../../../../../../../core/remote_config/remote_config_service.dart';
import '../../profile_item.dart';
import '../heart_about_us/profile_item_view.dart';

typedef OnValueChanged = void Function(Map<String, bool> value);

class ProfessionalTitlePicker extends StatefulWidget {
  const ProfessionalTitlePicker({
    this.isReadOnly = false,
    super.key,
    required this.onValueChanged,
    this.selectedValues,
  });

  final bool? isReadOnly;
  final OnValueChanged onValueChanged;
  final Map<String, bool>? selectedValues;

  @override
  State<ProfessionalTitlePicker> createState() => _ProfessionalTitlePickerState();
}

class _ProfessionalTitlePickerState extends State<ProfessionalTitlePicker> {
  final _selectedValues = <String, bool>{};

  @override
  void initState() {
    super.initState();
    if (widget.selectedValues != null) {
      _selectedValues.addAll(widget.selectedValues ?? {});
    }
  }

  void _handleTap(String value) {
    if (_selectedValues.isNotEmpty) {
      _selectedValues.clear();
    }
    _selectedValues[value] = true;

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance<RemoteConfigService>();
    final profilePageConfig = remoteConfigService.profilePageConfig;

    final ProfilePageConfig(
      :dietitian,
      :nurse,
      :other,
      :physician,
      :physiotherapist,
      :psychiatrist,
      :psychologist,
    ) = profilePageConfig;

    final values = [
      {physician: 'Physician'},
      {nurse: 'Nurse'},
      {psychologist: 'Psychologist'},
      {physiotherapist: 'Physiotherapist'},
      {psychiatrist: 'Psychiatrist'},
      {dietitian: 'Dietitian/Nutritionist'},
      {other: 'Other'},
    ];

    final items = values
        .map((value) =>
            ProfileItem(text: value.keys.firstOrNull ?? "", value: value.values.firstOrNull ?? ""))
        .toList();

    return ProfileItemView(
      isReadOnly: widget.isReadOnly,
      items: items,
      onItemTap: (value) {
        if ((widget.isReadOnly ?? false)) return;

        _handleTap(value);
        widget.onValueChanged(_selectedValues);
      },
      selectedValues: _selectedValues,
      title: "What’s your professional title?",
    );
  }
}
