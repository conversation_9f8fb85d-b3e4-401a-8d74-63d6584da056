import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:pinput/pinput.dart';
import 'package:vagustimpro/core/app_config/app_border_radius.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/app_config/app_gaps.dart';
import 'package:vagustimpro/core/app_config/app_sizes.dart';
import 'package:vagustimpro/core/app_config/app_snackbar.dart';
import 'package:vagustimpro/core/app_config/app_text_styles.dart';
import 'package:vagustimpro/core/custom_widgets/loader.dart';
import 'package:vagustimpro/core/enum/assets_enums.dart';
import 'package:vagustimpro/core/extension/context_extension.dart';
import 'package:vagustimpro/core/navigator/app_navigator.dart';
import 'package:vagustimpro/core/navigator/routes/constants/route_constants.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';
import 'package:vagustimpro/core/services/dialog_service.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/pages/login/login_page.dart';
import 'package:vagustimpro/features/auth/presentation/pages/otp/count_down_resend_widget.dart';
import 'package:vagustimpro/features/auth/presentation/pages/otp/otp_dialog_info.dart';
import 'package:vagustimpro/features/auth/presentation/pages/profile/hrv/other/hrv_profile_view.dart';
import 'package:vagustimpro/features/subuser/domain/entities/subuser_entity.dart';
import 'package:vagustimpro/features/time_of_usage/presentation/widgets/app_dialog.dart';

import '../../../../../app/flavor/flavor_config.dart';
import '../../../../../core/app_config/app_strings.dart';
import '../../../../../core/constants/flavor_constants.dart';
import '../../../../landing/presentation/bloc/landing_bloc.dart';
import '../../../../subuser/presentation/widgets/subuser_form/individual/individual_profile_view.dart';
import '../login/redirecting_to_another_app_dialog.dart';
import '../profile/health_professional/professional_profile_view.dart';

class OtpValidatePage extends StatefulWidget {
  const OtpValidatePage({super.key});

  @override
  State<OtpValidatePage> createState() => _OtpValidatePageState();
}

class _OtpValidatePageState extends State<OtpValidatePage> {
  bool isShowingFailedDialog = false;

  void _handleFirstAppLaunch(BuildContext context, String? name, SubuserEntity? subuser) {
    final bundleId =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

    if (FlavorConstants.isHealthPro(bundleId) && (name ?? "").isEmpty) {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        CupertinoPageRoute(builder: (ctx) => ProfessionalProfileView(isEditMode: false)),
        (route) => false,
      ));
    } else if (FlavorConstants.isIndividual(bundleId) && subuser == null) {
      unawaited(Navigator.of(context).pushAndRemoveUntil(
        CupertinoPageRoute(
          builder: (ctx) => IndividualProfileView(isEditMode: false),
        ),
        (route) => false,
      ));
    } else if (FlavorConstants.isHrv(bundleId) && subuser == null) {
      unawaited(
        Navigator.of(context).pushAndRemoveUntil(
          CupertinoPageRoute(builder: (ctx) => HrvProfileView()),
          (route) => false,
        ),
      );
    } else {
      context.read<LandingBloc>().add(ChangeBottomBarVisibility(isShowBottomBar: true));

      unawaited(
        Navigator.of(context).pushNamedAndRemoveUntil(RouteConstants.landingPage, (route) => false),
      );
    }
  }

  static void _redirectingDialogThen(BuildContext ctx) {
    if (ctx.mounted) {
      ctx.read<AuthBloc>().add(SignOutEvent());
    }
  }

  void _showRedirectingAnotherAppDialog(BuildContext ctx, RedirectingToAnotherAppState state) {
    WidgetsBinding.instance.addPostFrameCallback((_) => _handlePostFrameCallback(ctx, state));
  }

  void _handlePostFrameCallback(BuildContext ctx, RedirectingToAnotherAppState state) async {
    final response =
        await RedirectingToAnotherAppDialog(context: ctx, userType: state.user.userType).show;

    if (response == null && ctx.mounted) _redirectingDialogThen(ctx);
  }

  void _showDialog() {
    AppDialog(context: context, child: OtpDialogInfo()).show;
  }

  final otpController = TextEditingController();

  void onHandleValidateOtp() {
    if (otpController.text.isNotEmpty) {
      isShowingFailedDialog = false;

      context.read<AuthBloc>().isRedirectLandingPage = true;
      context.read<AuthBloc>().add(ValidateOtpEvent(otp: otpController.text));
    } else {
      final dialogService = GetIt.instance.get<DialogService>();

      final remoteConfigService = GetIt.instance.get<RemoteConfigService>();
      final loginPageConfig = remoteConfigService.loginPageConfig;
      dialogService.showDialog(
        confirmButtonText: loginPageConfig.enterYourEmailAlertConfirmBtnTxt,
        message: "Please enter OTP",
        navigatorKey: AppNavigator.navigatorKey,
        title: loginPageConfig.enterYourEmailAlertTitle,
      );
    }
  }

  Widget buildOtpForm(AuthState authState) {
    String email = authState is EmailSentSuccess ? (authState).email : "";

    final size = MediaQuery.sizeOf(context);
    final height = size.height;

    final heightOtp = 72;
    final widthOtp = 60;
    final fontSize = 20;
    final otpLength = 5;
    final bundleId =
        GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;
    final FocusNode focusNode = FocusNode();

    return SingleChildScrollView(
      child: Padding(
        padding: context.paddingMedium,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppGaps.instance.gapVS32,
            Center(
              child: FlavorConstants.isHrv(bundleId)
                  ? AssetsEnums.hrvOtpImage.toSvg()
                  : AssetsEnums.otpImage.toSvg(),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppGaps.instance.gapVS32,
                Text("Enter the Code", style: AppTextStyles.otpTitle),
                AppGaps.instance.gapVS8,
                Text(
                  "Enter the login code we just sent to your email: $email",
                ),
                AppGaps.instance.gapVS16,
                Center(
                  child: Pinput(
                    controller: otpController,
                    defaultPinTheme: PinTheme(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.appTextNewFormFieldBorderColor),
                        borderRadius: AppBorderRadius.circularSize12Radius(),
                      ),
                      height: heightOtp.sp,
                      textStyle: TextStyle(fontSize: fontSize.sp),
                      width: widthOtp.sp,
                    ),
                    focusNode: focusNode,
                    focusedPinTheme: PinTheme(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.appTextFormFieldLabelColor),
                        borderRadius: AppBorderRadius.circularSize12Radius(),
                      ),
                      height: heightOtp.sp,
                      width: widthOtp.sp,
                    ),
                    length: otpLength,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    onCompleted: (value) {
                      otpController.text = value;
                      onHandleValidateOtp();
                    },
                    onTapOutside: (event) => focusNode.unfocus(),
                  ),
                ),
                AppGaps.instance.gapVS16,
                CountdownResendWidget(
                  onResendPress: () =>
                      context.read<AuthBloc>().add(SignInWithEmailEvent(email: email)),
                ),
                SizedBox(height: height * AppSizes.defaultSpacer),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final remoteConfigService = GetIt.instance.get<RemoteConfigService>();
    final loginPageConfig = remoteConfigService.loginPageConfig;
    final size = MediaQuery.sizeOf(context);
    final height = size.height;
    AuthState authState = context.read<AuthBloc>().state;
    String email = authState is EmailSentSuccess ? (authState).email : "";

    final iconSize = 24;

    return Scaffold(
      appBar: CupertinoNavigationBar(
        backgroundColor: AppColors.backgroundColor,
        leading: GestureDetector(
          onTap: () {
            unawaited(Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (ctx) => LoginPage()),
              (route) => false,
            ));
          },
          child: Icon(Icons.arrow_back_ios, size: iconSize.sp),
        ),
        middle: const Text(
          'Sign In',
          style: TextStyle(fontSize: 17, fontWeight: FontWeight.w600),
        ),
        trailing: IconButton(
          icon: Icon(
            Icons.info_outline_rounded,
            color: AppColors.neoBlue,
            size: 24.sp,
          ),
          onPressed: () => _showDialog(),
        ),
      ),
      body: SafeArea(
        child: BlocConsumer<AuthBloc, AuthState>(
          builder: (ctx, state) {
            debugPrint("Building with state otp page : $state");

            return (state is AuthLoading) || (state is AuthSuccess)
                ? const Loader()
                : buildOtpForm(state);
          },
          listenWhen: (previous, current) {
            if (previous is GetUserSuccess && current is GetUserSuccess) {
              return false;
            }
            return true;
          },
          listener: (ctx, state) {
            if (state is AuthLoading) {
              if (kDebugMode) {
                print("AuthLoading");
              }
            } else if (state is AuthSuccess) {
            } else if (state is GetUserSuccess) {
              final bundleId = GetIt.instance
                  .get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName)
                  .bundleId;

              if (FlavorConstants.isHrv(bundleId)) {
                context.read<LandingBloc>().add(ChangeBottomBarVisibility(isShowBottomBar: true));
              }

              _handleFirstAppLaunch(context, state.user.name, state.user.defaultSubuser);
            } else if (state is RedirectingToAnotherAppState) {
              _showRedirectingAnotherAppDialog(context, state);
            } else if (state is AuthFailure) {
              AppSnackbar.showErrorSnackbar(context, message: state.message);
              unawaited(Navigator.of(context).pushNamedAndRemoveUntil(
                RouteConstants.loginPage,
                (route) => false,
              ));
            } else if (state is ValidateOtpFailure) {
              final dialogService = GetIt.instance.get<DialogService>();
              otpController.clear();
              if (!isShowingFailedDialog) {
                dialogService.showDialog(
                  confirmButtonText: loginPageConfig.enterYourEmailAlertConfirmBtnTxt,
                  message: state.message,
                  navigatorKey: AppNavigator.navigatorKey,
                  title: "Error",
                );
                isShowingFailedDialog = true;
              }
            }
          },
        ),
      ),
    );
  }
}
