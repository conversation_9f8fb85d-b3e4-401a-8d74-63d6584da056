// ignore_for_file: handle-throwing-invocations, prefer-class-destructuring, prefer-moving-to-variable

import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// Import 'package:flutter_smartlook/flutter_smartlook.dart';.
import 'package:google_sign_in/google_sign_in.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:rxdart/rxdart.dart';
import 'package:vagustimpro/core/navigator/app_navigator.dart';
import 'package:vagustimpro/core/app_config/app_strings.dart';
import 'package:vagustimpro/core/navigator/routes/constants/route_constants.dart';
import 'package:vagustimpro/core/services/secure_storage_service.dart';
import 'package:vagustimpro/core/usecase/no_params.dart';

import 'package:vagustimpro/features/auth/domain/entities/user_entity.dart';
import 'package:vagustimpro/features/auth/domain/params/sign_in_with_deep_link_params.dart';
import 'package:vagustimpro/features/auth/domain/params/sign_in_with_email_params.dart';
import 'package:vagustimpro/features/auth/domain/params/update_user_profile_params.dart';
import 'package:vagustimpro/features/auth/domain/params/update_user_role_params.dart';
import 'package:vagustimpro/features/auth/domain/params/validate_otp_params.dart';
import 'package:vagustimpro/features/auth/domain/usecases/delete_user_usecase.dart';
import 'package:vagustimpro/features/auth/domain/usecases/get_user_usecase.dart';
import 'package:vagustimpro/features/auth/domain/usecases/sign_in_with_apple_usecase.dart';
import 'package:vagustimpro/features/auth/domain/usecases/sign_in_with_deep_link_usecase.dart';
import 'package:vagustimpro/features/auth/domain/usecases/sign_in_with_email_usecase.dart';
import 'package:vagustimpro/features/auth/domain/usecases/sign_in_with_google_usecase.dart';
import 'package:vagustimpro/features/auth/domain/usecases/update_user_profile_usecase.dart';
import 'package:vagustimpro/features/auth/domain/usecases/update_user_role_usecase.dart';
import 'package:vagustimpro/features/auth/domain/usecases/validate_otp_usecase.dart';

import 'package:vagustimpro/features/auth/presentation/pages/login/login_page.dart';

import '../../../../core/helpers/analytic_helper.dart';
import '../../../../core/utils/map_utils.dart';

part 'auth_event.dart';
part 'auth_state.dart';
part 'auth_bloc_mixin.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> with AuthBlocMixin {
  bool? isRedirectLandingPage;
  bool? isOnlyNameAndSurnameChanged = false;

  final SignInWithEmailUseCase _signInWithEmail;
  final SecureStorageService _secureStorageService;
  final SignInWithAppleUseCase _signInWithAppleUseCase;
  final SignInWithGoogleUseCase _signInWithGoogle;
  final SignInWithDeepLinkUseCase _signInWithDeepLink;
  final GetUserUseCase _getUser;
  final UpdateUserRoleUseCase _updateUserRole;
  final UpdateUserProfileUseCase _updateUserProfile;
  final ValidateOtpUsecase _validateOtpUsecase;
  final DeleteUserUseCase _deleteUser;

  AuthBloc({
    required DeleteUserUseCase deleteUser,
    required GetUserUseCase getUser,
    required SecureStorageService secureStorageService,
    required SignInWithAppleUseCase signInWithApple,
    required SignInWithDeepLinkUseCase signInWithDeepLink,
    required SignInWithEmailUseCase signInWithEmail,
    required SignInWithGoogleUseCase signInWithGoogle,
    required UpdateUserProfileUseCase updateUserProfile,
    required UpdateUserRoleUseCase updateUserRole,
    required ValidateOtpUsecase validateOtp,
  })  : _signInWithEmail = signInWithEmail,
        _secureStorageService = secureStorageService,
        _signInWithAppleUseCase = signInWithApple,
        _signInWithGoogle = signInWithGoogle,
        _signInWithDeepLink = signInWithDeepLink,
        _getUser = getUser,
        _updateUserRole = updateUserRole,
        _updateUserProfile = updateUserProfile,
        _validateOtpUsecase = validateOtp,
        _deleteUser = deleteUser,
        super(const AuthInitial()) {
    on<SignInWithEmailEvent>(_onSingInWithEmail);
    on<SignInWithAppleEvent>(_onSignInWithApple);
    on<SignInWithGoogleEvent>(_onSignInWithGoogle);
    on<SingInWithDeepLinkEvent>(_onSingInWithDeepLink);
    on<UpdateUserEvent>(_onUpdateUser);
    on<GetUserEvent>(
      _onGetUser,
      transformer: (events, mapper) {
        return events
            .debounceTime(const Duration(milliseconds: 300))
            .flatMap((event) => mapper(event));
      },
    );
    on<ProfileUpdateEvent>(_onUpdateUserProfile);
    on<ValidateOtpEvent>(_onValidateOtp);
    on<ProfileFormChangedEvent>(_onFormChanged);
    on<DeleteAccountEvent>(_onDeleteAccount);
    on<UserRefreshStateEvent>((event, emit) {
      if (currentUser != null) {
        debugPrint("UserRefreshStateEvent emitting GetUserSuccess");
        emit(GetUserSuccess(event.currentUser ?? currentUser!));
      }
    });
    on<SignOutEvent>((event, emit) async {
      emit(const AuthLoading());
      AnalyticHelper.mixpanel?.track("Sign Out", properties: {"email": currentUser?.email});
      currentUser = null;

      final auth = FirebaseAuth.instance;

      final user = auth.currentUser;

      if (user != null) {
        for (final userInfo in user.providerData) {
          switch (userInfo.providerId) {
            case AppStrings.googleCom:
              await GoogleSignIn().signOut();
              break;

            case AppStrings.appleCom:
              // Apple Sign-In'den çıkış işlemi için özel bir fonksiyon kullanmanıza gerek yoktur.
              // Ancak, güvenlik gereksinimlerinize bağlı olarak Apple oturumlarını manuel olarak yönetmek isteyebilirsiniz.
              break;
            // Diğer sağlayıcılar için ek işlemler ekleyebilirsiniz.
          }
        }
        if (!(event.isDeletedAccount ?? false)) await auth.signOut();
      }
      if (!await Purchases.isAnonymous) {
        await Purchases.logOut();
      }
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AppNavigator.navigatorKey.currentState?.pushAndRemoveUntil(
          CupertinoPageRoute<Widget>(builder: (context) => const LoginPage()),
          (route) => false,
        );
      });
      emit(const AuthInitial());
    });
  }

  void _onSingInWithEmail(
    SignInWithEmailEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final res = await _signInWithEmail.calculate(
      SignInWithEmailParams(email: event.email),
    );

    await res.fold(
      (fail) {
        emit(AuthFailure(fail.message));
      },
      (user) async {
        await _secureStorageService.saveUserEmail(event.email);
        // await Smartlook.instance.user.session.openNew();
        //  await Smartlook.instance.user.setIdentifier(event.email);
        //  await Smartlook.instance.user.setEmail(event.email);
        emit(EmailSentSuccess(event.email));
      },
    );
  }

  Future<void> _onSignInWithApple(
    SignInWithAppleEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final res = await _signInWithAppleUseCase.calculate(const NoParams());

    res.fold(
      (failure) {
        emit(AuthFailure(failure.message));
      },
      (user) {
        emit(AuthSuccess(user));
      },
    );
  }

  Future<void> _onSignInWithGoogle(
    SignInWithGoogleEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final res = await _signInWithGoogle.calculate(const NoParams());

    res.fold(
      (failure) {
        emit(AuthFailure(failure.message));
      },
      (user) {
        emit(AuthSuccess(user));
      },
    );
  }

  void _onSingInWithDeepLink(
    SingInWithDeepLinkEvent event,
    Emitter<AuthState> emit,
  ) async {
    final lastSavedEmail = await _secureStorageService.getUserEmail();
    final res = await _signInWithDeepLink.calculate(
      SignInWithDeepLinkParams(
        deepLink: event.deepLink,
        email: lastSavedEmail!,
      ),
    );

    res.fold(
      (fail) {
        emit(AuthFailure(fail.message));
      },
      (user) {
        add(GetUserEvent(isAuthLoading: false));

        emit(AuthSuccess(user));
      },
    );
  }

  Future<void> _onUpdateUser(
    UpdateUserEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final res = await _updateUserRole.calculate(UpdateUserRoleParams(userType: event.userType));

    res.fold(
      (fail) {
        emit(AuthFailure(fail.message));
      },
      (user) {
        if (currentUser != null) {
          currentUser!.userType = event.userType;
        }

        add(GetUserEvent());
        emit(UpdateUserSuccess(user));
      },
    );
  }

  Future<void> _onGetUser(GetUserEvent event, Emitter<AuthState> emit) async {
    debugPrint("_onGetUser method triggered");

    if (state is! AuthLoading && (event.isAuthLoading ?? true)) {
      emit(const AuthLoading());
    }

    final res = await _getUser.calculate(const NoParams());

    res.fold(
      (fail) {
        emit(AuthFailure(fail.message));
      },
      (user) {
        currentUser = user;

        emit(GetUserSuccess(user));
      },
    );
  }

  Future<void> _onUpdateUserProfile(
    ProfileUpdateEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final res = await _updateUserProfile.calculate(UpdateUserProfileParams(
      country: event.country ?? "",
      hereAboutUs: event.hereAboutUs,
      institution: event.institutionName ?? "",
      name: event.name ?? "",
      otherPou: event.otherPou ?? [],
      purposeOfUsage: event.purposeOfUsage ?? {},
      specialization: event.specialization ?? "",
      surname: event.surname ?? "",
      title: event.title ?? "",
      userType: event.userType,
    ));

    // Sends an analytics event to track user.
    AnalyticHelper.instance.track(AppStrings.updateProfileReq);

    res.fold(
      (fail) {
        // Sends an analytics event to track user.
        AnalyticHelper.instance.track(
          AppStrings.updateProfileFailed,
          properties: AppStrings.failedProperties(fail.message),
        );

        emit(AuthFailure(fail.message));
      },
      (user) {
        // Sends an analytics event to track user.
        AnalyticHelper.instance.track(AppStrings.updateProfileSucces);
        AnalyticHelper.instance.analyticUpdateProfile(event);

        currentUser = user;
        add(UserRefreshStateEvent());
        emit(UpdateUserSuccess(user));
      },
    );
  }

  Future<void> _onValidateOtp(ValidateOtpEvent event, Emitter<AuthState> emit) async {
    emit(const AuthLoading());
    final lastSavedEmail = await _secureStorageService.getUserEmail();
    final res = await _validateOtpUsecase
        .calculate(ValidateOtpParams(email: lastSavedEmail!, otp: event.otp));
    // Sends an analytics event to track user.
    AnalyticHelper.instance.track(AppStrings.otpButtonReq);

    res.fold(
      (fail) {
        // Sends an analytics event to track user.
        AnalyticHelper.instance.track(
          AppStrings.otpButtonFailed,
          properties: AppStrings.failedProperties(fail.message),
        );
        emit(ValidateOtpFailure(fail.message));
      },
      (passwordlessLink) {
        // Sends an analytics event to track user.
        AnalyticHelper.instance.track(AppStrings.otpButtonSucces);
        add(SingInWithDeepLinkEvent(deepLink: Uri.parse(passwordlessLink)));
      },
    );
  }

  void _onFormChanged(
    ProfileFormChangedEvent event,
    Emitter<AuthState> emit,
  ) {
    final isEnableBtn = event.name != (event.user?.name) ||
        event.surname != (event.user?.surname) ||
        event.institutionName != (event.user?.institution) ||
        event.specialization != (event.user?.specialization) ||
        event.title != (event.user?.title) ||
        event.country != (event.user?.country) ||
        !MapUtils.mapEquals(event.selectedValues, event.user?.purposeOfUsage ?? {});

    // Check if ONLY name and surname have changed, while other fields remained the same.
    isOnlyNameAndSurnameChanged =
        // First check that either name or surname (or both) have changed.
        (event.name != (event.user?.name) || event.surname != (event.user?.surname)) &&
            // Then verify all other fields remained unchanged.
            event.institutionName == (event.user?.institution) &&
            event.specialization == (event.user?.specialization) &&
            event.title == (event.user?.title) &&
            event.country == (event.user?.country) &&
            MapUtils.mapEquals(event.selectedValues, event.user?.purposeOfUsage ?? {});

    debugPrint('isEnableBtn: $isEnableBtn');
    debugPrint('isOnlyNameAndSurnameChanged: $isOnlyNameAndSurnameChanged');

    bool isFormValid = false;

    if (isEnableBtn) {
      isFormValid = event.name.isNotEmpty &&
          event.surname.isNotEmpty &&
          event.institutionName.isNotEmpty &&
          event.specialization.isNotEmpty &&
          event.title.isNotEmpty &&
          event.country.isNotEmpty &&
          (event.hereAboutUs.isNotEmpty || event.isEditMode) &&
          event.selectedValues.isNotEmpty &&
          (!event.isOtherSelected || event.otherPou.isNotEmpty);

      debugPrint('isFormValid: $isFormValid');
    }

    emit(ProfileButtonState(isFormValid));
  }

  Future<void> _onDeleteAccount(
    DeleteAccountEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final res = await _deleteUser.calculate(const NoParams());

    res.fold(
      (fail) async {
        emit(AuthFailure(fail.message));
      },
      (_) async {
        emit(const AuthInitial());
      },
    );
  }
}
