part of 'auth_bloc.dart';

@immutable
sealed class AuthState {
  const AuthState();
}

final class AuthInitial extends AuthState {
  const AuthInitial();
}

final class AuthLoading extends AuthState {
  const AuthLoading();
}

final class AuthSuccess extends AuthState {
  final UserEntity user;
  const AuthSuccess(this.user);
}

final class AuthFailure extends AuthState {
  final String message;
  const AuthFailure(this.message);
}

final class AuthUserCredentialsSuccess extends AuthState {
  final String email;
  const AuthUserCredentialsSuccess(this.email);
}

final class AuthUserCredentialsFailure extends AuthState {
  const AuthUserCredentialsFailure();
}

final class EmailSentSuccess extends AuthState {
  final String email;
  const EmailSentSuccess(this.email);
}

final class GetUserSuccess extends AuthState {
  final UserEntity user;
  const GetUserSuccess(this.user);
}

final class UpdateUserSuccess extends AuthState {
  final UserEntity user;
  const UpdateUserSuccess(this.user);
}

final class ValidateOtpSuccess extends AuthState {
  final String passwordlessLink;
  const ValidateOtpSuccess(this.passwordlessLink);
}

final class ValidateOtpFailure extends AuthState {
  final String message;
  const ValidateOtpFailure(this.message);
}

final class ProfileButtonState extends AuthState {
  final bool isFormValid;
  const ProfileButtonState(this.isFormValid);
}

final class RedirectingToAnotherAppState extends AuthState {
  final UserEntity user;
  const RedirectingToAnotherAppState(this.user);
}
