// ignore_for_file: prefer-declaring-const-constructor, prefer-abstract-final-static-class, avoid-ignoring-return-values

import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';
import 'package:vagustimpro/core/remote_config/remote_config_service.dart';

class AppSnackbar {
  static final commonPageConfig = GetIt.instance.get<RemoteConfigService>().commonPageConfig;
  static AppSnackbar? _instance;
  static AppSnackbar get instance => _instance ??= AppSnackbar._();
  AppSnackbar._();

  static void showSnackbar(BuildContext context, {Color? color, required String message}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(backgroundColor: color, content: Text(message)),
    );
  }

  static void showErrorSnackbar(BuildContext context, {required String message}) {
    showSnackbar(context, color: AppColors.errorColor, message: message);
  }

  static void showSucces(BuildContext context, {required String message}) {
    showSnackbar(context, color: AppColors.succesColor, message: message);
  }

  static void showErrorSurveyEmptySelectedOption(BuildContext context) {
    final message = commonPageConfig.pleaseSelectOneOfGivenOptions;

    showSnackbar(context, color: AppColors.errorColor, message: message);
  }

  static void ratingSucces(BuildContext context) {
    final message = commonPageConfig.congratulations1MonthPremium;

    showSucces(context, message: message);
  }

  static void ratingError(BuildContext context) {
    final message = commonPageConfig.rateForDesc;
    showErrorSnackbar(context, message: message);
  }
}
