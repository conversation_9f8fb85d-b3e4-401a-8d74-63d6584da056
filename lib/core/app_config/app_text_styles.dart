// ignore_for_file: avoid-long-files

import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_colors.dart';

abstract final class AppTextStyles {
  static const title = TextStyle(
    color: AppColors.darkTextColor,
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  static const description = TextStyle(
    color: AppColors.lightTextColor,
    fontSize: 14,
    fontWeight: FontWeight.normal,
  );

  static const navigationBarHeader = TextStyle(
    color: AppColors.navigationBarHeaderColor,
  );
  // From app_theme.dart.
  static final selectSessionTypeHeader = TextStyle(
    color: AppColors.navigationBarHeaderColor,
    fontSize: 24.sp,
    fontWeight: FontWeight.w500,
  );

  // From app_theme.dart.
  static final surveyAppBar = TextStyle(
    color: AppColors.surveyAppBarTitleColor,
    fontSize: 16.sp,
    fontWeight: FontWeight.bold,
  );

  // From app_theme.dart.
  static final surveyQuestionTitle = TextStyle(
    color: AppColors.surveyAppBarTitleColor,
    fontSize: 26.sp,
    fontWeight: FontWeight.bold,
  );
  static final surveyStep = TextStyle(
    color: AppColors.surveyAppBarTitleColor.withValues(alpha: 0.5),
    fontSize: 14.sp,
    fontWeight: FontWeight.bold,
  );
  static final surveyOption = TextStyle(
    color: AppColors.surveyAppBarTitleColor,
    fontSize: 14.sp,
    fontWeight: FontWeight.bold,
  );

  // From app_theme.dart.
  static final appDialogDescriptionStyle = TextStyle(
    color: AppColors.dialogDescriptionStyleColor,
    fontSize: 16.sp,
    fontWeight: FontWeight.normal,
  );
  static final appDialogTitleStyle = TextStyle(
    color: AppColors.dialogDescriptionStyleColor,
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );
  // From app_theme.dart.
  static final bottomBarUnSelectedTextStyle = TextStyle(
    color: AppColors.dialogDescriptionStyleColor,
    fontSize: 12.sp,
  );

  static final bottomBarSelectedTextStyle = TextStyle(
    color: AppColors.bottomBarSelectedTextStyleColor,
    fontSize: 12.sp,
    fontWeight: FontWeight.w600,
  );

  static final insightChartTextStyle = TextStyle(
    color: AppColors.bottomBarSelectedTextStyleColor,
    fontSize: 12.sp,
    fontWeight: FontWeight.bold,
  );
  static final insightChartTitleTextStyle = TextStyle(
    color: AppColors.bottomBarSelectedTextStyleColor,
    fontSize: 18.sp,
    fontWeight: FontWeight.bold,
  );
  static final insightInfoCardTextStyle = TextStyle(
    color: AppColors.bottomBarSelectedTextStyleColor,
    fontSize: 16.sp,
  );

  static final insightConnectButtonTextStyle = TextStyle(
    color: AppColors.insightPageChartLineColor,
    fontSize: 12.sp,
    fontWeight: FontWeight.bold,
  );

  static final stimulationControlOnBoardText = TextStyle(
    color: AppColors.stimulationControlWidgetTutorialTextColor,
    fontSize: 18.sp,
    fontWeight: FontWeight.w600,
  );

  static final stimulationControlOnBoardSkipText = TextStyle(
    color: AppColors.stimulationControlWidgetTutorialTextColor,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );

  static const stimulationControlEarText = TextStyle(
    color: AppColors.bottomBarSelectedItemColor,
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );
  static final stimulationControlRemainingText = TextStyle(
    color: AppColors.hrvTitle,
    fontSize: 24.sp,
    fontWeight: FontWeight.bold,
  );
  static const stimulationControlPanelPauseText = TextStyle(
    color: AppColors.stimulationControlPauseColor,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );
  static const stimulationControlPanelStartText = TextStyle(
    color: AppColors.controlPanelStartButton,
    fontSize: 16,
    fontWeight: FontWeight.bold,
  );

  static const stimulationControlPanelAdjustParameterText = TextStyle(
    color: AppColors.stimulationControlTitle,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static const stimulationWalktroughTutorialText = TextStyle(
    color: AppColors.stimulationControlWidgetWalktroughTutorialTextColor,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static final healthGraphicText = TextStyle(
    color: AppColors.healthDataGraphicColor,
    fontSize: 14.sp,
    fontWeight: FontWeight.bold,
  );

  static const healthDataSleepText = TextStyle(
    color: AppColors.healthDataGraphicTextColor,
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );

  static const healthDataInfoText = TextStyle(
    color: AppColors.healthDataGraphicInfoColor,
    fontSize: 14,
  );

  static const comingSoonText = TextStyle(
    color: AppColors.healthDataGraphicTextColor,
    fontSize: 22,
  );

  static final surveyCompletedDescriptionTextStyle = TextStyle(
    color: AppColors.surveyCompletedDescriptionTextColor,
    fontSize: 14.sp,
    fontWeight: FontWeight.normal,
  );

  static const healthDataAverageText = TextStyle(
    color: AppColors.healthDataAverageTextColor,
    fontSize: 18,
    fontWeight: FontWeight.w900,
  );

  static const healthGraphicNoDataText = TextStyle(
    color: AppColors.healthDataGraphicDescriptionColor,
    fontSize: 24,
    fontWeight: FontWeight.w400,
  );

  static const healthGraphicSingleDataText = TextStyle(
    color: AppColors.healthDataAverageTextColor,
    fontSize: 16,
    fontWeight: FontWeight.bold,
  );

  static const comprehensiveLoadingTitle = TextStyle(
    color: AppColors.healthDataAverageTextColor,
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );
  static const comprehensiveLoadingDescription = TextStyle(
    color: AppColors.healthDataGraphicInfoColor,
    fontSize: 16,
  );

  static final insightSummaryDescription = TextStyle(
    color: AppColors.insightSummaryDesc,
    fontSize: 16.sp,
  );

  static final settingsTitle = TextStyle(
    color: AppColors.primaryColor,
    fontSize: 16.sp,
  );

  static final profileBringAFriendDesc = TextStyle(
    color: AppColors.primaryColor,
    fontSize: 10.sp,
  );

  static final dailyReminderTime = TextStyle(
    color: AppColors.healthDataAverageTextColor,
    fontSize: 20.sp,
    fontWeight: FontWeight.w600,
  );

  static final timePickerSaveButton = TextStyle(
    color: AppColors.neoBlue,
    fontSize: 16.sp,
    fontWeight: FontWeight.bold,
  );

  static const headPhoneLevel = TextStyle(
    color: AppColors.headPhoneLevelColor,
    fontSize: 14,
    fontWeight: FontWeight.bold,
  );

  static final insightMyHistoryTitle = TextStyle(
    color: AppColors.insightMyHistoryColor,
    fontSize: 18.sp,
    fontWeight: FontWeight.bold,
  );
  static final insightMyHistoryDesc = TextStyle(
    color: AppColors.insightMyHistoryDescColor,
    fontSize: 16.sp,
  );

  static final individualHomeHeaderTitle = TextStyle(
    color: AppColors.individualHomeHeaderTitle,
    fontSize: 32.sp,
    fontWeight: FontWeight.bold,
  );

  static final individualHomeHeaderDesc = TextStyle(
    color: AppColors.individualHomeHeaderTitle,
    fontSize: 12.sp,
    fontWeight: FontWeight.w500,
  );

  static final individualHomeStoryTitle = TextStyle(
    color: AppColors.individualHomeStoryTitle,
    fontSize: 20.sp,
    fontWeight: FontWeight.w600,
  );

  static final individualHomeStoryDesc = TextStyle(
    color: AppColors.individualHomeStoryDesc,
    fontSize: 12.sp,
    fontWeight: FontWeight.w600,
  );

  static final individualHomeInStoryTitle = TextStyle(
    color: AppColors.individualHomeStoryBackgroundColor,
    fontSize: 32.sp,
    fontWeight: FontWeight.w900,
  );

  static final individualHomeInStoryDesc = TextStyle(
    color: AppColors.individualHomeStoryBackgroundColor,
    fontSize: 24.sp,
    fontWeight: FontWeight.w600,
  );

  static final individualHomePremiumButtonTitle = TextStyle(
    color: AppColors.individualHomeHeaderTitle,
    fontSize: 10.sp,
    fontWeight: FontWeight.w300,
  );

  static final individualHomePremiumButtonDesc = TextStyle(
    color: AppColors.individualHomeHeaderTitle,
    fontSize: 16.sp,
    fontWeight: FontWeight.w500,
  );

  static final vagusCoachTitle = TextStyle(
    color: AppColors.vagusCoachTitle,
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );
  static final vagusCoachDesc = TextStyle(
    color: AppColors.vagusCoachDesc,
    fontSize: 14.sp,
    fontWeight: FontWeight.w500,
  );

  static final whereToFindDoctorIdTitle = TextStyle(
    color: AppColors.individualHomeStoryBackgroundColor,
    fontSize: 18.sp,
    fontWeight: FontWeight.bold,
  );
  static final whereToFindDoctorIdDesc = TextStyle(
    color: AppColors.vagusCoachDesc,
    fontSize: 14.sp,
    fontWeight: FontWeight.w500,
  );

  static final healthProYourId = TextStyle(
    color: AppColors.headPhoneLevelColor,
    fontSize: 24.sp,
    fontWeight: FontWeight.w600,
  );

  static final remoteMonitoringPendingTitle = TextStyle(
    color: AppColors.individualHomeStoryBackgroundColor,
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );

  static final remoteMonitoringTime = TextStyle(
    color: AppColors.remoteMonitoringTimeColor,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );

  static final remoteMonitoringTabText = TextStyle(
    color: AppColors.appleButtonTextColor,
    fontSize: 12.sp,
    fontWeight: FontWeight.bold,
  );

  static final hrvBpmTitle = TextStyle(
    color: AppColors.hrvBpm,
    fontSize: 40.sp,
    fontWeight: FontWeight.bold,
  );

  static final hrvCalibrationTitle = TextStyle(
    color: AppColors.hrvBpm,
    fontSize: 18.sp,
    fontWeight: FontWeight.bold,
  );

  static final hrvBpmDesc = TextStyle(
    color: AppColors.hrvBpm.withValues(alpha: 0.5),
    fontSize: 12.sp,
    fontWeight: FontWeight.bold,
  );

  static final hrvResultTitle = TextStyle(
    color: AppColors.hrvResultTitle,
    fontSize: 18.sp,
    fontWeight: FontWeight.bold,
  );
  static final hrvResultTitleBpm = TextStyle(
    color: AppColors.hrvTitle,
    fontSize: 60.sp,
    fontWeight: FontWeight.w700,
  );
  static final hrvResultItemTitle = TextStyle(
    color: AppColors.hrvTitle,
    fontSize: 20.sp,
    fontWeight: FontWeight.w700,
  );
  static final hrvResultDesc = TextStyle(
    color: AppColors.hrvResultDesc,
    fontSize: 16.sp,
    fontWeight: FontWeight.w500,
  );
  static final multipleDeviceScan = TextStyle(
    color: AppColors.dialogDescriptionStyleColor,
    fontSize: 14.sp,
  );

  static final multipleDeviceFound = TextStyle(
    color: AppColors.multipleDeviceTitle,
    fontSize: 16.sp,
  );

  static final multipleDeviceAppbarTitle = TextStyle(
    color: AppColors.surveyAppBarTitleColor,
    fontSize: 20.sp,
    fontWeight: FontWeight.w600,
  );

  static final multipleDeviceItemName = TextStyle(
    color: AppColors.multipleDeviceItemName,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );

  static final multipleDeviceDetailTitle = TextStyle(
    color: AppColors.multipleDeviceItemName,
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );

  static final multipleDeviceDetailField = TextStyle(
    color: AppColors.multipleDeviceItemPersonIconColor,
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );

  static final multipleDeviceSelectUserField = TextStyle(
    color: AppColors.multipleDeviceItemPersonIconColor,
    fontSize: 14.sp,
  );

  static final multipleDeviceTitle = TextStyle(
    color: AppColors.multipleDevicesTitle,
    fontSize: 15.sp,
    fontWeight: FontWeight.w600,
  );

  static final hrvItemDesc = TextStyle(
    color: AppColors.hrvItemText,
    fontSize: 14.sp,
  );

  static final hrvStartCamera = TextStyle(
    color: AppColors.hrvStartCamera,
    fontSize: 20.sp,
    fontWeight: FontWeight.bold,
  );

  static final hrvStartCameraDesc = TextStyle(
    color: AppColors.hrvStartCamera,
    fontSize: 16.sp,
    fontWeight: FontWeight.normal,
  );

  static final hrvBottomBarUnselected = TextStyle(
    color: AppColors.hrvBottomBarUnselected,
    fontSize: 12,
    fontWeight: FontWeight.normal,
  );

  static final hrvProfileText = TextStyle(
    color: AppColors.hrvProfileText,
    fontSize: 20.sp,
    fontWeight: FontWeight.bold,
  );

  static final hrvProfileWeightBar = TextStyle(
    color: AppColors.hrvProfileTabSelected,
    fontSize: 16.sp,
    fontWeight: FontWeight.w500,
  );

  static final hrvInsightsTitle = TextStyle(
    color: AppColors.hrvStartCamera,
    fontSize: 18.sp,
    fontWeight: FontWeight.w600,
  );

  static final otpTitle = TextStyle(
    color: AppColors.otpTitle,
    fontSize: 26.sp,
    fontWeight: FontWeight.bold,
  );

  static final profilePurposeOfUsageOtherItem = TextStyle(
    color: AppColors.pureWhite,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );

  static final aiDrivenSurveyTitle = TextStyle(
    color: AppColors.surveyCompletedDescriptionTextColor,
    fontSize: 16.sp,
    fontWeight: FontWeight.normal,
  );

  static final presetProgramTitle = TextStyle(
    color: AppColors.pureWhite,
    fontSize: 16.sp,
    fontWeight: FontWeight.bold,
  );

  static final presetProgramTag = TextStyle(
    color: AppColors.pureWhite,
    fontSize: 12.sp,
    fontWeight: FontWeight.bold,
  );

  static final sessionTypeTitle = TextStyle(
    color: AppColors.sessionTypeTitle,
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: -0.2,
  );

  static final sessionTypeDesc = TextStyle(
    color: AppColors.sessionTypeDescription,
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
  );

  // Parameter control widget styles.
  static final paramControlTitle = TextStyle(
    color: AppColors.paramControlTitle,
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );

  static final paramControlValue = TextStyle(
    color: AppColors.sessionTypeTitle,
    fontSize: 24.sp,
    fontWeight: FontWeight.bold,
  );

  static final paramControlUnit = TextStyle(
    color: AppColors.sessionTypeDescription,
    fontSize: 14.sp,
    fontWeight: FontWeight.w500,
  );

  static final paramControlRange = TextStyle(
    color: AppColors.darkTextColor.withOpacity(0.6),
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
  );

  static final paramControlInfo = TextStyle(
    color: AppColors.darkTextColor.withOpacity(0.7),
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
  );

  static final activityTitle = TextStyle(
    color: AppColors.darkTextColor,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );

  static final adjustParametersDefaultButton = TextStyle(
    color: AppColors.adjustParametersDefaultButton,
    fontSize: 16.sp,
  );

  static final activityItemTitle = TextStyle(
    color: AppColors.neoBlue,
    fontSize: 12.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
  );

  static final bleSearchTitle = TextStyle(
    color: AppColors.darkTextColor,
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );

  static final bleSearchDesc = TextStyle(
    color: AppColors.lightTextColor,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );

  static final historyValue = TextStyle(
    color: AppColors.historyPrimary,
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
  );

  static final historyRange = TextStyle(
    color: AppColors.pureWhite,
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
  );
  static final historyCalendarValue = TextStyle(
    color: AppColors.historyCalendarValue,
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
  );

  static final historyCalendarMonth = TextStyle(
    color: AppColors.historyCalendarWeek,
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
  );
  static final historyCalendarWeek = TextStyle(
    color: AppColors.historyCalendarWeek,
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
  );

  static final historyDetailTitle = TextStyle(
    color: AppColors.darkTextColor,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );
  static final historyDetailSubtitle = TextStyle(
    color: AppColors.historyDetailSubtitle,
    fontSize: 10.sp,
  );

  static final stimulationControlTitle = TextStyle(
    color: AppColors.stimulationControlTitle,
    fontSize: 18.sp,
    fontWeight: FontWeight.bold,
  );
  static final stimulationControlBattery = TextStyle(
    color: AppColors.stimulationControlTitle,
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
  );
}
