import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:vagustimpro/core/app_config/app_strings.dart';
import 'package:vagustimpro/core/constants/flavor_constants.dart';

import '../../app/flavor/flavor_config.dart';

abstract final class AppColors {
  static final bundleId =
      GetIt.instance.get<FlavorConfig>(instanceName: AppStrings.flavorInstanceName).bundleId;

  static const primaryColor = Color(0xFFFAFAFA);

  static const pauseButtonColor = Color(0xFFEADA89);

  static const stopButtonColor = Color.fromARGB(255, 188, 20, 20);

  static const neoStopButtonColor = Color(0xFFC50306);

  static const startButtonColor = Color(0xFF06CEA9);

  static const greyButtonColor = Color(0xFFB4B4B4);

  static const darkTextColor = Color(0xFF374151);
  static const lightTextColor = Color(0xFF6B7280);
  static const lightGray = Color(0xFFC9C9C9);

  static const disabledButtonColor = Color(0xFFD3D7E2);
  static const disabledTextColor = Color(0xFF3F3C3C);

  static const navigationBarHeaderColor = Color(0xFF374151);

  static const extraLightBlue = Color(0xFFD2DBFF);

  static const extraLightGray = Color(0xFFF3F3F3);
  static const pureWhite = Color(0xFFFFFFFF);

  static const vagustimBlue = Color(0xFF195082);

  static const buttonRed = Color.fromRGBO(250, 104, 124, 1);

  static const buttonBlue = Color(0xFF0073F7);
  static const buttonGreen = Color(0xFF06CEA9);
  static const textInputBorderColor = Color(0xFF707070);
  static const lightGrey = Color(0xFFE3E3E3);
  static const blueTextColor = Color(0xFF0084C4);
  static const darkerTextColor = Color(0xFF3F3F3F);
  static const redTextColor = Color(0xFFD85E7A);
  static const darkText = Color(0xFF141414);
  static const activeSwitchColor = Color(0xFF3EE267);
  static const disabledSwitchColor = Color(0xFFC8C8C8);
  static const darkBlueBox = Color(0xFF007BF8);
  static const lightBlueBox = Color(0xFFE1F2FF);
  static const bleCircle1 = Color(0xFF7B9AFA);
  static const bleCircle2 = Color(0xFFAEC1FD);
  static const bleCircle3 = Color(0xFFDBE3FE);
  static const controlBackgroundColor = Color(0xFFF4F4F4);
  static const headphoneTextColor = Color(0xFF5C5C5C);
  static const activeDotColor = Color(0xFF8B8B8B);
  static const passiveDotColor = Color(0xFFF0F0F0);
  static const intensityControlBorderColor = Color(0xFFB9B9B9);
  static const intensityControlBackgroundColor = Color(0xFFF8F8F8);
  static const progressColor = Color(0xFF57ABD4);
  static const wifiInputBorderColor = Color(0xFFE5E5E5);
  static const greySelectColor = Color(0xFFEEEEEE);
  static const backgroundColor = Color(0xFFFFFFFF);
  static const succesColor = Colors.green;
  static const errorColor = Colors.red; // Select Session Type Page.
  static const selectSessionTypeCardBackgroundColor = Color(0xFFF2F2F2);
  static const selectSessionTypeNewCardBackgroundColor = Color(0xFF38239A);

  static const selectSessionTypeCardTitleColor = Color(0xFF2C2B49);

  static const selectSessionTypeCardInfoColor = Color(0xFF211E41);
  static const selectSessionTypeCardDescriptionColor = Color(0xFF211E41);
  static const selectSessionTypeSaveButtonColor = Color.fromARGB(255, 5, 121, 73);
  static const selectSessionTypeDefaultParametersButtonColor =
      Color(0xFF3259BC); // App text form field.

  static const appTextFormFieldBorderColor = Color(0xFFEDEDEA);
  static const appTextFormFieldBackgroundColor = Color(0xFFF3F3F3);

  static const appTextNewFormFieldBackgroundColor = Colors.white;
  static const appTextNewFormFieldBorderColor = Color(0xFFD8DADC);
  static const appTextFormFieldDisabledBackgroundColor = Color(0xFFE0E0E0);
  static const appTextFormFieldDisabledTextColor = Color(0xFF9E9E9E);
  static const appTextFormFieldLabelColor = Color.fromRGBO(32, 32, 32, 1);
  static const appTextFormFieldRequiredColor = Color(0xFFDF0000);

  static const appTextFormFieldUnderlineColor = Color.fromRGBO(168, 168, 168, 1);
  static const appTextFormFieldHintColor = Color.fromRGBO(71, 67, 95, 1);
  static const cupertinoPickerBackgroundColor = Colors.white; // Survey Page.
  static const surveySelectedOptionCheckColor = Color.fromRGBO(73, 124, 44, 1);

  static const surveyAppBarTitleColor = Color(0xFF524B6B);

  static const surveyProgressBarActiveColor = Color(0xFF3259BC);
  static const surveyOptionBackgroundColor = Color(0xFF717171);
  static const surveySelectedOptionColor = Color(0xFF3259BC);
  static const surveySelectedOptionTextColor = Colors.white;
  static const surveyFloatingActionButtonColor = Color(0xFF3259BC);
  static const referralSettingsTileColor = Color(0xFF65558F); // Rate Us!
  static const starColor = Color(0xFFFFD700);

  static const starOffColor = Colors.grey; // App Dialog.

  static const dialogCloseIconColor = Color(0xFFCAC4D0);
  static const dialogDescriptionStyleColor = Color(0xFF49454F);

  static const dialogOkButtonColor = Color(0xFF3259BC);
  static const commonButtonWithBorderColor = Color(0xFF74787E);
  static const bottomBarSelectedItemBackgroundColor = Color(0xFFD2E1FB);

  static const bottomBarBackgroundColor = Color(0xFF507EA4);

  static const bottomBarSelectedItemColor = Color(0xFF45454F);
  static const bottomBarSelectedTextStyleColor = Colors.black; // Insight View.
  static const insightPageChartGradientColorFirst = Color(0xFFADB7F9);
  static const insightPageChartGradientColorSecond = Color(0xFFB1B9F8);

  static const insightPageChartLineColor = Color(0xFF001DFF);
  static const insightPageChartVerticalLineColor = Color(0xFFB1B1B1);
  static const insightPageChartTopItemStyleColor = Color(0xFF939393);
  static const insightCardBackgroundColor = Colors.white;
  static const insightPageCircleProgressBackgroundColor = Color(0xFFE9EDF0);
  static const insightPageCircleProgressColor = Color(0xFF4B62FF);
  static const insightPageConnectButtonBorderColor = Color(0xFF74767E);
  static const stimulationControlWidgetTutorialTextColor = Colors.white;
  static const stimulationControlWidgetWalktroughTutorialTextColor = Color(0xFF374151);

  static const headPhoneControlCardBorderColor = Color(0xFFC4C7D0);
  static final headPhoneControlCardBackgroundColor = const Color(0xFFE3EDF7);
  static const headPhoneControlBorderColor = Color(0xFFE3EDF7);
  static const headPhoneControlGradientStart = Color(0xFFE8F0F8);

  static const headPhoneControlIncrementButtonBackgroundColor = Colors.white;
  static const headPhoneControlIncrementButtonColor = Color(0xFF1C57B7);
  static const remainingTimeTextColor = Color(0xFF858992);
  static const stimulationControlPauseColor = Color(0xFF989898);
  static const controlPanelBackgroundColor = Color.fromRGBO(243, 243, 243, 1);
  static const controlPanelInContainerBackgroundColor = Colors.white;
  static const controlPanelStartButton = Color(0xFF158908);
  static const controlPanelAdjustParametersButtonBorderColor = Color.fromRGBO(125, 120, 156, 1);
  static const controlPanelAdjustParametersButtonTextColor = Color(0xFF090909);
  static const headPhoneControlBarSuccesColor = Colors.green;
  static const headPhoneControlBarErrorColor = Colors.redAccent;
  static const headPhoneControlBarBetweenColor = Colors.yellow;
  static const controlPanelPauseButtonColor = Color(0xFFEDC900);
  static const stimulationControlTutorialSkipButtonBackgroundColor = Color(0xFFD2E1FB);
  static const tutorialTitleTextColor = Color(0xFF8CA5FF);

  static const stimulationControlTutorialSkipButtonIconColor = Color(0xFF1D192B);
  static const healthDataGraphicColor = Color(0xFF2F80ED);

  static const healthDataGraphicTextColor = Color(0xFF2E2E30);

  static const healthDataGraphicDescriptionColor = Color(0xFF79797F);
  static const healthDataGraphicInfoColor = Color(0xFF404040);
  static const surveyCompletedDescriptionTextColor = Color(0xFF404040);
  static const healthDataHeartRateIconColor = Color(0xFFD30000);
  static const healthDataAverageTextColor = Color(0xFF161626);
  static const healthDataGraphicBarColor = Color(0xFF2C4CF5);
  static const insightPageOverallWellnessScoreTextColor = Color(0xFF0A84FF);
  static const insightPageScoreRangeLow = Color(0xFFF96565);
  static const insightPageScoreRangeModerate = Color(0xFFFFA162);
  static const insightPageScoreRangeAverage = Color(0xFFFFDA55);
  static const insightPageScoreRangeGood = Color(0xFFA8DF6F);
  static const insightPageScoreRangeExcellent = Color(0xFF5FAC87);
  static const insightGetPremiumAcesButtonColor = Color(0xFF171717);
  static const insightGetPremiumAcesButtonIconColor = Colors.white;

  static const insightSummaryDesc = Color(0xFF525252);
  static const insightGetPremiumAccessNowButtonGradient = LinearGradient(colors: [
    Color(0xFF5BB1EE),
    Color(0xFF517ACC),
    Color(0xFF4952B3),
    Color(0xFF5C68DC),
  ]);

  static const profileIconColor = Color(0xFF636366);

  static const profileTileBackgroundColor = Colors.white;

  static const profileTextColor = Color(0xFF424242);
  static const profileTitle = Color(0xFF3C3C43);
  static const profileDivider = Color(0xFF181818);
  static const timePickerTextColor = Colors.black;
  static const headPhoneLevelColor = Color(0xFF999999);
  static const insightMyHistoryColor = Color(0xFF191D30);
  static const insightMyHistoryDescColor = Color(0xFF8C8E97);

  static const insightMyHistoryIconColor = Color(0xFFC7C7CC);
  static const individualHomeHeaderGradient = LinearGradient(colors: [
    Color(0xFF0C0D5B),
    Color(0xFF6338D9),
  ]);
  static const individualHomeHeaderTitle = Colors.white;

  static const individualHomeStoryTitle = Color(0xFF524B6B);
  static const individualHomeStoryDesc = Color(0xFF2E2C2C);
  static const individualHomeStoryBorderColor = Color(0xFFBABABA);
  static final individualHomeStoryIndicatorColor = Colors.grey.shade300;
  static const individualHomeStoryBackgroundColor = Colors.black;
  static const individualHomePremiumButtonGradient = LinearGradient(
    begin: Alignment.centerLeft,
    colors: [Color(0xFF517ACC), Color(0xFF4952B3), Color(0xFF5C68DC)],
    end: Alignment.centerRight,
  );
  static const individualHomeConnectButtonSuccesColor = Colors.green;
  static const vagusCoachTitle = Color(0xFF112950);

  static const vagusCoachDesc = Color(0xFF394D6D);

  static const vagusCoachMailColor = Color(0xFF007AFF);
  static const vagusCoachBackgroundColor = Colors.white;
  static const advanceDeviceControlLabelColor = Color(0xFF111928);
  static const appleButtonBackgroundColor = Colors.black;

  static const appleButtonTextColor = Colors.white;

  static const disableButtonBackgroundColor = Color(0xFFE7E8ED);
  static const disableButtonIconColor = Color(0xFF9A9BA0);

  static const remoteMonitoringTimeColor = Color(0xFF8E9AAB);
  static const remoteMonitoringDisconnectColor = Colors.blueGrey;

  static const hrvBpm = Colors.white;
  static const hrvProgressBarColor = Color(0xFFEB4B62);

  static final hrvProgressBarBackgroundColor = Colors.grey[200];
  static const hrvResultTitle = Color(0xFFA3A3A3);
  static const hrvResultDesc = Color(0xFF404040);

  static const multipleDeviceTitle = Color(0xFF524B6B);
  static const multipleDeviceBorder = Color(0xFF171717);
  static const multipleDeviceItemBackgroundColor = Colors.white;
  static const multipleDeviceItemBorderColor = Colors.black;
  static const multipleDeviceItemName = Color(0xFF000D09);
  static const multipleDeviceItemConnectButtonBackground = Color(0xFF007AFF);
  static const multipleDeviceItemOngoingButtonBackground = Color(0xFF1A932E);

  static const multipleDeviceItemPausedButtonBackground = Color(0xFFDFA510);
  static const multipleDeviceItemNotStartedButtonBackground = Color(0xFFEE201C);
  static const multipleDeviceItemDetailField = Color(0xFF3C3C43);
  static const multipleDeviceItemPersonIconColor = Color(0xFF8E9AAB);
  static final multipleDeviceItemCompletedButtonBackground = Color(0xFF0A4514);
  static const multipleDevicesTitle = Color(0xFF45454F);
  static const multipleDevicesStatusInfoBackground = Color(0xFF000000);
  static const multipleDevicesStatusInfoShadow = Color(0xFFFFFFFF);

  static const bpmLow = Color(0xFF3259BC);
  static const bpmNormal = Color(0xFF0C9D30);
  static const bmpHigh = Color(0xFFD80E03);

  static const hrvLowScore = Color(0xFFFFB806);
  static const hrvNormalScore = Color(0xFF00B92F);
  static const hrvGoodScore = Color(0xFF0D6017);

  static const hrvItemText = Colors.white;
  static const hrvTitle = Color(0xFF2E2E30);
  static const hrvStartCamera = Color(0xFF010101);
  static const hrvIcon = Color(0xFFEB4B62);
  static const hrvBottomBar = Colors.white;
  static const hrvBottomBarUnselected = Color(0xFFA5A5A5);

  static const customRedGradient = LinearGradient(
    colors: [
      Color(0xFFFF8A9B),
      Color(0xFFEB4B62),
      Color(0xFFE13758),
      Color(0xFFD72D4E),
    ],
  );

  static const hrvProfileText = Color(0xFF374151);
  static const hrvProfileTabSelected = Colors.white;
  static const hrvProfileTabUnSelected = Color(0xFF010101);
  static final hrvChartRod = Color(0xFFE6E6E6).withValues(alpha: 0.8);
  static const otpTitle = Color(0xFF4D3E3E);

  static const profileFormItemBackground = Color(0xFFEDEDEA);
  static const profileFormNonItemSelect = Color(0xFFF3F3F3);
  static const profileFormItemText = Color(0xFF524B6B);
  static const profileFormOtherBackground = Color(0xFFE5E7ED);
  static const profileFormOtherIcon = Colors.white;
  static final countryPickerBorder = Colors.grey.shade300;

  static const aiDrivenSkipButton = Color(0xFF6B7280);

  static const aiDrivenBgColor = Color(0xFFF8F7FF);
  static const aiDrivenIconBgColor = Color(0xFF6B5ECD);
  static const presetProgramsBgColor = Color(0xFFF0FFFE);
  static const presetProgramsIconBgColor = Color(0xFF14B8A6);
  static const adjustParametersBgColor = Color(0xFFFFF5F5);
  static const adjustParametersIconBgColor = Color(0xFFEF4444);

  static const sessionTypeTitle = Color(0xFF1A1A1A);
  static const sessionTypeDescription = Color(0xFF6B7280);
  static const sessionTypeDefaultIconBackgroundColor = Color(0xFF6B5ECD);
  static const sessionTypeGreyColor = Color(0xFFF3F4F6);
  static const sessionTypePremiumGradientStart = Color(0xFFFFB800);
  static const sessionTypePremiumGradientEnd = Color(0xFFFF8A00);
  static const sessionTypeComingSoonColor = Color(0xFF6B7280);

  // Parameter control widget colors.
  static const paramFrequencyColor = Color(0xFF3259BC);

  static const paramPulseWidthColor = Color(0xFFFF8A00);

  static const paramDurationColor = Color(0xFF06CEA9);

  static const activityCircleAvatarBackgroundColor = Color(0xFFF7F7F7);

  static const paramOnOffColor = Color(0xFF5C68DC);
  static const paramControlTitle = Color(0xFF68696B);

  static const adjustParametersDefaultButton = Color(0xFF0F77F0);

  static const bottomBarFabButtonShadow = Color(0xFF95D0F8);

  static const activityBorderLinear = LinearGradient(
    begin: Alignment.centerLeft,
    colors: [
      Color(0xFFC2E7FF),
      Color(0xFF1075BA),
      Color(0xFF3259BC),
      Color(0xFF4971D9),
      Color(0xFF3A26B5),
    ],
    end: Alignment.centerRight,
    stops: [0.02, 0.5, 0.7, 0.85, 1.0],
  );
  static final activityItemBackground = Colors.grey.shade600;
  static const stimulationControlBtn = Color(0xFF3259BC);
  static const stimulationControlBtnDisable = Color(0xFFAEC4FF);
  static const stimulationControlTitle = Color(0xFF2E2E30);

  static const historyFaded = Color(0xFFF1F2FF);
  static const historyPrimary = Color(0xFF5865F2);
  static const historyCircleBackground = Color(0xFF62D0A2);
  static const historyCalendarValue = Color(0xFF858585);
  static const historyCalendarWeek = Color(0xFF121212);
  static const historyCardBackground = Color(0xFFFAFAFC);
  static const historyDetailSubtitle = Color(0xFF8D8D8D);
  static const historyDetailIcon = Color(0xFF001F4D);

  // Flavor specific colors.
  static const hrvPrimary = 0xFFEB4B62;
  static const vagustimPrimary = 0xFF007BF4;
  static const hrvProfileUnSelected = 0xFFFFD6DC;
  static const vagustimProfileUnSelected = 0xFFB4BCCF;
  static const vagustimNeoBlue = 0xFF3259BC;

  static get loaderColor =>
      FlavorConstants.isHrv(bundleId) ? Color(hrvPrimary) : Color(vagustimPrimary);
  static get profileBarUnSelected => FlavorConstants.isHrv(bundleId)
      ? Color(hrvProfileUnSelected)
      : Color(vagustimProfileUnSelected);

  static Color get neoBlue =>
      FlavorConstants.isHrv(bundleId) ? Color(hrvPrimary) : Color(vagustimNeoBlue);
}
