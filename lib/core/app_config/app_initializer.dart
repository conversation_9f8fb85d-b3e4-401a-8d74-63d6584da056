// ignore_for_file: avoid-global-state

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vagustimpro/app/flavor/flavor_config.dart';
import 'package:vagustimpro/app/vagustim_pro_app.dart';
import 'package:vagustimpro/core/app_config/network_constants.dart';
import 'package:vagustimpro/core/ble/ble_constants.dart';
import 'package:vagustimpro/core/ble/ble_device_connector.dart';
import 'package:vagustimpro/core/ble/ble_device_interactor.dart';
import 'package:vagustimpro/core/ble/ble_logger.dart';
import 'package:vagustimpro/core/ble/ble_scanner.dart';
import 'package:vagustimpro/core/ble/ble_service_bundle.dart';
import 'package:vagustimpro/core/ble/ble_status_monitor.dart';
import 'package:vagustimpro/core/ble/models/ble_scanner_state.dart';
import 'package:vagustimpro/core/constants/app_constants.dart';
import 'package:vagustimpro/features/app_permission/presentation/bloc/app_permission_bloc.dart';
import 'package:vagustimpro/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:vagustimpro/features/landing/presentation/bloc/landing_bloc.dart';
import 'package:vagustimpro/features/measurement/presentation/bloc/measurement_bloc.dart';
import 'package:vagustimpro/features/ota/presentation/bloc/ota_bloc.dart';
import 'package:vagustimpro/features/referral/presentation/bloc/referral_bloc.dart';
import 'package:vagustimpro/features/settings/presentation/bloc/settings_bloc.dart';
import 'package:vagustimpro/features/stimulation/presentation/bloc/stimulation_bloc.dart';
import 'package:vagustimpro/features/subuser/presentation/bloc/subuser_bloc.dart';
import 'package:vagustimpro/features/survey/presentation/bloc/survey_bloc.dart';
import 'package:vagustimpro/features/time_of_usage/presentation/bloc/time_of_usage_bloc.dart';
import 'package:vagustimpro/init_dependencies.dart';

import '../../features/daily_reminder/presentation/bloc/daily_reminder_bloc.dart';
import '../../features/parameter/presentation/new_bloc/new_parameter_bloc.dart';
import '../cache/app_cache.dart';
import '../helpers/analytic_helper.dart';
import '../init/init_onesignal.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

abstract final class AppInitializer {
  static BleServiceBundle? bleServices;
  static get initScreenKey => 'initScreen';
  const AppInitializer._();

  static Future<void> initializeApp({required FlavorConfig flavorConfig}) async {
    final widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
    FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

    final FlavorConfig(
      :appName,
      :appTitle,
      :baseUrl,
      :bundleId,
      :envFileName,
      :isDebugShowCheckedModeBanner,
      :theme,
    ) = flavorConfig;

    await dotenv.load(fileName: envFileName);

    NetworkConstants.setBaseUrl(baseUrl ?? "");
    NetworkConstants.setAppName(appName ?? "");

    await initializePlatformSpecificConfigs();
    final bleService = setupBleServices();
    bleServices = bleService;

    await initDependencies(bleService, flavorConfig);

    InitOnesignal.initializeOneSignal();
    await AnalyticHelper.instance.initMixpanel();

    await configureSDK();
    final preferencesInstance = await SharedPreferences.getInstance();
    await BaseCache.instance.init();

    final initScreen = preferencesInstance.getInt(initScreenKey);

    if (kDebugMode) {
      runApp(_buildApp(bleService, flavorConfig, initScreen));
    } else {
      await SentryFlutter.init(
        (options) => _sentryInitOption(options),
        appRunner: () => runApp(_buildApp(bleService, flavorConfig, initScreen)),
      );
    }
  }

  static SentryFlutterOptions _sentryInitOption(SentryFlutterOptions options) {
    final sentryOption = options;

    sentryOption.dsn =AppConstants.sentryDns;
    sentryOption.sendDefaultPii = true;

    return options;
  }

  static Widget _buildApp(
    BleServiceBundle bleServices,
    FlavorConfig flavorConfig,
    int? initScreen,
  ) {
    return SentryWidget(
      child: MultiBlocProvider(
        providers: [
          BlocProvider(create: (_) => serviceLocator<AuthBloc>()),
          BlocProvider(create: (_) => serviceLocator<LandingBloc>()),
          BlocProvider(create: (_) => serviceLocator<SubuserBloc>()),
          BlocProvider(create: (_) => serviceLocator<StimulationBloc>()),
          BlocProvider(create: (_) => serviceLocator<NewParameterBloc>()),
          BlocProvider(create: (_) => serviceLocator<MeasurementBloc>()),
          BlocProvider(create: (_) => serviceLocator<AppPermissionBloc>()),
          BlocProvider(create: (_) => serviceLocator<ReferralBloc>()),
          BlocProvider(create: (_) => serviceLocator<SettingsBloc>()),
          BlocProvider(create: (_) => serviceLocator<TimeOfUsageBloc>()),
          BlocProvider(create: (_) => serviceLocator<SurveyBloc>()),
          BlocProvider(create: (_) => serviceLocator<DailyReminderBloc>()),
          BlocProvider(create: (_) => serviceLocator<OtaBloc>()),
        ],
        child: MultiProvider(
          providers: [
            Provider<BleScanner>(create: (_) => bleServices.scanner),
            Provider<BleStatusMonitor>(create: (_) => bleServices.monitor),
            Provider<BleDeviceConnector>(create: (_) => bleServices.connector),
            Provider<BleDeviceInteractor>(create: (_) => bleServices.serviceDiscoverer),
            Provider<BleLogger>(create: (_) => bleServices.bleLogger),
            StreamProvider<BleScannerState?>(
              create: (_) => bleServices.scanner.state,
              initialData: const BleScannerState(
                discoveredDevices: [],
                isScanInProgress: false,
              ),
            ),
            StreamProvider<BleStatus?>(
              create: (_) => bleServices.monitor.state,
              initialData: BleStatus.unknown,
            ),
            StreamProvider<ConnectionStateUpdate>(
              create: (_) => bleServices.connector.state,
              initialData: const ConnectionStateUpdate(
                connectionState: DeviceConnectionState.disconnected,
                deviceId: BleConstants.unknownDeviceId,
                failure: null,
              ),
            ),
          ],
          child: VagustimProApp(
            appTitle: flavorConfig.appTitle,
            initScreen: initScreen,
            isDebugShowCheckedModeBanner: flavorConfig.isDebugShowCheckedModeBanner,
            theme: flavorConfig.theme,
          ),
        ),
      ),
    );
  }
}
