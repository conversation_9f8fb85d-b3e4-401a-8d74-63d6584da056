import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vagustimpro/core/app_config/app_sizes.dart';

final class AppGaps {
  static const gapH4 = SizedBox(height: AppSizes.verySmallSize);
  static const gapH8 = SizedBox(height: AppSizes.smallSize);
  static const gapH12 = SizedBox(height: AppSizes.displaySize);
  static const gapH16 = SizedBox(height: AppSizes.mediumSize);
  static const gapH24 = SizedBox(height: AppSizes.largeSize);
  static const gapH32 = SizedBox(height: AppSizes.extraLargeSize);
  static const gapH50 = SizedBox(height: AppSizes.walkthroughBottom);

  static const gapW4 = SizedBox(width: AppSizes.verySmallSize);
  static const gapW8 = SizedBox(width: AppSizes.smallSize);
  static const gapW12 = SizedBox(width: AppSizes.displaySize);
  static const gapW16 = SizedBox(width: AppSizes.mediumSize);
  static const gapW24 = SizedBox(width: AppSizes.largeSize);
  static const gapW32 = SizedBox(width: AppSizes.extraLargeSize);
  static AppGaps? _instance;

  SizedBox get gapVS16 => AppSizes.mediumSize.verticalSpace;
  SizedBox get gapVS12 => AppSizes.displaySize.verticalSpace;
  SizedBox get gapVS32 => AppSizes.extraLargeSize.verticalSpace;
  SizedBox get gapVS8 => AppSizes.smallSize.verticalSpace;

  SizedBox get gapVS4 => AppSizes.verySmallSize.verticalSpace;
  SizedBox get gapHS8 => AppSizes.smallSize.horizontalSpace;

  SizedBox get gapHS4 => AppSizes.verySmallSize.horizontalSpace;
  SizedBox get gapHS16 => AppSizes.mediumSize.horizontalSpace;
  SizedBox get gapHS12 => AppSizes.displaySize.horizontalSpace;
  static AppGaps get instance => _instance ??= const AppGaps._();

  const AppGaps._();

  static SizedBox gapBottom(BuildContext context) =>
      SizedBox(height: MediaQuery.paddingOf(context).bottom + AppSizes.mediumSize);
}
