abstract final class AppSizes {
  static const verySmallSize = 4.0;
  static const smallSize = 8.0;
  static const displaySize = 12.0;
  static const mediumSize = 16.0;
  static const largeSize = 24.0;
  static const extraLargeSize = 32.0;

  static const iconSmall = largeSize;
  static const iconMedium = extraLargeSize;
  static const iconLarge = 48.0;

  static const buttonHeight = 50.0;
  static const buttonWidth = 200.0;
  static const buttonRadius = displaySize;
  static const buttonMediumRadius = 30.0;

  static const fontSizeSmall = 14.0;
  static const fontSizeMedium = mediumSize;
  static const fontSizeLarge = 18.0;

  static const smallInputFieldHeight = 40.0;
  static const mediumInputFieldHeight = buttonHeight;
  static const largeInputFieldHeight = 60.0;

  static const walkthroughDotWidth = largeSize;
  static const walkthroughDotHeight = verySmallSize;
  static const walkthroughDotRadius = displaySize;
  static const walkthroughDotMargin = verySmallSize;
  static const walkthroughOpacity = 0.2;
  static const walkthroughImageHeight = 423.0;
  static const walkthroughImageWidth = 309.0;
  static const walkthroughBottom = buttonHeight;

  static const logoHeight = 0.3;
  static const logoWidth = 0.6;

  static const footerSpacer = walkthroughOpacity;
  static const defaultSpacer = 0.01;
}
