import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../enum/assets_enums.dart';
import '../enum/assets_enums_lottie.dart';

class MinimalLoader extends StatelessWidget {
  const MinimalLoader({super.key});

  @override
  Widget build(BuildContext context) {
    final scanSize = 26.0;
    final loadingSize = 60.0;
    final containerHeight = 52.0;
    final scanLoadingHeight = loadingSize.sp;
    final scanLoadingWidth = scanLoadingHeight;

    return Center(
      child: SizedBox.square(
        dimension: containerHeight.sp,
        child: Stack(
          children: [
            AssetsEnumsLottie.scanLoading.toLottie(
              height: scanLoadingHeight,
              width: scanLoadingWidth,
            ),
            Positioned.fill(
              child: Center(
                child: AssetsEnums.icScan.toSvg(
                  height: scanSize.h,
                  width: scanSize.w,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
