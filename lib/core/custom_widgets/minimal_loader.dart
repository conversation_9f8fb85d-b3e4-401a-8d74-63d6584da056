import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../enum/assets_enums.dart';
import '../enum/assets_enums_lottie.dart';

class MinimalLoader extends StatelessWidget {
  const MinimalLoader({super.key});

  @override
  Widget build(BuildContext context) {
    final scanSize = 30.0;
    final loadingSize = 60.0;

    return Center(
      child: Stack(
        children: [
          AssetsEnumsLottie.scanLoading.toLottie(
            height: loadingSize.sp,
            width: loadingSize.sp,
          ),
          Positioned.fill(
            child: Center(
              child: AssetsEnums.icScan.toSvg(
                height: scanSize.h,
                width: scanSize.w,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
