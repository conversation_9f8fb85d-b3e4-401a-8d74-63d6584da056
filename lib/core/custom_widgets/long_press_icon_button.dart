// ignore_for_file: avoid-non-null-assertion

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vagustimpro/core/custom_widgets/press_callback.dart';

class LongPressIconButton extends StatefulWidget {
  const LongPressIconButton({
    required this.color,
    required this.icon,
    this.isEnabled = true,
    super.key,
    required this.onPressed,
    required this.size,
  });
  final IconData icon;
  final Color color;
  final double size;
  final PressCallback? onPressed;
  final bool isEnabled;

  @override
  State<LongPressIconButton> createState() => _LongPressIconButtonState();
}

class _LongPressIconButtonState extends State<LongPressIconButton> {
  Timer? _timer;

  void _handlePress() {
    if (widget.isEnabled && widget.onPressed != null) {
      widget.onPressed!();
    }
  }

  void _handleStartTimer() {
    if (widget.isEnabled && widget.onPressed != null) {
      _timer = Timer.periodic(
        const Duration(milliseconds: 100),
        (timer) => widget.onPressed!(),
      );
    }
  }

  void _handleStopTimer() {
    _timer?.cancel();
    _timer = null;
  }

  @override
  void dispose() {
    _timer?.cancel();
    _timer = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPressEnd: (details) => _handleStopTimer(),
      onLongPressStart: (details) => _handleStartTimer(),
      onTap: _handlePress,
      child: Icon(
        widget.icon,
        color: widget.isEnabled ? widget.color : Colors.grey,
        size: widget.size,
      ),
    );
  }
}
