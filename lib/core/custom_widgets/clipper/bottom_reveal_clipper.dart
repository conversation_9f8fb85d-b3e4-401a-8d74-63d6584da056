// ignore_for_file: format-comment
import 'package:flutter/material.dart';

class BottomRevealClipper extends CustomClipper<Path> {
  final double revealFactor;
  const BottomRevealClipper(this.revealFactor);

  @override
  Path getClip(Size size) {
    final visibleHeight = size.height * revealFactor;

    final rect = Rect.fromLTWH(
      0,
      size.height - visibleHeight,
      size.width,
      visibleHeight,
    );

    return Path()..addRect(rect);
  }

  @override
  bool shouldReclip(covariant BottomRevealClipper oldClipper) {
    return oldClipper.revealFactor != revealFactor;
  }
}
