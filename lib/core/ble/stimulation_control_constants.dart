abstract final class StimulationControlConstants {
  // Command types for stimulation control.
  static const startCommand = _startValue;
  static const stopCommand = _stopValue;
  static const pauseCommand = _pauseValue;

  // Device status values.
  static const pauseStatus = _pauseValue;
  static const stopStatus = _stopValue;

  // Device status string literals used in UI comparisons.
  static const deviceStatusPause = _pauseValue;
  static const deviceStatusStop = _stopValue;

  // Dialog messages and titles.
  static const parameterNotFoundTitle = 'Parameter Not Found';
  static const parameterNotFoundDescription =
      'Session type parameter not found. Please choose a different session type to proceed.'; // Intensity thresholds and adjustments.

  static const lowIntensityThreshold = 150.0;
  static const mediumIntensityThreshold = 300.0;
  static const lowIntensityIncrement = 30.0;

  static const mediumIntensityIncrement = 10.0;
  static const highIntensityIncrement = 5.0;

  static const minIntensity = _zeroValue;
  static const maxIntensity = 1000.0;

  // Progress reset values.
  static const resetProgressValue = _zeroValue;

  // User type constants.
  static const individualsUserType = 'individuals';

  // Device name constants.
  static const incorrectDeviceMessage = "Incorrect Product";

  // Permission names.
  static const currentIntensityPermission = "current_intensity";

  // Magic numbers for UI layout.
  static const lottieHeight = 60.0;
  static const heightMultiplier = 0.7;
  static const headphoneControlHeightMultiplier = 0.44;

  // Headphone control specific constants.
  static const totalSteps = 1000;
  static const stepPerIndicator = _tenValue;
  static const progressBarSize = _tenValue;
  static const progressBarImageBottomValue = _progressBarImagePositionValue;
  static const progressBarImageLeftValue = _progressBarImagePositionValue;
  static const progressBarImageRightValue = _progressBarImagePositionValue;
  static const progressBarImageHeight = 0.4;
  static const progressBarImageWidth = 16;
  static const progressBarClipperWidth = _tenValue;
  static const buttonHeight = 44.0;
  static const iconSize = 28.0;
  static const spacingWidth = _spacingWidthValue;

  // Empty string defaults.
  static const emptyString = _emptyStringValue;

  static const defaultDeviceName = _emptyStringValue;

  // UI text constants.
  static const intensityControlTitle = 'Intensity Control';
  static const earTextPrefix = ' ';
  static const fullOpacity = 1.0;

  static const reducedOpacity = 0.8;
  static const hiddenOpacity = 0.0;
  static const fullHeightFactor = 1.0;
  static const hiddenHeightFactor = 0.0;
  static const flexValue = 1;
  static const spacingValue = 0.0;
  static const _pauseValue = 'pause';
  static const _stopValue = 'stop';
  static const _startValue = 'start';

  static const _zeroValue = 0.0;
  static const _emptyStringValue = '';
  static const _tenValue = 10;
  static const _spacingWidthValue = _tenValue;
  static const _progressBarImagePositionValue = 4;
}
