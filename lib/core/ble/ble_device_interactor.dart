import 'dart:convert';

import 'package:dart_code_metrics_annotations/annotations.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:vagustimpro/core/ble/ble_logger.dart';

typedef BleDiscoverServices = Future<List<DiscoveredService>> Function(String deviceId);
typedef ReadCharacteristic = Future<List<int>> Function(QualifiedCharacteristic characteristic);
typedef WriteCharacteristic = Future<void> Function(
  QualifiedCharacteristic characteristic, {
  required List<int> value,
});
typedef SubscribeToCharacteristic = Stream<List<int>> Function(
  QualifiedCharacteristic characteristic,
);

class BleDeviceInteractor {
  static const discoveringServicesEndMessage = 'Discovering services finished';
  static const readCharacteristicMessage = 'Read characteristic value';
  static const writeWithResponseMessage = 'Write with response value';
  static const writeWithoutResponseMessage = 'Write without response value';
  static const subscribingMessage = 'Subscribing to: ';

  final BleDiscoverServices onBleDiscoverServices;
  final ReadCharacteristic onReadCharacteristic;
  final WriteCharacteristic onWriteWithResponse;
  final WriteCharacteristic onWriteWithoutResponse;
  final SubscribeToCharacteristic onSubscribeToCharacteristic;
  final LogMessage onLogMessage;

  const BleDeviceInteractor({
    required BleDiscoverServices bleDiscoverServices,
    required LogMessage logMessage,
    required ReadCharacteristic readCharacteristic,
    required SubscribeToCharacteristic subscribeToCharacteristic,
    required WriteCharacteristic writeWithOutResponse,
    required WriteCharacteristic writeWithResponse,
  })  : onBleDiscoverServices = bleDiscoverServices,
        onReadCharacteristic = readCharacteristic,
        onWriteWithResponse = writeWithResponse,
        onWriteWithoutResponse = writeWithOutResponse,
        onSubscribeToCharacteristic = subscribeToCharacteristic,
        onLogMessage = logMessage;

  @Throws()
  Future<List<DiscoveredService>> discoverServices(String deviceId) async {
    try {
      final discoveringServicesStartMessage = 'Start discovering services for: $deviceId';

      onLogMessage(discoveringServicesStartMessage);
      final services = await onBleDiscoverServices(deviceId);
      onLogMessage(discoveringServicesEndMessage);

      return services;
    } on Exception catch (exception) {
      final errorMessage = 'Error occurred when discovering services: $exception';
      onLogMessage(errorMessage);
      rethrow;
    }
  }

  @Throws()
  Future<List<int>> readCharacteristic({
    required QualifiedCharacteristic characteristic,
  }) async {
    try {
      final result = await onReadCharacteristic(characteristic);
      final message =
          '$readCharacteristicMessage ${characteristic.characteristicId}: value = ${utf8.decode(result)}';
      onLogMessage(message);

      return result;
    } on Exception catch (error, s) {
      final errorMessage =
          'Error occurred when reading ${characteristic.characteristicId} : $error';
      onLogMessage(errorMessage);
      if (kDebugMode) {
        print(s);
      }
      rethrow;
    }
  }

  @Throws()
  Future<void> writeCharacteristicWithResponse({
    required QualifiedCharacteristic characteristic,
    required List<int> value,
  }) async {
    try {
      final message =
          '$writeWithResponseMessage : ${utf8.decode(value)} to ${characteristic.characteristicId}';
      onLogMessage(message);
      await onWriteWithResponse(characteristic, value: value);
    } on Exception catch (error, s) {
      final errorMessage =
          'Error occurred when writing ${characteristic.characteristicId} : $error';
      onLogMessage(errorMessage);
      if (kDebugMode) {
        print(s);
      }
      rethrow;
    }
  }

  @Throws()
  Future<void> writeCharacteristicWithoutResponse({
    required QualifiedCharacteristic characteristic,
    required List<int> value,
  }) async {
    try {
      await onWriteWithoutResponse(characteristic, value: value);
      final message = '$writeWithoutResponseMessage: $value to ${characteristic.characteristicId}';
      onLogMessage(message);
    } on Exception catch (error, s) {
      final errorMessage =
          'Error occurred when writing ${characteristic.characteristicId} : $error';
      onLogMessage(errorMessage);
      if (kDebugMode) {
        print(s);
      }
      rethrow;
    }
  }

  Stream<List<int>> subscribeToCharacteristic(
    QualifiedCharacteristic characteristic,
  ) {
    final message = '$subscribingMessage ${characteristic.characteristicId}';
    onLogMessage(message);

    return onSubscribeToCharacteristic(characteristic);
  }
}
