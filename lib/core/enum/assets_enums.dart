// ignore_for_file: no-magic-string

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

enum AssetsEnums {
  adjustParameters("adjust_parameters"),
  adjustParams("adjust_params"),
  aiDrivenCalculate("ai_driven_calculate"),
  aiDrivenHeartRate("ai_driven_heart_rate"),
  aiDrivenSurvey("ai_driven_survey"),
  comingSoonContainer("coming_soon_container"),
  comingSoonLoader("coming_soon_loader"),
  comingSoonRocket("coming_soon_rocket"),
  completeSurvey("complete_survey"),
  consultation("consultation"),
  crown("crown"),
  customizeWithAi("customize_with_ai"),
  deviceControlTutorial("device_control_tutorial"),
  deviceImage("device_image"),
  graphicSleep("graphic_sleep"),
  homeHeaderBackground("home_header_background"),
  homeStoryTexture("home_story_texture"),
  hrvCalculating("hrv_calculating"),
  hrvCameraWithFinger("hrv_camera_with_finger"),
  hrvInsights("hrv_insights"),
  hrvLogo("hrv_logo"),
  hrvNoData("hrv_no_data"),
  hrvOtpImage("hrv_otp_image"),
  hrvProfileLoadingHeader("hrv_profile_loading_header"),
  hrvSplashIcon("hrv_splash_icon"),
  hrvToCameraWithFingersHalf("hrv_two_camera_with_fingers_half"),
  icAdjustYourParameters("ic_adjust_your_parameters"),
  icAiDriven("ic_ai_driven"),
  icAppleWatch("ic_apple_watch"),
  icBellGreen("ic_bell_green"),
  icBellRed("ic_bell_red"),
  icBellYellow("ic_bell_yellow"),
  icClock("ic_clock"),
  icDashboard("ic_dashboard"),
  icDelete("ic_delete"),
  icDeviceControl("ic_device_control"),
  icDevices("ic_devices"),
  icFrequency("ic_frequency"),
  icHeart("ic_heart"),
  icHelp("ic_help"),
  icHistory("ic_history"),
  icHome("ic_home"),
  icHrvProfile("ic_hrv_profile"),
  icInsight("ic_insight"),
  icNotFound("ic_not_found"),
  icOffDuration("ic_off_duration"),
  icPersons("ic_persons"),
  icPlay("ic_play"),
  icPower("ic_power"),
  icPremium("ic_premium"),
  icPresetCognitivePerformance("ic_preset_cognitive_performance"),
  icPresetEaseTension("ic_preset_ease_tension"),
  icPresetHealth("ic_preset_health"),
  icPresetOverall("ic_preset_overall"),
  icPresetPrograms("ic_preset_programs"),
  icPresetRecovery("ic_preset_recovery"),
  icPresetRelaxation("ic_preset_relaxation"),
  icPresetSleep("ic_preset_sleep"),
  icPresetStress("ic_preset_stress"),
  icProfile("ic_profile"),
  icPulseWidth("ic_pulsewidth"),
  icSLogo("ic_s_logo"),
  icSad("ic_sad"),
  icScan("ic_scan"),
  icSleep("ic_sleep"),
  icSmile("ic_smile"),
  icSmileOutlined("ic_smile_outlined"),
  icSummary("ic_summary"),
  icSurvey("ic_survey"),
  icSurveyFillOut("ic_survey_fill_out"),
  icTime("ic_time"),
  icVagustimProfessional("ic_vagustim_professional"),
  icVagustimWithRed("ic_vagustim_with_red"),
  insightGraphic("insight_graphic"),
  iphone13Mockup("iphone_13_mockup"),
  iphone13MockupWithFinger("iphone_13_mockup_with_finger"),
  leftEar("left_ear"),
  leftEarProgress("left_ear_progress"),
  otpImage("otp_image"),
  overallWellnessScore("ic_overall_wellness_score"),
  presetWaveBackground("preset_wave_background"),
  presetWaveForeground("preset_wave_foreground"),
  question("question"),
  quickStart("quick_start"),
  referral("referral"),
  remoteMonitoringWalktrough("remote_monitoring_walktrough"),
  rightEar("right_ear"),
  splashIcon("splash_icon"),
  story1("story_1"),
  surveyCalcuate("survey_calculate"),
  tutorialImageBackground("tutorial_image_background"),
  twoCameraWithFingers("hrv_two_camera_with_fingers"),
  whereToFindDoctorId("where_to_find_doctor_id");

  const AssetsEnums(this.value);
  final String value;

  final baseSvgPath = "assets/images/svg/";
  final basePngPath = "assets/images/";
  final baseHrvPath = "assets/images/hrv_insight/";
  final baseGifPath = "assets/gifs/";

  String toGifPath(String gifValue) {
    return "$baseGifPath$gifValue.gif";
  }

  String toPngPath(String pngValue) {
    return "$basePngPath$pngValue.png";
  }

  String get assetPng => "$basePngPath$value.png";
  String get assetSvg => "$baseSvgPath$value.svg";
  String get assetGif => "$basePngPath$value.gif";
  String get assetHrvPng => "$baseHrvPath$value.png";

  Widget toPng({Color? color, BoxFit? fit, double? height, double? width}) {
    return Image.asset(
      assetPng,
      color: color,
      fit: fit,
      height: height,
      semanticLabel: value,
      width: width,
    );
  }

  Widget toPngWithHrv({Color? color, BoxFit? fit, double? height, double? width}) {
    return Image.asset(
      assetHrvPng,
      color: color,
      fit: fit,
      height: height,
      semanticLabel: value,
      width: width,
    );
  }

  Widget toSvg({Color? color, double? height, double? width}) {
    return SvgPicture.asset(
      assetSvg,
      colorFilter: color == null ? null : ColorFilter.mode(color, BlendMode.srcIn),
      height: height,
      width: width,
    );
  }

  AssetImage toPngAssetsImage() {
    return AssetImage(assetPng);
  }
}
